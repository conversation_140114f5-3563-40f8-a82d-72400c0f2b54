#!/usr/bin/env node

/**
 * HLenergy Performance and Memory Leak Analysis
 * 
 * This script performs comprehensive performance analysis including:
 * - Bundle size analysis
 * - Memory leak detection
 * - Performance metrics collection
 * - Socket.io connection monitoring
 * - PWA performance analysis
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

const success = (message) => log(`✅ ${message}`, 'green')
const warning = (message) => log(`⚠️  ${message}`, 'yellow')
const error = (message) => log(`❌ ${message}`, 'red')
const info = (message) => log(`ℹ️  ${message}`, 'blue')

// Results tracking
const results = {
  bundleAnalysis: {},
  memoryLeaks: [],
  performanceIssues: [],
  recommendations: []
}

/**
 * Install required dependencies for analysis
 */
const installDependencies = () => {
  log('\n📦 Installing analysis dependencies...', 'bright')
  
  const dependencies = [
    'webpack-bundle-analyzer',
    'lighthouse',
    'clinic',
    'autocannon'
  ]
  
  try {
    // Check if dependencies are already installed
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    const devDeps = packageJson.devDependencies || {}
    
    const missing = dependencies.filter(dep => !devDeps[dep])
    
    if (missing.length > 0) {
      info(`Installing missing dependencies: ${missing.join(', ')}`)
      execSync(`npm install --save-dev ${missing.join(' ')}`, { stdio: 'inherit' })
    } else {
      success('All analysis dependencies are already installed')
    }
  } catch (err) {
    warning('Could not install all dependencies automatically')
    info('Please install manually: npm install --save-dev webpack-bundle-analyzer lighthouse clinic autocannon')
  }
}

/**
 * Analyze bundle size and composition
 */
const analyzeBundleSize = () => {
  log('\n📊 Analyzing Bundle Size...', 'bright')
  
  try {
    // Build the project first
    info('Building project for analysis...')
    execSync('npm run build', { stdio: 'pipe' })
    
    // Check if dist directory exists
    const distPath = 'frontend/dist'
    if (!fs.existsSync(distPath)) {
      error('Build directory not found. Please run npm run build first.')
      return
    }
    
    // Analyze bundle size
    const bundleSize = execSync(`du -sh ${distPath}`).toString().trim()
    const sizeMatch = bundleSize.match(/(\d+(?:\.\d+)?[KMG]?)/)
    
    if (sizeMatch) {
      const size = sizeMatch[1]
      results.bundleAnalysis.totalSize = size
      
      // Check if size is reasonable (< 5MB for initial load)
      const sizeInMB = parseFloat(size.replace(/[KMG]/, '')) * 
        (size.includes('G') ? 1024 : size.includes('M') ? 1 : 0.001)
      
      if (sizeInMB > 5) {
        warning(`Bundle size is large: ${size}`)
        results.recommendations.push('Consider code splitting and lazy loading')
      } else {
        success(`Bundle size is reasonable: ${size}`)
      }
    }
    
    // Analyze individual chunks
    const jsFiles = execSync(`find ${distPath} -name "*.js" -exec du -h {} +`).toString()
    const cssFiles = execSync(`find ${distPath} -name "*.css" -exec du -h {} +`).toString()
    
    results.bundleAnalysis.jsFiles = jsFiles.split('\n').filter(Boolean).length
    results.bundleAnalysis.cssFiles = cssFiles.split('\n').filter(Boolean).length
    
    info(`JavaScript files: ${results.bundleAnalysis.jsFiles}`)
    info(`CSS files: ${results.bundleAnalysis.cssFiles}`)
    
  } catch (err) {
    error(`Bundle analysis failed: ${err.message}`)
  }
}

/**
 * Check for potential memory leaks in the codebase
 */
const checkMemoryLeaks = () => {
  log('\n🔍 Checking for Memory Leak Patterns...', 'bright')
  
  const patterns = [
    {
      name: 'Event listeners without cleanup',
      regex: /addEventListener.*(?!removeEventListener)/g,
      severity: 'high'
    },
    {
      name: 'Timers without cleanup',
      regex: /(setInterval|setTimeout).*(?!clear)/g,
      severity: 'medium'
    },
    {
      name: 'Global variables',
      regex: /window\.\w+\s*=/g,
      severity: 'low'
    },
    {
      name: 'Circular references',
      regex: /this\.\w+\s*=.*this/g,
      severity: 'medium'
    }
  ]
  
  const searchPaths = [
    'frontend/src',
    'src'
  ]
  
  patterns.forEach(pattern => {
    searchPaths.forEach(searchPath => {
      if (fs.existsSync(searchPath)) {
        try {
          const files = execSync(`find ${searchPath} -name "*.ts" -o -name "*.js" -o -name "*.vue"`).toString().split('\n').filter(Boolean)
          
          files.forEach(file => {
            try {
              const content = fs.readFileSync(file, 'utf8')
              const matches = content.match(pattern.regex)
              
              if (matches) {
                results.memoryLeaks.push({
                  file,
                  pattern: pattern.name,
                  severity: pattern.severity,
                  matches: matches.length
                })
              }
            } catch (err) {
              // Skip files that can't be read
            }
          })
        } catch (err) {
          // Skip if find command fails
        }
      }
    })
  })
  
  if (results.memoryLeaks.length > 0) {
    warning(`Found ${results.memoryLeaks.length} potential memory leak patterns`)
    results.memoryLeaks.forEach(leak => {
      log(`  ${leak.file}: ${leak.pattern} (${leak.severity})`, 
          leak.severity === 'high' ? 'red' : leak.severity === 'medium' ? 'yellow' : 'blue')
    })
  } else {
    success('No obvious memory leak patterns detected')
  }
}

/**
 * Analyze Socket.io performance
 */
const analyzeSocketPerformance = () => {
  log('\n🔌 Analyzing Socket.io Performance...', 'bright')
  
  try {
    // Check Socket.io configuration
    const socketFiles = [
      'frontend/src/composables/useSocket.ts',
      'src/composables/useSocket.ts',
      'frontend/src/plugins/socket.ts',
      'src/plugins/socket.ts'
    ]
    
    let socketConfig = null
    for (const file of socketFiles) {
      if (fs.existsSync(file)) {
        socketConfig = fs.readFileSync(file, 'utf8')
        break
      }
    }
    
    if (socketConfig) {
      // Check for potential issues
      const issues = []
      
      if (!socketConfig.includes('disconnect')) {
        issues.push('Missing disconnect cleanup')
      }
      
      if (!socketConfig.includes('removeEventListener') && !socketConfig.includes('off')) {
        issues.push('Event listeners may not be properly cleaned up')
      }
      
      if (socketConfig.includes('setInterval') && !socketConfig.includes('clearInterval')) {
        issues.push('Timers may not be properly cleaned up')
      }
      
      if (issues.length > 0) {
        warning('Socket.io potential issues:')
        issues.forEach(issue => log(`  - ${issue}`, 'yellow'))
        results.recommendations.push('Review Socket.io cleanup patterns')
      } else {
        success('Socket.io configuration looks good')
      }
    } else {
      info('Socket.io files not found for analysis')
    }
  } catch (err) {
    error(`Socket.io analysis failed: ${err.message}`)
  }
}

/**
 * Run Lighthouse performance audit
 */
const runLighthouseAudit = () => {
  log('\n🚨 Running Lighthouse Performance Audit...', 'bright')
  
  try {
    // Check if servers are running
    info('Make sure your development servers are running:')
    info('  Frontend: npm run dev (port 5173)')
    info('  Backend: npm run start (port 3001)')
    
    // Run Lighthouse audit
    const lighthouseCmd = `npx lighthouse http://localhost:5173 --output=json --output-path=lighthouse-report.json --chrome-flags="--headless" --quiet`
    
    info('Running Lighthouse audit... (this may take a minute)')
    execSync(lighthouseCmd, { stdio: 'pipe' })
    
    // Parse results
    const report = JSON.parse(fs.readFileSync('lighthouse-report.json', 'utf8'))
    const scores = report.lhr.categories
    
    log('\nLighthouse Scores:', 'bright')
    Object.entries(scores).forEach(([category, data]) => {
      const score = Math.round(data.score * 100)
      const color = score >= 90 ? 'green' : score >= 70 ? 'yellow' : 'red'
      log(`  ${data.title}: ${score}/100`, color)
    })
    
    // Check for specific performance issues
    const audits = report.lhr.audits
    const performanceIssues = []
    
    if (audits['largest-contentful-paint']?.score < 0.9) {
      performanceIssues.push('Largest Contentful Paint needs improvement')
    }
    
    if (audits['cumulative-layout-shift']?.score < 0.9) {
      performanceIssues.push('Cumulative Layout Shift needs improvement')
    }
    
    if (audits['first-input-delay']?.score < 0.9) {
      performanceIssues.push('First Input Delay needs improvement')
    }
    
    results.performanceIssues = performanceIssues
    
    // Clean up
    fs.unlinkSync('lighthouse-report.json')
    
  } catch (err) {
    warning(`Lighthouse audit failed: ${err.message}`)
    info('Make sure Chrome is installed and servers are running')
  }
}

/**
 * Generate performance recommendations
 */
const generateRecommendations = () => {
  log('\n💡 Performance Recommendations:', 'bright')
  
  // Bundle size recommendations
  if (results.bundleAnalysis.totalSize) {
    const size = results.bundleAnalysis.totalSize
    if (size.includes('M') && parseFloat(size) > 3) {
      results.recommendations.push('Consider implementing code splitting')
      results.recommendations.push('Use dynamic imports for large components')
      results.recommendations.push('Optimize images and assets')
    }
  }
  
  // Memory leak recommendations
  if (results.memoryLeaks.length > 0) {
    results.recommendations.push('Implement proper cleanup in component unmount')
    results.recommendations.push('Use AbortController for fetch requests')
    results.recommendations.push('Clear timers and intervals on cleanup')
  }
  
  // Performance issue recommendations
  if (results.performanceIssues.length > 0) {
    results.recommendations.push('Optimize critical rendering path')
    results.recommendations.push('Implement lazy loading for images')
    results.recommendations.push('Minimize layout shifts')
  }
  
  // Display recommendations
  if (results.recommendations.length > 0) {
    results.recommendations.forEach((rec, index) => {
      log(`  ${index + 1}. ${rec}`, 'cyan')
    })
  } else {
    success('No specific recommendations - performance looks good!')
  }
}

/**
 * Main execution
 */
const main = () => {
  log('\n🚀 HLenergy Performance & Memory Analysis', 'bright')
  log('═'.repeat(60), 'cyan')
  
  installDependencies()
  analyzeBundleSize()
  checkMemoryLeaks()
  analyzeSocketPerformance()
  runLighthouseAudit()
  generateRecommendations()
  
  log('\n📊 Analysis Complete!', 'bright')
  log('═'.repeat(60), 'cyan')
}

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = {
  analyzeBundleSize,
  checkMemoryLeaks,
  analyzeSocketPerformance,
  runLighthouseAudit,
  generateRecommendations
}
