#!/usr/bin/env node

/**
 * Socket.io Performance and Memory Leak Test
 * 
 * This script tests Socket.io connections for:
 * - Memory leaks during connect/disconnect cycles
 * - Performance under load
 * - Connection stability
 * - Event listener cleanup
 */

const io = require('socket.io-client')
const { performance } = require('perf_hooks')

// Test configuration
const config = {
  serverUrl: 'http://localhost:3001',
  maxConnections: 100,
  testDuration: 60000, // 1 minute
  connectionInterval: 100, // ms between connections
  messageInterval: 1000, // ms between messages
  verbose: false
}

// Test results
const results = {
  connections: {
    successful: 0,
    failed: 0,
    total: 0
  },
  messages: {
    sent: 0,
    received: 0,
    lost: 0
  },
  performance: {
    avgConnectionTime: 0,
    avgMessageLatency: 0,
    memoryUsage: [],
    errors: []
  },
  memoryLeaks: []
}

// Active connections tracking
const activeConnections = new Map()
const connectionTimes = []
const messageLatencies = []

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

const success = (message) => log(`✅ ${message}`, 'green')
const warning = (message) => log(`⚠️  ${message}`, 'yellow')
const error = (message) => log(`❌ ${message}`, 'red')
const info = (message) => log(`ℹ️  ${message}`, 'blue')

/**
 * Monitor memory usage
 */
const monitorMemory = () => {
  const memUsage = process.memoryUsage()
  results.performance.memoryUsage.push({
    timestamp: Date.now(),
    heapUsed: memUsage.heapUsed,
    heapTotal: memUsage.heapTotal,
    external: memUsage.external,
    rss: memUsage.rss,
    activeConnections: activeConnections.size
  })
  
  if (config.verbose) {
    log(`Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB, Connections: ${activeConnections.size}`, 'cyan')
  }
}

/**
 * Create a Socket.io connection with performance tracking
 */
const createConnection = (id) => {
  return new Promise((resolve, reject) => {
    const startTime = performance.now()
    
    const socket = io(config.serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 5000,
      forceNew: true
    })
    
    const connectionData = {
      id,
      socket,
      startTime,
      connected: false,
      messagesSent: 0,
      messagesReceived: 0,
      errors: []
    }
    
    // Connection success
    socket.on('connect', () => {
      const connectionTime = performance.now() - startTime
      connectionTimes.push(connectionTime)
      
      connectionData.connected = true
      activeConnections.set(id, connectionData)
      results.connections.successful++
      
      if (config.verbose) {
        log(`Connection ${id} established in ${connectionTime.toFixed(2)}ms`, 'green')
      }
      
      resolve(connectionData)
    })
    
    // Connection error
    socket.on('connect_error', (err) => {
      connectionData.errors.push(err.message)
      results.connections.failed++
      results.performance.errors.push({
        type: 'connection_error',
        message: err.message,
        connectionId: id
      })
      
      error(`Connection ${id} failed: ${err.message}`)
      reject(err)
    })
    
    // Message handling
    socket.on('test:response', (data) => {
      const latency = performance.now() - data.timestamp
      messageLatencies.push(latency)
      connectionData.messagesReceived++
      results.messages.received++
      
      if (config.verbose) {
        log(`Message received on connection ${id}, latency: ${latency.toFixed(2)}ms`, 'blue')
      }
    })
    
    // Error handling
    socket.on('error', (err) => {
      connectionData.errors.push(err.message)
      results.performance.errors.push({
        type: 'socket_error',
        message: err.message,
        connectionId: id
      })
      
      warning(`Socket error on connection ${id}: ${err.message}`)
    })
    
    // Disconnect handling
    socket.on('disconnect', (reason) => {
      if (config.verbose) {
        log(`Connection ${id} disconnected: ${reason}`, 'yellow')
      }
      
      activeConnections.delete(id)
    })
    
    // Timeout
    setTimeout(() => {
      if (!connectionData.connected) {
        socket.disconnect()
        results.connections.failed++
        reject(new Error('Connection timeout'))
      }
    }, 10000)
  })
}

/**
 * Send test messages on a connection
 */
const sendTestMessages = (connectionData) => {
  const { socket, id } = connectionData
  
  const sendMessage = () => {
    if (socket.connected) {
      const message = {
        id: `msg_${id}_${connectionData.messagesSent}`,
        timestamp: performance.now(),
        data: 'test_data'
      }
      
      socket.emit('test:message', message)
      connectionData.messagesSent++
      results.messages.sent++
      
      if (config.verbose) {
        log(`Message sent from connection ${id}`, 'blue')
      }
    }
  }
  
  // Send initial message
  sendMessage()
  
  // Set up interval for regular messages
  const messageInterval = setInterval(sendMessage, config.messageInterval)
  
  // Store interval for cleanup
  connectionData.messageInterval = messageInterval
  
  return messageInterval
}

/**
 * Cleanup a connection
 */
const cleanupConnection = (connectionData) => {
  const { socket, messageInterval } = connectionData
  
  // Clear message interval
  if (messageInterval) {
    clearInterval(messageInterval)
  }
  
  // Disconnect socket
  if (socket && socket.connected) {
    socket.disconnect()
  }
  
  // Remove from active connections
  activeConnections.delete(connectionData.id)
}

/**
 * Detect memory leaks
 */
const detectMemoryLeaks = () => {
  const memoryData = results.performance.memoryUsage
  if (memoryData.length < 10) return
  
  // Check for consistent memory growth
  const recentData = memoryData.slice(-10)
  const oldData = memoryData.slice(-20, -10)
  
  if (oldData.length === 0) return
  
  const recentAvg = recentData.reduce((sum, data) => sum + data.heapUsed, 0) / recentData.length
  const oldAvg = oldData.reduce((sum, data) => sum + data.heapUsed, 0) / oldData.length
  
  const growthRate = (recentAvg - oldAvg) / oldAvg
  
  if (growthRate > 0.1) { // 10% growth
    results.memoryLeaks.push({
      timestamp: Date.now(),
      growthRate: growthRate * 100,
      heapUsed: recentAvg,
      activeConnections: activeConnections.size
    })
    
    warning(`Potential memory leak detected: ${(growthRate * 100).toFixed(2)}% growth`)
  }
}

/**
 * Run the performance test
 */
const runPerformanceTest = async () => {
  log('\n🚀 Starting Socket.io Performance Test', 'bright')
  log('═'.repeat(60), 'cyan')
  
  info(`Server: ${config.serverUrl}`)
  info(`Max Connections: ${config.maxConnections}`)
  info(`Test Duration: ${config.testDuration / 1000}s`)
  
  // Start memory monitoring
  const memoryMonitor = setInterval(monitorMemory, 1000)
  const leakDetector = setInterval(detectMemoryLeaks, 5000)
  
  // Create connections gradually
  const connectionPromises = []
  
  for (let i = 0; i < config.maxConnections; i++) {
    results.connections.total++
    
    const connectionPromise = createConnection(i)
      .then(connectionData => {
        // Start sending messages
        sendTestMessages(connectionData)
        return connectionData
      })
      .catch(err => {
        error(`Failed to create connection ${i}: ${err.message}`)
      })
    
    connectionPromises.push(connectionPromise)
    
    // Wait between connections to avoid overwhelming the server
    if (i < config.maxConnections - 1) {
      await new Promise(resolve => setTimeout(resolve, config.connectionInterval))
    }
  }
  
  // Wait for all connections to be established
  info('Waiting for all connections to establish...')
  await Promise.allSettled(connectionPromises)
  
  info(`${activeConnections.size} connections established`)
  
  // Run test for specified duration
  info(`Running test for ${config.testDuration / 1000} seconds...`)
  await new Promise(resolve => setTimeout(resolve, config.testDuration))
  
  // Cleanup
  info('Cleaning up connections...')
  
  // Cleanup all connections
  for (const connectionData of activeConnections.values()) {
    cleanupConnection(connectionData)
  }
  
  // Stop monitoring
  clearInterval(memoryMonitor)
  clearInterval(leakDetector)
  
  // Wait for cleanup to complete
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // Final memory check
  monitorMemory()
}

/**
 * Generate performance report
 */
const generateReport = () => {
  log('\n📊 Performance Test Results', 'bright')
  log('═'.repeat(60), 'cyan')
  
  // Connection statistics
  log('\n🔌 Connection Statistics:', 'bright')
  success(`Successful: ${results.connections.successful}/${results.connections.total}`)
  if (results.connections.failed > 0) {
    warning(`Failed: ${results.connections.failed}`)
  }
  
  // Performance metrics
  if (connectionTimes.length > 0) {
    const avgConnectionTime = connectionTimes.reduce((sum, time) => sum + time, 0) / connectionTimes.length
    results.performance.avgConnectionTime = avgConnectionTime
    info(`Average connection time: ${avgConnectionTime.toFixed(2)}ms`)
  }
  
  if (messageLatencies.length > 0) {
    const avgLatency = messageLatencies.reduce((sum, lat) => sum + lat, 0) / messageLatencies.length
    results.performance.avgMessageLatency = avgLatency
    info(`Average message latency: ${avgLatency.toFixed(2)}ms`)
  }
  
  // Message statistics
  log('\n💬 Message Statistics:', 'bright')
  info(`Sent: ${results.messages.sent}`)
  info(`Received: ${results.messages.received}`)
  
  const messageSuccessRate = results.messages.sent > 0 ? 
    (results.messages.received / results.messages.sent) * 100 : 0
  
  if (messageSuccessRate < 95) {
    warning(`Message success rate: ${messageSuccessRate.toFixed(2)}%`)
  } else {
    success(`Message success rate: ${messageSuccessRate.toFixed(2)}%`)
  }
  
  // Memory analysis
  log('\n🧠 Memory Analysis:', 'bright')
  
  if (results.performance.memoryUsage.length > 0) {
    const initialMemory = results.performance.memoryUsage[0].heapUsed
    const finalMemory = results.performance.memoryUsage[results.performance.memoryUsage.length - 1].heapUsed
    const memoryGrowth = finalMemory - initialMemory
    
    info(`Initial memory: ${Math.round(initialMemory / 1024 / 1024)}MB`)
    info(`Final memory: ${Math.round(finalMemory / 1024 / 1024)}MB`)
    
    if (memoryGrowth > 0) {
      warning(`Memory growth: +${Math.round(memoryGrowth / 1024 / 1024)}MB`)
    } else {
      success(`Memory growth: ${Math.round(memoryGrowth / 1024 / 1024)}MB`)
    }
  }
  
  // Memory leaks
  if (results.memoryLeaks.length > 0) {
    warning(`\n🚨 ${results.memoryLeaks.length} potential memory leaks detected`)
    results.memoryLeaks.forEach((leak, index) => {
      log(`  ${index + 1}. Growth rate: ${leak.growthRate.toFixed(2)}%`, 'red')
    })
  } else {
    success('\n✅ No memory leaks detected')
  }
  
  // Errors
  if (results.performance.errors.length > 0) {
    warning(`\n⚠️  ${results.performance.errors.length} errors occurred`)
    results.performance.errors.forEach((err, index) => {
      log(`  ${index + 1}. ${err.type}: ${err.message}`, 'red')
    })
  }
  
  log('\n📋 Test Complete!', 'bright')
}

/**
 * Main execution
 */
const main = async () => {
  try {
    await runPerformanceTest()
    generateReport()
  } catch (err) {
    error(`Test failed: ${err.message}`)
    process.exit(1)
  }
}

// Parse command line arguments
process.argv.forEach((arg, index) => {
  if (arg === '--verbose') config.verbose = true
  if (arg === '--connections') config.maxConnections = parseInt(process.argv[index + 1]) || config.maxConnections
  if (arg === '--duration') config.testDuration = parseInt(process.argv[index + 1]) * 1000 || config.testDuration
  if (arg === '--server') config.serverUrl = process.argv[index + 1] || config.serverUrl
})

// Run if called directly
if (require.main === module) {
  main()
}

module.exports = {
  runPerformanceTest,
  generateReport,
  results
}
