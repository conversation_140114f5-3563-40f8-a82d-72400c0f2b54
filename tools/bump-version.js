#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Version types
const VERSION_TYPES = {
  patch: 'patch',
  minor: 'minor', 
  major: 'major'
}

// Get version type from command line argument
const versionType = process.argv[2] || 'patch'

if (!VERSION_TYPES[versionType]) {
  console.error('❌ Invalid version type. Use: patch, minor, or major')
  process.exit(1)
}

console.log(`🚀 Bumping ${versionType} version...`)

// Files to update
const FILES_TO_UPDATE = [
  {
    path: 'frontend/package.json',
    type: 'json',
    versionField: 'version'
  },
  {
    path: 'backend/package.json', 
    type: 'json',
    versionField: 'version'
  },
  {
    path: 'frontend/src/utils/version.ts',
    type: 'typescript',
    pattern: /export const APP_VERSION = '([^']+)'/,
    replacement: "export const APP_VERSION = '{VERSION}'"
  },
  {
    path: 'frontend/src/utils/version.ts',
    type: 'typescript',
    pattern: /export const BUILD_DATE = '([^']+)'/,
    replacement: `export const BUILD_DATE = '${new Date().toISOString()}'`
  }
]

// Function to increment version
function incrementVersion(version, type) {
  const parts = version.split('.').map(Number)
  
  switch (type) {
    case 'major':
      parts[0]++
      parts[1] = 0
      parts[2] = 0
      break
    case 'minor':
      parts[1]++
      parts[2] = 0
      break
    case 'patch':
    default:
      parts[2]++
      break
  }
  
  return parts.join('.')
}

// Function to update JSON files
function updateJsonFile(filePath, versionField, newVersion) {
  try {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'))
    content[versionField] = newVersion
    fs.writeFileSync(filePath, JSON.stringify(content, null, 2) + '\n')
    console.log(`✅ Updated ${filePath}`)
  } catch (error) {
    console.warn(`⚠️  Could not update ${filePath}: ${error.message}`)
  }
}

// Function to update TypeScript files
function updateTypeScriptFile(filePath, pattern, replacement, newVersion) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    
    if (replacement.includes('{VERSION}')) {
      content = content.replace(pattern, replacement.replace('{VERSION}', newVersion))
    } else {
      content = content.replace(pattern, replacement)
    }
    
    fs.writeFileSync(filePath, content)
    console.log(`✅ Updated ${filePath}`)
  } catch (error) {
    console.warn(`⚠️  Could not update ${filePath}: ${error.message}`)
  }
}

// Main execution
try {
  // Get current version from frontend package.json
  const frontendPackagePath = 'frontend/package.json'
  const frontendPackage = JSON.parse(fs.readFileSync(frontendPackagePath, 'utf8'))
  const currentVersion = frontendPackage.version
  const newVersion = incrementVersion(currentVersion, versionType)
  
  console.log(`📦 Current version: ${currentVersion}`)
  console.log(`📦 New version: ${newVersion}`)
  
  // Update all files
  FILES_TO_UPDATE.forEach(file => {
    if (!fs.existsSync(file.path)) {
      console.warn(`⚠️  File not found: ${file.path}`)
      return
    }
    
    if (file.type === 'json') {
      updateJsonFile(file.path, file.versionField, newVersion)
    } else if (file.type === 'typescript') {
      updateTypeScriptFile(file.path, file.pattern, file.replacement, newVersion)
    }
  })
  
  // Update PWA manifest version
  const manifestPath = 'frontend/public/manifest.json'
  if (fs.existsSync(manifestPath)) {
    updateJsonFile(manifestPath, 'version', newVersion)
  }
  
  // Create git tag
  try {
    execSync(`git add .`)
    execSync(`git commit -m "chore: bump version to ${newVersion}"`)
    execSync(`git tag -a v${newVersion} -m "Version ${newVersion}"`)
    console.log(`✅ Created git tag: v${newVersion}`)
  } catch (error) {
    console.warn(`⚠️  Could not create git tag: ${error.message}`)
  }
  
  // Update environment variables
  const envExamplePath = 'frontend/.env.example'
  if (fs.existsSync(envExamplePath)) {
    let envContent = fs.readFileSync(envExamplePath, 'utf8')
    envContent = envContent.replace(
      /VITE_APP_VERSION=.*/,
      `VITE_APP_VERSION=${newVersion}`
    )
    fs.writeFileSync(envExamplePath, envContent)
    console.log(`✅ Updated ${envExamplePath}`)
  }
  
  console.log(`\n🎉 Version bump complete!`)
  console.log(`📋 Next steps:`)
  console.log(`   1. Review changes: git diff HEAD~1`)
  console.log(`   2. Push changes: git push origin main`)
  console.log(`   3. Push tags: git push origin --tags`)
  console.log(`   4. Build and deploy: npm run build`)
  console.log(`\n💡 PWA users will be prompted to update on next visit`)
  
} catch (error) {
  console.error(`❌ Error bumping version: ${error.message}`)
  process.exit(1)
}
