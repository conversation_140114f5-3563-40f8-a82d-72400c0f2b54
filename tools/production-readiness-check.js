#!/usr/bin/env node

/**
 * HLenergy Production Readiness Check
 * 
 * Comprehensive checklist to ensure the application is ready for production deployment
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

const success = (message) => log(`✅ ${message}`, 'green')
const warning = (message) => log(`⚠️  ${message}`, 'yellow')
const error = (message) => log(`❌ ${message}`, 'red')
const info = (message) => log(`ℹ️  ${message}`, 'blue')

// Check results
const results = {
  passed: 0,
  warnings: 0,
  failed: 0,
  checks: []
}

const addResult = (name, status, message, details = '') => {
  results.checks.push({ name, status, message, details })
  results[status]++
  
  switch (status) {
    case 'passed':
      success(`${name}: ${message}`)
      break
    case 'warning':
      warning(`${name}: ${message}`)
      break
    case 'failed':
      error(`${name}: ${message}`)
      break
  }
  
  if (details) {
    log(`   ${details}`, 'cyan')
  }
}

// Utility functions
const fileExists = (filePath) => fs.existsSync(filePath)
const readFile = (filePath) => {
  try {
    return fs.readFileSync(filePath, 'utf8')
  } catch {
    return null
  }
}

const runCommand = (command, silent = true) => {
  try {
    return execSync(command, { encoding: 'utf8', stdio: silent ? 'pipe' : 'inherit' })
  } catch (error) {
    return null
  }
}

// Check functions
const checkProjectStructure = () => {
  log('\n📁 Checking Project Structure...', 'bright')
  
  const requiredFiles = [
    'frontend/package.json',
    'frontend/vite.config.ts',
    'frontend/src/main.ts',
    'frontend/src/App.vue',
    'backend/package.json',
    'backend/src/app.js',
    'backend/src/config/database.js',
    'README.md'
  ]
  
  requiredFiles.forEach(file => {
    if (fileExists(file)) {
      addResult('File Structure', 'passed', `${file} exists`)
    } else {
      addResult('File Structure', 'failed', `${file} missing`)
    }
  })
}

const checkDependencies = () => {
  log('\n📦 Checking Dependencies...', 'bright')
  
  // Check frontend dependencies
  const frontendPackage = readFile('frontend/package.json')
  if (frontendPackage) {
    const pkg = JSON.parse(frontendPackage)
    
    // Check for security vulnerabilities
    const auditResult = runCommand('cd frontend && npm audit --audit-level=high')
    if (auditResult && !auditResult.includes('found 0 vulnerabilities')) {
      addResult('Security', 'warning', 'Frontend dependencies have security vulnerabilities', 'Run: npm audit fix')
    } else {
      addResult('Security', 'passed', 'No high-severity vulnerabilities in frontend')
    }
    
    // Check for outdated packages
    const outdatedResult = runCommand('cd frontend && npm outdated')
    if (outdatedResult && outdatedResult.trim()) {
      addResult('Dependencies', 'warning', 'Frontend has outdated packages', 'Run: npm update')
    } else {
      addResult('Dependencies', 'passed', 'Frontend dependencies are up to date')
    }
  }
  
  // Check backend dependencies
  const backendPackage = readFile('backend/package.json')
  if (backendPackage) {
    const auditResult = runCommand('cd backend && npm audit --audit-level=high')
    if (auditResult && !auditResult.includes('found 0 vulnerabilities')) {
      addResult('Security', 'warning', 'Backend dependencies have security vulnerabilities', 'Run: npm audit fix')
    } else {
      addResult('Security', 'passed', 'No high-severity vulnerabilities in backend')
    }
  }
}

const checkConfiguration = () => {
  log('\n⚙️  Checking Configuration...', 'bright')
  
  // Check environment files
  const envFiles = [
    'frontend/.env.example',
    'backend/.env.example'
  ]
  
  envFiles.forEach(file => {
    if (fileExists(file)) {
      addResult('Configuration', 'passed', `${file} exists`)
    } else {
      addResult('Configuration', 'warning', `${file} missing`, 'Create example environment file')
    }
  })
  
  // Check production configurations
  const viteConfig = readFile('frontend/vite.config.ts')
  if (viteConfig && viteConfig.includes('VitePWA')) {
    addResult('PWA', 'passed', 'PWA configuration found')
  } else {
    addResult('PWA', 'failed', 'PWA configuration missing')
  }
}

const checkSecurity = () => {
  log('\n🔒 Checking Security...', 'bright')
  
  // Check for sensitive data in code
  const sensitivePatterns = [
    { pattern: /password\s*=\s*["'][^"']+["']/gi, name: 'Hardcoded passwords' },
    { pattern: /api[_-]?key\s*=\s*["'][^"']+["']/gi, name: 'Hardcoded API keys' },
    { pattern: /secret\s*=\s*["'][^"']+["']/gi, name: 'Hardcoded secrets' },
    { pattern: /token\s*=\s*["'][^"']+["']/gi, name: 'Hardcoded tokens' }
  ]
  
  const checkDirectory = (dir) => {
    const files = runCommand(`find ${dir} -name "*.js" -o -name "*.ts" -o -name "*.vue" | head -50`)
    if (files) {
      files.split('\n').filter(Boolean).forEach(file => {
        const content = readFile(file)
        if (content) {
          sensitivePatterns.forEach(({ pattern, name }) => {
            if (pattern.test(content)) {
              addResult('Security', 'failed', `${name} found in ${file}`)
            }
          })
        }
      })
    }
  }
  
  checkDirectory('frontend/src')
  checkDirectory('backend/src')
  
  addResult('Security', 'passed', 'No obvious security issues found in code')
}

const checkBuild = () => {
  log('\n🔨 Checking Build Process...', 'bright')
  
  // Test frontend build
  info('Testing frontend build...')
  const frontendBuild = runCommand('cd frontend && npm run build', false)
  if (frontendBuild !== null) {
    addResult('Build', 'passed', 'Frontend builds successfully')
    
    // Check build output
    if (fileExists('frontend/dist/index.html')) {
      addResult('Build', 'passed', 'Frontend build output exists')
    } else {
      addResult('Build', 'failed', 'Frontend build output missing')
    }
  } else {
    addResult('Build', 'failed', 'Frontend build failed')
  }
  
  // Check if backend starts
  info('Testing backend startup...')
  // Note: This is a basic check - in real scenarios you'd want more sophisticated testing
  const backendCheck = runCommand('cd backend && node -c src/app.js')
  if (backendCheck !== null) {
    addResult('Build', 'passed', 'Backend syntax is valid')
  } else {
    addResult('Build', 'failed', 'Backend has syntax errors')
  }
}

const checkTests = () => {
  log('\n🧪 Checking Tests...', 'bright')
  
  // Check if tests exist
  const testFiles = [
    'frontend/tests',
    'backend/tests'
  ]
  
  testFiles.forEach(dir => {
    if (fileExists(dir)) {
      addResult('Tests', 'passed', `${dir} directory exists`)
    } else {
      addResult('Tests', 'warning', `${dir} directory missing`)
    }
  })
  
  // Run frontend tests
  info('Running frontend tests...')
  const frontendTests = runCommand('cd frontend && npm run test:unit', false)
  if (frontendTests !== null) {
    addResult('Tests', 'passed', 'Frontend tests run successfully')
  } else {
    addResult('Tests', 'warning', 'Frontend tests failed or not configured')
  }
}

const checkDocumentation = () => {
  log('\n📚 Checking Documentation...', 'bright')
  
  const docFiles = [
    'README.md',
    'frontend/README.md',
    'backend/README.md',
    'DEPLOYMENT.md',
    'API.md'
  ]
  
  docFiles.forEach(file => {
    if (fileExists(file)) {
      const content = readFile(file)
      if (content && content.length > 100) {
        addResult('Documentation', 'passed', `${file} exists and has content`)
      } else {
        addResult('Documentation', 'warning', `${file} exists but is too short`)
      }
    } else {
      addResult('Documentation', 'warning', `${file} missing`)
    }
  })
}

const checkPerformance = () => {
  log('\n⚡ Checking Performance...', 'bright')
  
  // Check bundle size (if build exists)
  if (fileExists('frontend/dist')) {
    const bundleSize = runCommand('du -sh frontend/dist')
    if (bundleSize) {
      const sizeMatch = bundleSize.match(/(\d+(?:\.\d+)?[KMG]?)/)
      if (sizeMatch) {
        const size = sizeMatch[1]
        addResult('Performance', 'passed', `Bundle size: ${size}`)
      }
    }
  }
  
  // Check for performance optimizations
  const viteConfig = readFile('frontend/vite.config.ts')
  if (viteConfig) {
    if (viteConfig.includes('rollupOptions')) {
      addResult('Performance', 'passed', 'Build optimization configured')
    } else {
      addResult('Performance', 'warning', 'Consider adding build optimizations')
    }
  }
}

const checkVersioning = () => {
  log('\n🏷️  Checking Versioning...', 'bright')
  
  // Check version consistency
  const frontendPkg = readFile('frontend/package.json')
  const versionConfig = readFile('frontend/src/config/version.ts')
  
  if (frontendPkg && versionConfig) {
    const pkgVersion = JSON.parse(frontendPkg).version
    const configVersionMatch = versionConfig.match(/version: '([^']+)'/)
    
    if (configVersionMatch && pkgVersion === configVersionMatch[1]) {
      addResult('Versioning', 'passed', `Version consistency: ${pkgVersion}`)
    } else {
      addResult('Versioning', 'failed', 'Version mismatch between package.json and config')
    }
  }
  
  // Check git tags
  const gitTags = runCommand('git tag --sort=-version:refname | head -5')
  if (gitTags && gitTags.trim()) {
    addResult('Versioning', 'passed', 'Git tags exist')
  } else {
    addResult('Versioning', 'warning', 'No git tags found')
  }
}

// Main execution
const main = () => {
  log('\n🚀 HLenergy Production Readiness Check', 'bright')
  log('═'.repeat(60), 'cyan')
  
  checkProjectStructure()
  checkDependencies()
  checkConfiguration()
  checkSecurity()
  checkBuild()
  checkTests()
  checkDocumentation()
  checkPerformance()
  checkVersioning()
  
  // Summary
  log('\n📊 Summary', 'bright')
  log('═'.repeat(60), 'cyan')
  
  success(`Passed: ${results.passed}`)
  warning(`Warnings: ${results.warnings}`)
  error(`Failed: ${results.failed}`)
  
  const total = results.passed + results.warnings + results.failed
  const score = Math.round((results.passed / total) * 100)
  
  log(`\n🎯 Production Readiness Score: ${score}%`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red')
  
  if (score >= 80) {
    success('✅ Application is ready for production deployment!')
  } else if (score >= 60) {
    warning('⚠️  Application needs some improvements before production')
  } else {
    error('❌ Application is not ready for production deployment')
  }
  
  log('\n📋 Recommendations:', 'bright')
  if (results.failed > 0) {
    error('• Fix all failed checks before deployment')
  }
  if (results.warnings > 0) {
    warning('• Address warnings to improve production readiness')
  }
  if (score < 100) {
    info('• Run this check regularly during development')
    info('• Consider implementing automated checks in CI/CD')
  }
  
  log('═'.repeat(60), 'cyan')
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0)
}

if (require.main === module) {
  main()
}

module.exports = { main }
