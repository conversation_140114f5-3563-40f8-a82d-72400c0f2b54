/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  future: {
    hoverOnlyWhenSupported: true,
  },
  theme: {
    extend: {
      colors: {
        // Custom HLenergy brand colors
        'hlenergy': {
          'forest': '#02342b',      // Deep forest green - primary dark
          'gold': '#eaaa34',        // Warm gold - accent/warning
          'teal': '#12816c',        // Rich teal - primary
          'sage': '#5cad64',        // Sage green - success
          'mint': '#389868',        // Fresh mint - secondary
          'lime': '#7fbf60',        // Bright lime - info
        },
        primary: {
          50: '#f0fdf9',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#12816c',  // Our teal
          600: '#0f6b5a',
          700: '#0d5a4a',
          800: '#0a4a3a',
          900: '#02342b',  // Our forest
          950: '#01251f',
        },
        secondary: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#389868',  // Our mint
          600: '#2d7a54',
          700: '#22543d',
          800: '#1a202c',
          900: '#171923',
          950: '#0d1117',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        heading: ['Poppins', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'theme-transition': 'themeTransition 0.3s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 20px rgba(18, 129, 108, 0.3)' },
          '100%': { boxShadow: '0 0 30px rgba(18, 129, 108, 0.6)' },
        },
        themeTransition: {
          '0%': { opacity: '0.8' },
          '100%': { opacity: '1' },
        },
      },
      boxShadow: {
        'glow': '0 0 20px rgba(18, 129, 108, 0.3)',
        'glow-gold': '0 0 20px rgba(234, 170, 52, 0.3)',
        'glow-sage': '0 0 20px rgba(92, 173, 100, 0.3)',
        'theme-shadow': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      },
    },
  },
  plugins: [
    require('daisyui'),
  ],
  daisyui: {
    themes: [
      {
        // Light Theme - HLenergy Professional
        "hlenergy-light": {
          "primary": "#12816c",        // Rich teal
          "primary-focus": "#0f6b5a",  // Darker teal
          "primary-content": "#ffffff", // White text on primary

          "secondary": "#389868",       // Fresh mint
          "secondary-focus": "#2d7a54", // Darker mint
          "secondary-content": "#ffffff", // White text on secondary

          "accent": "#eaaa34",         // Warm gold
          "accent-focus": "#d4941c",   // Darker gold
          "accent-content": "#02342b", // Dark forest text on accent

          "neutral": "#1f2937",        // Dark gray for better readability
          "neutral-focus": "#111827",  // Darker gray
          "neutral-content": "#ffffff", // White text on neutral

          "base-100": "#ffffff",       // Pure white background
          "base-200": "#f9fafb",       // Very light gray
          "base-300": "#f3f4f6",       // Light gray
          "base-content": "#111827",   // Very dark gray text for readability

          "info": "#3b82f6",           // Blue for info
          "info-content": "#ffffff",   // White text on info

          "success": "#10b981",        // Green for success
          "success-content": "#ffffff", // White text on success

          "warning": "#f59e0b",        // Orange for warning
          "warning-content": "#ffffff", // White text on warning

          "error": "#ef4444",          // Red for errors
          "error-content": "#ffffff",  // White text on error
        },
      },
      {
        // Dark Theme - HLenergy Professional Dark
        "hlenergy-dark": {
          "primary": "#5cad64",        // Sage green (lighter for dark theme)
          "primary-focus": "#4a9454",  // Darker sage
          "primary-content": "#ffffff", // White text on primary

          "secondary": "#7fbf60",       // Bright lime
          "secondary-focus": "#6ba050", // Darker lime
          "secondary-content": "#02342b", // Dark forest text on secondary

          "accent": "#eaaa34",         // Warm gold
          "accent-focus": "#d4941c",   // Darker gold
          "accent-content": "#02342b", // Dark forest text on accent

          "neutral": "#12816c",        // Rich teal
          "neutral-focus": "#0f6b5a",  // Darker teal
          "neutral-content": "#ffffff", // White text on neutral

          "base-100": "#0a1f1b",       // Very dark forest
          "base-200": "#0f2a24",       // Dark forest
          "base-300": "#1a3d35",       // Medium forest
          "base-content": "#e8f5f2",   // Light mint text

          "info": "#389868",           // Fresh mint
          "info-content": "#ffffff",   // White text on info

          "success": "#7fbf60",        // Bright lime
          "success-content": "#02342b", // Dark forest text on success

          "warning": "#eaaa34",        // Warm gold
          "warning-content": "#02342b", // Dark forest text on warning

          "error": "#f87171",          // Lighter red for dark theme
          "error-content": "#ffffff",  // White text on error
        },
      },
      // Keep some popular themes as alternatives
      "light",
      "dark",
      "emerald",
      "forest",
      "business",
    ],
    darkTheme: "hlenergy-dark",
    base: true,
    styled: true,
    utils: true,
    logs: false,
    themeRoot: ":root",
  },
}

