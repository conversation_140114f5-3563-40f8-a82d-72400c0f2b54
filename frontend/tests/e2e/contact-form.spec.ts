import { test, expect } from '@playwright/test'

test.describe('Contact Form E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display contact form', async ({ page }) => {
    // Navigate to contact section or page
    await page.click('text=Contact')
    
    // Verify form elements are visible
    await expect(page.locator('input[name="name"]')).toBeVisible()
    await expect(page.locator('input[name="email"]')).toBeVisible()
    await expect(page.locator('input[name="phone"]')).toBeVisible()
    await expect(page.locator('input[name="company"]')).toBeVisible()
    await expect(page.locator('textarea[name="message"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })

  test('should validate required fields', async ({ page }) => {
    await page.click('text=Contact')
    
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Check for validation messages
    await expect(page.locator('.error, .invalid, [role="alert"]')).toBeVisible()
  })

  test('should validate email format', async ({ page }) => {
    await page.click('text=Contact')
    
    // Fill invalid email
    await page.fill('input[name="email"]', 'invalid-email')
    await page.blur('input[name="email"]')
    
    // Check for email validation error
    await expect(page.locator('text=/valid email/i')).toBeVisible()
  })

  test('should submit form successfully', async ({ page }) => {
    // Mock the API response
    await page.route('**/api/contact', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Contact form submitted successfully'
        })
      })
    })

    await page.click('text=Contact')
    
    // Fill form with valid data
    await page.fill('input[name="name"]', 'John Doe')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="phone"]', '+1234567890')
    await page.fill('input[name="company"]', 'Test Company')
    await page.fill('textarea[name="message"]', 'This is a test message for HLenergy contact form.')
    
    // Submit form
    await page.click('button[type="submit"]')
    
    // Wait for success message
    await expect(page.locator('text=/success/i')).toBeVisible()
    
    // Verify form is reset
    await expect(page.locator('input[name="name"]')).toHaveValue('')
    await expect(page.locator('input[name="email"]')).toHaveValue('')
    await expect(page.locator('textarea[name="message"]')).toHaveValue('')
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/contact', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          message: 'Server error'
        })
      })
    })

    await page.click('text=Contact')
    
    // Fill and submit form
    await page.fill('input[name="name"]', 'John Doe')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('textarea[name="message"]', 'Test message')
    await page.click('button[type="submit"]')
    
    // Check for error message
    await expect(page.locator('text=/error/i')).toBeVisible()
  })

  test('should show loading state during submission', async ({ page }) => {
    // Mock delayed API response
    await page.route('**/api/contact', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    await page.click('text=Contact')
    
    // Fill and submit form
    await page.fill('input[name="name"]', 'John Doe')
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('textarea[name="message"]', 'Test message')
    
    const submitButton = page.locator('button[type="submit"]')
    await submitButton.click()
    
    // Check loading state
    await expect(submitButton).toBeDisabled()
    await expect(page.locator('text=/loading/i')).toBeVisible()
  })

  test('should work across different languages', async ({ page }) => {
    await page.click('text=Contact')
    
    // Switch to Spanish
    await page.click('[data-testid="language-selector"]')
    await page.click('text=Español')
    
    // Verify form labels are translated
    await expect(page.locator('text=/nombre/i')).toBeVisible()
    await expect(page.locator('text=/correo/i')).toBeVisible()
    
    // Switch to Portuguese
    await page.click('[data-testid="language-selector"]')
    await page.click('text=Português')
    
    // Verify form labels are translated
    await expect(page.locator('text=/nome/i')).toBeVisible()
    await expect(page.locator('text=/email/i')).toBeVisible()
  })

  test('should be accessible', async ({ page }) => {
    await page.click('text=Contact')
    
    // Check form accessibility
    const form = page.locator('form')
    await expect(form).toBeVisible()
    
    // Check labels are associated with inputs
    const nameInput = page.locator('input[name="name"]')
    const nameLabel = page.locator('label[for="name"]')
    await expect(nameLabel).toBeVisible()
    
    // Check form can be navigated with keyboard
    await page.keyboard.press('Tab')
    await expect(nameInput).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('input[name="email"]')).toBeFocused()
  })

  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.click('text=Contact')
    
    // Verify form is responsive
    await expect(page.locator('form')).toBeVisible()
    
    // Test touch interactions
    await page.tap('input[name="name"]')
    await page.fill('input[name="name"]', 'Mobile User')
    
    await page.tap('input[name="email"]')
    await page.fill('input[name="email"]', '<EMAIL>')
    
    // Verify virtual keyboard doesn't break layout
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })
})
