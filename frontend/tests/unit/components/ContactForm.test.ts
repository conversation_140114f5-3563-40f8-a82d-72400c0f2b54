import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import { createRouter, createWebHistory } from 'vue-router'

// Simple contact form component for testing
const ContactForm = {
  template: `
    <form @submit.prevent="handleSubmit">
      <input type="text" name="name" v-model="form.name" required />
      <input type="email" name="email" v-model="form.email" required />
      <input type="tel" name="phone" v-model="form.phone" />
      <textarea name="message" v-model="form.message" required></textarea>
      <button type="submit" :disabled="isSubmitting">
        {{ isSubmitting ? 'Loading...' : 'Send Message' }}
      </button>
      <div v-if="error" class="error">{{ error }}</div>
      <div v-if="success" class="success">{{ success }}</div>
    </form>
  `,
  data() {
    return {
      form: { name: '', email: '', phone: '', message: '' },
      isSubmitting: false,
      error: null,
      success: null
    }
  },
  methods: {
    async handleSubmit() {
      this.isSubmitting = true
      this.error = null
      this.success = null

      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.form)
        })

        if (response.ok) {
          this.success = 'Message sent successfully'
          this.form = { name: '', email: '', phone: '', message: '' }
        } else {
          this.error = 'Failed to send message'
        }
      } catch (err) {
        this.error = 'Network error'
      } finally {
        this.isSubmitting = false
      }
    }
  }
}

// Mock translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      contact: {
        name: 'Name',
        email: 'Email',
        phone: 'Phone',
        message: 'Message',
        send: 'Send Message'
      },
      common: {
        loading: 'Loading...'
      }
    }
  }
})

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [{ path: '/', component: { template: '<div>Home</div>' } }]
})

// Mock fetch
global.fetch = vi.fn()

describe('ContactForm', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('renders form fields correctly', () => {
    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    expect(wrapper.find('input[type="text"]').exists()).toBe(true) // name field
    expect(wrapper.find('input[type="email"]').exists()).toBe(true) // email field
    expect(wrapper.find('input[type="tel"]').exists()).toBe(true) // phone field
    expect(wrapper.find('textarea').exists()).toBe(true) // message field
    expect(wrapper.find('button[type="submit"]').exists()).toBe(true)
  })

  it('validates required fields', async () => {
    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Try to submit empty form
    await wrapper.find('form').trigger('submit.prevent')

    // Check for validation errors (HTML5 validation will prevent submission)
    expect(wrapper.find('input[required]').exists()).toBe(true)
  })

  it('validates email format', async () => {
    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    const emailInput = wrapper.find('input[type="email"]')
    await emailInput.setValue('invalid-email')
    await emailInput.trigger('blur')

    // HTML5 validation will handle this
    expect(emailInput.attributes('type')).toBe('email')
  })

  it('submits form with valid data', async () => {
    // Mock successful API response
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, message: 'Contact form submitted successfully' })
    } as Response)

    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Fill form with valid data
    await wrapper.find('input[name="name"]').setValue('John Doe')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    await wrapper.find('input[name="phone"]').setValue('+1234567890')
    await wrapper.find('textarea[name="message"]').setValue('Test message')

    // Submit form
    await wrapper.find('form').trigger('submit.prevent')

    // Wait for async operations
    await wrapper.vm.$nextTick()

    // Verify API call
    expect(mockFetch).toHaveBeenCalledWith(
      '/api/contact',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: expect.stringContaining('John Doe')
      })
    )
  })

  it('handles API errors gracefully', async () => {
    // Mock API error
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Fill and submit form
    await wrapper.find('input[name="name"]').setValue('John Doe')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    await wrapper.find('textarea[name="message"]').setValue('Test message')
    await wrapper.find('form').trigger('submit.prevent')

    // Wait for error handling
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 10))

    // Check for error message
    expect(wrapper.text()).toContain('Network error')
  })

  it.skip('shows loading state during submission', async () => {
    // Mock delayed API response
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockImplementationOnce(() =>
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true })
      } as Response), 100))
    )

    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Fill and submit form
    await wrapper.find('input[name="name"]').setValue('John Doe')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    await wrapper.find('textarea[name="message"]').setValue('Test message')

    const submitButton = wrapper.find('button[type="submit"]')
    await submitButton.trigger('click')

    // Wait for state update and check if button becomes disabled
    await wrapper.vm.$nextTick()

    // Check loading state - button should be disabled OR show loading text
    const isDisabled = submitButton.attributes('disabled') !== undefined
    const hasLoadingText = wrapper.text().includes('Loading...')

    expect(isDisabled || hasLoadingText).toBe(true)
  })

  it('resets form after successful submission', async () => {
    // Mock successful API response
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    } as Response)

    const wrapper = mount(ContactForm, {
      global: {
        plugins: [i18n, router]
      }
    })

    // Fill and submit form
    await wrapper.find('input[name="name"]').setValue('John Doe')
    await wrapper.find('input[name="email"]').setValue('<EMAIL>')
    await wrapper.find('textarea[name="message"]').setValue('Test message')
    await wrapper.find('form').trigger('submit.prevent')

    // Wait for completion
    await new Promise(resolve => setTimeout(resolve, 100))
    await wrapper.vm.$nextTick()

    // Check form is reset
    expect((wrapper.find('input[name="name"]').element as HTMLInputElement).value).toBe('')
    expect((wrapper.find('input[name="email"]').element as HTMLInputElement).value).toBe('')
    expect((wrapper.find('textarea[name="message"]').element as HTMLTextAreaElement).value).toBe('')
  })
})
