import { describe, it, expect, vi, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useAuthStore } from '@/stores/auth'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock as any

// Mock fetch
global.fetch = vi.fn()

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('initializes with default state', () => {
    const authStore = useAuthStore()
    
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.isLoading).toBe(false)
    expect(authStore.error).toBeNull()
  })

  it('loads token from localStorage on initialization', () => {
    const mockToken = 'mock-jwt-token'
    localStorageMock.getItem.mockReturnValue(mockToken)
    
    const authStore = useAuthStore()
    authStore.initializeAuth()
    
    expect(authStore.token).toBe(mockToken)
    expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token')
  })

  it('logs in user successfully', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      role: 'client'
    }
    const mockToken = 'mock-jwt-token'
    
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { user: mockUser, token: mockToken }
      })
    } as Response)

    const authStore = useAuthStore()
    
    await authStore.login('<EMAIL>', 'password123')
    
    expect(authStore.user).toEqual(mockUser)
    expect(authStore.token).toBe(mockToken)
    expect(authStore.isAuthenticated).toBe(true)
    expect(authStore.error).toBeNull()
    expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', mockToken)
  })

  it('handles login failure', async () => {
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        success: false,
        message: 'Invalid credentials'
      })
    } as Response)

    const authStore = useAuthStore()
    
    await authStore.login('<EMAIL>', 'wrongpassword')
    
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.error).toBe('Invalid credentials')
  })

  it('registers user successfully', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      name: 'New User',
      role: 'client'
    }
    const mockToken = 'mock-jwt-token'
    
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { user: mockUser, token: mockToken }
      })
    } as Response)

    const authStore = useAuthStore()
    
    await authStore.register({
      name: 'New User',
      email: '<EMAIL>',
      password: 'password123'
    })
    
    expect(authStore.user).toEqual(mockUser)
    expect(authStore.token).toBe(mockToken)
    expect(authStore.isAuthenticated).toBe(true)
  })

  it('logs out user and clears data', () => {
    const authStore = useAuthStore()
    
    // Set some initial state
    authStore.user = { id: 1, email: '<EMAIL>', name: 'Test', role: 'client' }
    authStore.token = 'mock-token'
    
    authStore.logout()
    
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
  })

  it('checks if user is admin', () => {
    const authStore = useAuthStore()
    
    // Test with admin user
    authStore.user = { id: 1, email: '<EMAIL>', name: 'Admin', role: 'admin' }
    expect(authStore.isAdmin).toBe(true)
    
    // Test with regular user
    authStore.user = { id: 2, email: '<EMAIL>', name: 'User', role: 'client' }
    expect(authStore.isAdmin).toBe(false)
    
    // Test with no user
    authStore.user = null
    expect(authStore.isAdmin).toBe(false)
  })

  it('checks if user is staff', () => {
    const authStore = useAuthStore()
    
    // Test with staff user
    authStore.user = { id: 1, email: '<EMAIL>', name: 'Staff', role: 'staff' }
    expect(authStore.isStaff).toBe(true)
    
    // Test with admin user (should also be staff)
    authStore.user = { id: 2, email: '<EMAIL>', name: 'Admin', role: 'admin' }
    expect(authStore.isStaff).toBe(true)
    
    // Test with regular user
    authStore.user = { id: 3, email: '<EMAIL>', name: 'User', role: 'client' }
    expect(authStore.isStaff).toBe(false)
  })

  it('handles network errors during login', async () => {
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const authStore = useAuthStore()
    
    await authStore.login('<EMAIL>', 'password123')
    
    expect(authStore.user).toBeNull()
    expect(authStore.token).toBeNull()
    expect(authStore.isAuthenticated).toBe(false)
    expect(authStore.error).toContain('Network error')
  })

  it('sets loading state during async operations', async () => {
    const mockFetch = vi.mocked(fetch)
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ success: true, data: { user: {}, token: 'token' } })
      } as Response), 100))
    )

    const authStore = useAuthStore()
    
    const loginPromise = authStore.login('<EMAIL>', 'password123')
    
    // Should be loading
    expect(authStore.isLoading).toBe(true)
    
    await loginPromise
    
    // Should not be loading anymore
    expect(authStore.isLoading).toBe(false)
  })
})
