<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        .locked { background: #fff3cd; color: #856404; }
        .unlocked { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔒 Session Lock Test</h1>
    <p>This page tests the session lock behavior with offline/online scenarios.</p>

    <div class="test-section">
        <h2>Current Status</h2>
        <div id="networkStatus" class="status">Checking...</div>
        <div id="sessionStatus" class="status">Checking...</div>
        <div id="lockFlag" class="status">Checking...</div>
    </div>

    <div class="test-section">
        <h2>Network Controls</h2>
        <p>Use browser DevTools to simulate offline/online:</p>
        <ol>
            <li>Open DevTools (F12)</li>
            <li>Go to Network tab</li>
            <li>Check "Offline" to simulate offline mode</li>
            <li>Uncheck to go back online</li>
        </ol>
        <button onclick="checkNetworkStatus()">Check Network Status</button>
        <button onclick="simulateOffline()">Simulate Offline Event</button>
        <button onclick="simulateOnline()">Simulate Online Event</button>
    </div>

    <div class="test-section">
        <h2>Session Lock Controls</h2>
        <button onclick="setSessionLockFlag()">Set Session Lock Flag</button>
        <button onclick="clearSessionLockFlag()">Clear Session Lock Flag</button>
        <button onclick="checkSessionLockFlag()">Check Session Lock Flag</button>
        <button onclick="simulateInactivity()">Simulate 5min Inactivity</button>
        <button onclick="simulatePageRefresh()">Simulate Page Refresh</button>
    </div>

    <div class="test-section">
        <h2>Test Scenarios</h2>
        <button onclick="testOfflineSessionLock()">Test: Offline Session Lock</button>
        <button onclick="testRefreshBypass()">Test: Refresh Bypass Prevention</button>
        <button onclick="testOnlineResume()">Test: Online Resume</button>
        <button onclick="clearAllTests()">Clear All</button>
    </div>

    <div class="test-section">
        <h2>Activity Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Logging function
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Status update functions
        function updateNetworkStatus() {
            const status = document.getElementById('networkStatus');
            const isOnline = navigator.onLine;
            status.textContent = `Network: ${isOnline ? 'Online' : 'Offline'}`;
            status.className = `status ${isOnline ? 'online' : 'offline'}`;
            log(`Network status: ${isOnline ? 'Online' : 'Offline'}`);
        }

        function updateSessionStatus() {
            const status = document.getElementById('sessionStatus');
            // This would normally check your auth store
            status.textContent = 'Session: Active (simulated)';
            status.className = 'status unlocked';
        }

        function updateLockFlag() {
            const status = document.getElementById('lockFlag');
            const flag = localStorage.getItem('session_lock_required');
            status.textContent = `Lock Flag: ${flag || 'Not Set'}`;
            status.className = `status ${flag === 'true' ? 'locked' : 'unlocked'}`;
        }

        // Network control functions
        function checkNetworkStatus() {
            updateNetworkStatus();
            log('Manual network status check performed');
        }

        function simulateOffline() {
            log('Simulating offline event...');
            window.dispatchEvent(new Event('offline'));
        }

        function simulateOnline() {
            log('Simulating online event...');
            window.dispatchEvent(new Event('online'));
        }

        // Session lock control functions
        function setSessionLockFlag() {
            localStorage.setItem('session_lock_required', 'true');
            updateLockFlag();
            log('Session lock flag set to true');
        }

        function clearSessionLockFlag() {
            localStorage.removeItem('session_lock_required');
            updateLockFlag();
            log('Session lock flag cleared');
        }

        function checkSessionLockFlag() {
            updateLockFlag();
            log('Session lock flag checked');
        }

        function simulateInactivity() {
            const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
            localStorage.setItem('last_activity', fiveMinutesAgo.toString());
            log('Simulated 5 minutes of inactivity');
        }

        function simulatePageRefresh() {
            log('Simulating page refresh (would reload in real scenario)');
            // In real scenario: location.reload()
            checkSessionLockFlag();
        }

        // Test scenario functions
        function testOfflineSessionLock() {
            log('=== Testing Offline Session Lock ===');
            log('1. Going offline...');
            simulateOffline();
            
            setTimeout(() => {
                log('2. Simulating inactivity...');
                simulateInactivity();
                
                setTimeout(() => {
                    log('3. Checking if session lock is prevented...');
                    const flag = localStorage.getItem('session_lock_required');
                    if (flag !== 'true') {
                        log('✅ SUCCESS: Session lock prevented while offline');
                    } else {
                        log('❌ FAIL: Session lock occurred while offline');
                    }
                }, 1000);
            }, 1000);
        }

        function testRefreshBypass() {
            log('=== Testing Refresh Bypass Prevention ===');
            log('1. Setting session lock flag...');
            setSessionLockFlag();
            
            setTimeout(() => {
                log('2. Simulating page refresh...');
                simulatePageRefresh();
                
                setTimeout(() => {
                    log('3. Checking if flag persists...');
                    const flag = localStorage.getItem('session_lock_required');
                    if (flag === 'true') {
                        log('✅ SUCCESS: Session lock flag persists after refresh');
                    } else {
                        log('❌ FAIL: Session lock flag was cleared');
                    }
                }, 1000);
            }, 1000);
        }

        function testOnlineResume() {
            log('=== Testing Online Resume ===');
            log('1. Going offline...');
            simulateOffline();
            
            setTimeout(() => {
                log('2. Coming back online...');
                simulateOnline();
                
                setTimeout(() => {
                    log('3. Session should resume normal timeout behavior');
                    log('✅ Online resume test completed');
                }, 1000);
            }, 2000);
        }

        function clearAllTests() {
            document.getElementById('log').innerHTML = '';
            clearSessionLockFlag();
            log('All tests cleared');
        }

        // Event listeners
        window.addEventListener('online', () => {
            updateNetworkStatus();
            log('🌐 ONLINE event detected');
        });

        window.addEventListener('offline', () => {
            updateNetworkStatus();
            log('🌐 OFFLINE event detected');
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateNetworkStatus();
            updateSessionStatus();
            updateLockFlag();
            log('Session Lock Test page initialized');
        });
    </script>
</body>
</html>
