<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Login - HLenergy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .instructions {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Admin Login Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li><strong>Test 1:</strong> Try wrong password - should show error without logout</li>
                <li><strong>Test 2:</strong> Try correct password - should login successfully</li>
                <li><strong>Test 3:</strong> Check browser console for any errors</li>
            </ol>
        </div>

        <!-- Test 1: Wrong Password -->
        <div class="test-section">
            <h3>🔍 Test 1: Wrong Password</h3>
            <p>This should show an error message without triggering logout.</p>
            
            <div class="form-group">
                <label for="email1">Email:</label>
                <input type="email" id="email1" value="<EMAIL>" readonly>
            </div>
            
            <div class="form-group">
                <label for="password1">Password:</label>
                <input type="password" id="password1" value="wrongpassword" readonly>
            </div>
            
            <button onclick="testWrongPassword()" id="btn1">Test Wrong Password</button>
            <div id="result1" class="result" style="display: none;"></div>
        </div>

        <!-- Test 2: Correct Password -->
        <div class="test-section">
            <h3>✅ Test 2: Correct Password</h3>
            <p>This should login successfully and redirect to admin dashboard.</p>
            
            <div class="form-group">
                <label for="email2">Email:</label>
                <input type="email" id="email2" value="<EMAIL>" readonly>
            </div>
            
            <div class="form-group">
                <label for="password2">Password:</label>
                <input type="password" id="password2" value="admin123456" readonly>
            </div>
            
            <button onclick="testCorrectPassword()" id="btn2">Test Correct Password</button>
            <div id="result2" class="result" style="display: none;"></div>
        </div>

        <!-- Console Log -->
        <div class="test-section">
            <h3>📊 Console Output</h3>
            <p>Check browser console (F12) for detailed logs.</p>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001/api/v1';
        
        // Override console.log to show in page
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsoleOutput(message, type = 'log') {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            output.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsoleOutput(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsoleOutput(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsoleOutput(args.join(' '), 'warn');
        };

        async function testWrongPassword() {
            const btn = document.getElementById('btn1');
            const result = document.getElementById('result1');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            result.style.display = 'none';
            
            console.log('🔍 Testing wrong password...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'wrongpassword'
                    })
                });
                
                const data = await response.json();
                
                if (response.status === 401) {
                    console.log('✅ PASS - Got expected 401 error');
                    console.log('❌ Authentication failed on auth endpoint: /auth/login');
                    result.className = 'result success';
                    result.textContent = `✅ PASS - Got 401 error: ${data.error?.message}`;
                } else {
                    console.log('❌ FAIL - Expected 401 but got:', response.status);
                    result.className = 'result error';
                    result.textContent = `❌ FAIL - Expected 401 but got: ${response.status}`;
                }
                
            } catch (error) {
                console.error('❌ Network error:', error.message);
                result.className = 'result error';
                result.textContent = `❌ Network Error: ${error.message}`;
            }
            
            result.style.display = 'block';
            btn.disabled = false;
            btn.textContent = 'Test Wrong Password';
        }

        async function testCorrectPassword() {
            const btn = document.getElementById('btn2');
            const result = document.getElementById('result2');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            result.style.display = 'none';
            
            console.log('🔍 Testing correct password...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123456'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    console.log('✅ PASS - Login successful');
                    console.log(`User: ${data.data.user.name}`);
                    console.log(`Role: ${data.data.user.role}`);
                    result.className = 'result success';
                    result.textContent = `✅ PASS - Login successful! User: ${data.data.user.name}`;
                } else {
                    console.log('❌ FAIL - Login failed:', data.error?.message);
                    result.className = 'result error';
                    result.textContent = `❌ FAIL - Login failed: ${data.error?.message}`;
                }
                
            } catch (error) {
                console.error('❌ Network error:', error.message);
                result.className = 'result error';
                result.textContent = `❌ Network Error: ${error.message}`;
            }
            
            result.style.display = 'block';
            btn.disabled = false;
            btn.textContent = 'Test Correct Password';
        }

        // Initialize
        console.log('🧪 Admin Login Test Page Loaded');
        console.log('📡 API Base URL:', API_BASE_URL);
        console.log('🔧 Frontend Fix Applied: API interceptor now distinguishes between auth endpoints and protected endpoints');
    </script>
</body>
</html>
