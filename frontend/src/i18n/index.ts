import { createI18n } from 'vue-i18n'
import en from '../locales/en.json'
import es from '../locales/es.json'
import pt from '../locales/pt.json'

export type MessageLanguages = keyof typeof en
export type MessageSchema = typeof en

const i18n = createI18n<[MessageSchema], 'en' | 'es' | 'pt'>({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en,
    es,
    pt,
  },
})

export default i18n
