<template>
  <div class="communication-form p-6 min-h-screen bg-base-200">
    <!-- Loading State -->
    <div v-if="isInitializing" class="flex items-center justify-center min-h-96">
      <div class="text-center space-y-4">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <p class="text-base-content/70">Loading communication form...</p>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Log Communication</h1>
        <p class="text-base-content/70 mt-1">Record customer interactions and communications</p>
      </div>
      <RouterLink to="/crm" class="btn btn-ghost">
        <Icon name="arrow-left" size="sm" class="mr-2" />
        Back to CRM
      </RouterLink>
    </div>

    <!-- Communication Form -->
    <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/10 bg-base-100/70 max-w-4xl mx-auto">
      <div class="card-body">
        <form @submit.prevent="submitCommunication" class="space-y-6">
          <!-- Customer Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Customer *</span>
              </label>
              <select
                v-model="form.customerId"
                class="select select-bordered w-full"
                required
                :disabled="isInitializing"
              >
                <option value="">{{ isInitializing ? 'Loading customers...' : 'Select a customer' }}</option>
                <option
                  v-for="customer in (customers || [])"
                  :key="customer.id"
                  :value="customer.id"
                >
                  {{ customer.firstName }} {{ customer.lastName }} - {{ customer.email }}
                </option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Project (Optional)</span>
              </label>
              <select 
                v-model="form.projectId" 
                class="select select-bordered w-full"
                :disabled="!form.customerId"
              >
                <option value="">No project</option>
                <option
                  v-for="project in filteredProjects"
                  :key="project.id"
                  :value="project.id"
                >
                  {{ project.title }}
                </option>
              </select>
            </div>
          </div>

          <!-- Communication Details -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Type *</span>
              </label>
              <select v-model="form.type" class="select select-bordered w-full" required>
                <option value="">Select type</option>
                <option value="email">Email</option>
                <option value="phone">Phone Call</option>
                <option value="meeting">Meeting</option>
                <option value="note">Note</option>
                <option value="proposal">Proposal</option>
                <option value="contract">Contract</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Direction</span>
              </label>
              <select v-model="form.direction" class="select select-bordered w-full">
                <option value="outbound">Outbound</option>
                <option value="inbound">Inbound</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Priority</span>
              </label>
              <select v-model="form.priority" class="select select-bordered w-full">
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <!-- Subject and Content -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Subject</span>
            </label>
            <input 
              v-model="form.subject" 
              type="text" 
              placeholder="Communication subject or title"
              class="input input-bordered w-full"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Content *</span>
            </label>
            <textarea 
              v-model="form.content" 
              placeholder="Describe the communication details, outcomes, and any follow-up actions needed..."
              class="textarea textarea-bordered w-full h-32"
              required
            ></textarea>
          </div>

          <!-- Status and Timing -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Status</span>
              </label>
              <select v-model="form.status" class="select select-bordered w-full">
                <option value="unread">Unread</option>
                <option value="read">Read</option>
                <option value="replied">Replied</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Communication Date</span>
              </label>
              <input 
                v-model="form.scheduledAt" 
                type="datetime-local" 
                class="input input-bordered w-full"
              />
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-4 pt-6">
            <RouterLink to="/crm" class="btn btn-ghost">
              Cancel
            </RouterLink>
            <button 
              type="submit" 
              class="btn btn-primary"
              :disabled="isSubmitting"
            >
              <span v-if="!isSubmitting">Log Communication</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Recent Communications -->
    <div class="card glass-effect shadow-2xl backdrop-blur-md border border-white/10 bg-base-100/70 max-w-4xl mx-auto mt-8">
      <div class="card-body">
        <h3 class="card-title mb-4">Recent Communications</h3>
        
        <div class="space-y-4">
          <div 
            v-for="comm in recentCommunications" 
            :key="comm.id" 
            class="flex items-start space-x-4 p-4 bg-base-200/50 hover:bg-base-200 rounded-lg transition-colors"
          >
            <div class="avatar placeholder">
              <div class="bg-gradient-to-br from-[#02342b] to-[#12816c] text-white rounded-full w-10">
                <span class="text-sm">{{ getTypeIcon(comm.type) }}</span>
              </div>
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between">
                <div class="font-medium">{{ comm.subject || `${comm.type} communication` }}</div>
                <div class="text-sm text-base-content/70">{{ formatDate(comm.createdAt) }}</div>
              </div>
              <div class="text-sm text-base-content/70 mt-1">
                {{ comm.customer?.firstName }} {{ comm.customer?.lastName }}
                <span v-if="comm.project"> • {{ comm.project?.title }}</span>
              </div>
              <div class="text-sm mt-2">{{ comm.content?.substring(0, 100) }}...</div>
            </div>
          </div>
          
          <div v-if="recentCommunications.length === 0" class="text-center py-8 text-base-content/50">
            No communications logged yet
          </div>
        </div>
      </div>
    </div>
    </div> <!-- Close main content div -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCRMStore } from '@/stores/crm'
import Icon from '@/components/common/Icon.vue'

const router = useRouter()
const crmStore = useCRMStore()

const {
  customers,
  projects,
  communications,
  fetchCustomers,
  fetchProjects,
  fetchCommunications,
  createCommunication
} = crmStore

// Form state
const isSubmitting = ref(false)
const isInitializing = ref(true)
const form = ref({
  customerId: '',
  projectId: '',
  type: '',
  direction: 'outbound',
  priority: 'medium',
  subject: '',
  content: '',
  status: 'read',
  scheduledAt: new Date().toISOString().slice(0, 16)
})

// Computed properties
const filteredProjects = computed(() => {
  if (!form.value.customerId) return []
  return (projects.value || []).filter(project => project.customerId == form.value.customerId)
})

const recentCommunications = computed(() => {
  return communications.value?.slice(0, 5) || []
})

// Methods
const submitCommunication = async () => {
  try {
    isSubmitting.value = true
    
    const communicationData = {
      ...form.value,
      customerId: parseInt(form.value.customerId),
      projectId: form.value.projectId ? parseInt(form.value.projectId) : null,
      scheduledAt: form.value.scheduledAt ? new Date(form.value.scheduledAt).toISOString() : null
    }
    
    await createCommunication(communicationData)
    
    // Reset form
    form.value = {
      customerId: '',
      projectId: '',
      type: '',
      direction: 'outbound',
      priority: 'medium',
      subject: '',
      content: '',
      status: 'read',
      scheduledAt: new Date().toISOString().slice(0, 16)
    }
    
    // Refresh communications list
    await fetchCommunications({ limit: 10 })
    
    // Show success message
    alert('Communication logged successfully!')
    
  } catch (error) {
    console.error('Error logging communication:', error)
    alert('Failed to log communication. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'email': return '📧'
    case 'phone': return '📞'
    case 'meeting': return '🤝'
    case 'note': return '📝'
    case 'proposal': return '📋'
    case 'contract': return '📄'
    default: return '💬'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Initialize data
onMounted(async () => {
  try {
    await Promise.all([
      fetchCustomers({ limit: 100 }).catch(err => {
        console.warn('Failed to fetch customers:', err)
        return { items: [], pagination: {} }
      }),
      fetchProjects({ limit: 100 }).catch(err => {
        console.warn('Failed to fetch projects:', err)
        return { items: [], pagination: {} }
      }),
      fetchCommunications({ limit: 10 }).catch(err => {
        console.warn('Failed to fetch communications:', err)
        return { items: [], pagination: {} }
      })
    ])
  } catch (error) {
    console.error('Error initializing communication form:', error)
  } finally {
    isInitializing.value = false
  }
})
</script>

<style scoped>
.communication-form {
  min-height: 100vh;
  background: oklch(var(--b2));
}

.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}
</style>
