<template>
  <div class="dashboard-view">
    <!-- Dashboard Navigation -->
    <div class="bg-base-100 border-b border-base-300">
      <div class="container mx-auto px-4">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-8">
            <h1 class="text-xl font-bold">HLenergy Dashboard</h1>
            <nav class="hidden md:flex space-x-6">
              <button 
                @click="activeTab = 'overview'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'overview' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                Overview
              </button>
              <button 
                @click="activeTab = 'analytics'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'analytics' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                Analytics
              </button>
              <button 
                @click="activeTab = 'leads'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'leads' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                Leads
              </button>
              <button
                @click="activeTab = 'reports'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'reports' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                Reports
              </button>
              <button
                @click="activeTab = 'crm'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'crm' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                CRM
              </button>
              <button
                @click="activeTab = 'cache'"
                :class="{ 'text-primary border-b-2 border-primary': activeTab === 'cache' }"
                class="py-2 px-1 text-sm font-medium transition-colors"
              >
                Cache
              </button>
            </nav>
          </div>
          
          <div class="flex items-center space-x-4">
            <div class="text-sm text-base-content/70">
              Welcome back, {{ authStore.user?.name }}
            </div>
            <div class="avatar">
              <div class="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center text-sm font-semibold">
                {{ authStore.userInitials }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="container mx-auto px-4 py-6">
      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-6">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="card bg-gradient-to-r from-primary to-primary-focus text-primary-content">
            <div class="card-body">
              <h3 class="card-title text-lg">Total Leads</h3>
              <div class="text-3xl font-bold">{{ totalLeads }}</div>
              <div class="text-sm opacity-80">This month</div>
            </div>
          </div>
          
          <div class="card bg-gradient-to-r from-secondary to-secondary-focus text-secondary-content">
            <div class="card-body">
              <h3 class="card-title text-lg">Conversion Rate</h3>
              <div class="text-3xl font-bold">{{ conversionRate.toFixed(1) }}%</div>
              <div class="text-sm opacity-80">Visitor to lead</div>
            </div>
          </div>
          
          <div class="card bg-gradient-to-r from-accent to-accent-focus text-accent-content">
            <div class="card-body">
              <h3 class="card-title text-lg">Active Users</h3>
              <div class="text-3xl font-bold">{{ totalUsers }}</div>
              <div class="text-sm opacity-80">Last 7 days</div>
            </div>
          </div>
          
          <div class="card bg-gradient-to-r from-success to-success-focus text-success-content">
            <div class="card-body">
              <h3 class="card-title text-lg">Qualified Leads</h3>
              <div class="text-3xl font-bold">{{ qualifiedLeads }}</div>
              <div class="text-sm opacity-80">Ready for contact</div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Recent Leads</h3>
              <div class="space-y-3">
                <div v-for="i in 5" :key="i" class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                  <div>
                    <div class="font-medium">Lead #{{ 1000 + i }}</div>
                    <div class="text-sm text-base-content/70">Energy Audit Inquiry</div>
                  </div>
                  <div class="text-right">
                    <div class="text-sm">{{ Math.floor(Math.random() * 24) }}h ago</div>
                    <div class="badge badge-primary badge-sm">New</div>
                  </div>
                </div>
              </div>
              <div class="card-actions justify-end mt-4">
                <button @click="activeTab = 'leads'" class="btn btn-primary btn-sm">View All Leads</button>
              </div>
            </div>
          </div>

          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Top Services</h3>
              <div class="space-y-3">
                <div v-for="service in topServices" :key="service.name" class="flex items-center justify-between">
                  <span class="font-medium">{{ service.name }}</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-20 bg-base-200 rounded-full h-2">
                      <div 
                        class="bg-primary h-2 rounded-full"
                        :style="{ width: `${service.percentage}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-bold">{{ service.count }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'">
        <AnalyticsDashboard />
      </div>

      <!-- Leads Tab -->
      <div v-if="activeTab === 'leads'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Lead Management</h2>
          <button class="btn btn-primary">Export Leads</button>
        </div>
        
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Service Interest</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="i in 10" :key="i">
                    <td class="font-medium">Lead {{ i }}</td>
                    <td>lead{{ i }}@example.com</td>
                    <td>Energy Audit</td>
                    <td>
                      <div class="badge badge-primary">New</div>
                    </td>
                    <td>{{ new Date().toLocaleDateString() }}</td>
                    <td>
                      <button class="btn btn-sm btn-ghost">View</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Business Reports</h2>
          <button @click="exportReport" class="btn btn-primary">Generate Report</button>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Monthly Performance</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-primary">{{ conversionRate.toFixed(1) }}%</div>
                <div class="text-lg">Average Conversion Rate</div>
                <div class="text-sm text-base-content/70 mt-2">
                  Based on last 30 days of data
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Revenue Forecast</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-success">${{ (totalLeads * 2500).toLocaleString() }}</div>
                <div class="text-lg">Potential Revenue</div>
                <div class="text-sm text-base-content/70 mt-2">
                  Estimated based on current leads
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CRM Tab -->
      <div v-if="activeTab === 'crm'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Customer Relationship Management</h2>
          <RouterLink to="/crm/dashboard" class="btn btn-primary">Open CRM</RouterLink>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <!-- CRM Quick Access -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Quick Access</h3>
              <div class="space-y-3">
                <RouterLink to="/crm/customers" class="btn btn-block btn-sm btn-primary">
                  👥 Manage Customers
                </RouterLink>
                <RouterLink to="/crm/projects" class="btn btn-block btn-sm btn-secondary">
                  📋 View Projects
                </RouterLink>
                <RouterLink to="/crm/communications" class="btn btn-block btn-sm btn-accent">
                  💬 Communication Hub
                </RouterLink>
                <RouterLink to="/crm/customers/new" class="btn btn-block btn-sm btn-info">
                  + Add New Customer
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- CRM Stats -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">CRM Overview</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span>Total Customers</span>
                  <span class="font-bold">{{ totalCustomers || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Active Projects</span>
                  <span class="font-bold">{{ activeProjects?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Unread Messages</span>
                  <span class="font-bold text-warning">{{ unreadCommunications?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Conversion Rate</span>
                  <span class="font-bold text-success">{{ conversionRate.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent CRM Activity -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Recent Activity</h3>
              <div class="space-y-3">
                <div class="text-sm text-base-content/70">
                  No recent CRM activity
                </div>
                <RouterLink to="/crm/communications" class="btn btn-sm btn-ghost">
                  View All Activity
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Tab -->
      <div v-if="activeTab === 'cache'">
        <CacheManagement />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useAnalyticsStore } from '@/stores/analytics'
import { useCRMStore } from '@/stores/crm'
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard.vue'
import CacheManagement from '@/components/CacheManagement.vue'

const authStore = useAuthStore()
const analyticsStore = useAnalyticsStore()
const crmStore = useCRMStore()

const activeTab = ref('overview')

const { totalUsers, conversionRate, totalLeads, qualifiedLeads } = analyticsStore
const { totalCustomers, activeProjects, unreadCommunications } = crmStore

const topServices = ref([
  { name: 'Energy Audit', count: 45, percentage: 90 },
  { name: 'Consultation', count: 32, percentage: 64 },
  { name: 'Implementation', count: 28, percentage: 56 },
  { name: 'Monitoring', count: 15, percentage: 30 },
  { name: 'Compliance', count: 12, percentage: 24 }
])

const exportReport = async () => {
  try {
    const blob = await analyticsStore.exportAnalyticsData('json')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `hlenergy-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export report:', error)
  }
}

onMounted(async () => {
  await analyticsStore.initializeAnalyticsStore()
})
</script>

<style scoped>
.dashboard-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
</style>
