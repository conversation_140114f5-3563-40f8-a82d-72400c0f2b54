<template>
  <div class="min-h-screen bg-base-200 flex items-center justify-center">
    <div class="card w-96 bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title justify-center text-2xl mb-6">{{ $t('auth.register') }}</h2>
        
        <form @submit.prevent="handleRegister" class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('contact.name') }}</span>
            </label>
            <input 
              type="text" 
              v-model="registerForm.name"
              class="input input-bordered w-full" 
              :placeholder="$t('contact.name')"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('auth.email') }}</span>
            </label>
            <input 
              type="email" 
              v-model="registerForm.email"
              class="input input-bordered w-full" 
              :placeholder="$t('auth.email')"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('auth.password') }}</span>
            </label>
            <input 
              type="password" 
              v-model="registerForm.password"
              class="input input-bordered w-full" 
              :placeholder="$t('auth.password')"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">{{ $t('auth.confirm_password') }}</span>
            </label>
            <input 
              type="password" 
              v-model="registerForm.confirmPassword"
              class="input input-bordered w-full" 
              :placeholder="$t('auth.confirm_password')"
              required
            />
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer justify-start">
              <input type="checkbox" v-model="registerForm.agreeToTerms" class="checkbox checkbox-primary" required />
              <span class="label-text ml-2">I agree to the Terms of Service</span>
            </label>
          </div>
          
          <div class="form-control mt-6">
            <button type="submit" class="btn btn-primary w-full">
              {{ $t('auth.register') }}
            </button>
          </div>
        </form>
        
        <div class="divider">OR</div>
        
        <div class="text-center">
          <p class="text-sm">
            {{ $t('auth.have_account') }}
            <RouterLink to="/login" class="link link-primary">{{ $t('auth.login') }}</RouterLink>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const registerForm = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

const handleRegister = () => {
  if (registerForm.password !== registerForm.confirmPassword) {
    alert('Passwords do not match!')
    return
  }
  
  // TODO: Implement actual registration logic
  console.log('Registration attempt:', registerForm)
  alert('Registration functionality will be implemented with backend!')
  
  // For now, just redirect to login
  router.push('/login')
}
</script>
