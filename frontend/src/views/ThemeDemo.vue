<template>
  <div class="theme-demo min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="hero bg-gradient-to-br from-primary to-secondary text-primary-content py-20">
      <div class="hero-content text-center">
        <div class="max-w-4xl">
          <h1 class="text-5xl font-bold mb-6 animate-fade-in">
            🎨 HLenergy Theme Showcase
          </h1>
          <p class="text-xl mb-8 opacity-90">
            Experience our beautiful custom themes designed with your brand colors
          </p>
          <div class="flex flex-wrap justify-center gap-4">
            <ThemeSwitcher />
            <div class="badge badge-lg bg-accent text-accent-content">
              Current: {{ currentTheme }}
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Color Palette Section -->
    <section class="py-16 bg-base-200">
      <div class="max-w-7xl mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12 text-base-content">
          Brand Color Palette
        </h2>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          <!-- Forest Green -->
          <div class="card bg-hlenergy-forest text-white shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-forest rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Forest</h3>
              <p class="text-sm opacity-80">#02342b</p>
            </div>
          </div>

          <!-- Gold -->
          <div class="card bg-hlenergy-gold text-hlenergy-forest shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-gold rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Gold</h3>
              <p class="text-sm opacity-80">#eaaa34</p>
            </div>
          </div>

          <!-- Teal -->
          <div class="card bg-hlenergy-teal text-white shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-teal rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Teal</h3>
              <p class="text-sm opacity-80">#12816c</p>
            </div>
          </div>

          <!-- Sage -->
          <div class="card bg-hlenergy-sage text-white shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-sage rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Sage</h3>
              <p class="text-sm opacity-80">#5cad64</p>
            </div>
          </div>

          <!-- Mint -->
          <div class="card bg-hlenergy-mint text-white shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-mint rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Mint</h3>
              <p class="text-sm opacity-80">#389868</p>
            </div>
          </div>

          <!-- Lime -->
          <div class="card bg-hlenergy-lime text-hlenergy-forest shadow-xl">
            <div class="card-body p-6 text-center">
              <div class="w-16 h-16 bg-hlenergy-lime rounded-full mx-auto mb-4 border-4 border-white shadow-lg"></div>
              <h3 class="font-bold">Lime</h3>
              <p class="text-sm opacity-80">#7fbf60</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Component Showcase -->
    <section class="py-16 bg-base-100">
      <div class="max-w-7xl mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12 text-base-content">
          Component Showcase
        </h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Buttons -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h3 class="card-title text-primary mb-6">Buttons</h3>
              <div class="flex flex-wrap gap-3">
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-accent">Accent</button>
                <button class="btn btn-neutral">Neutral</button>
                <button class="btn btn-info">Info</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-error">Error</button>
              </div>
            </div>
          </div>

          <!-- Alerts -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h3 class="card-title text-primary mb-6">Alerts</h3>
              <div class="space-y-4">
                <div class="card bg-gradient-to-br from-info/10 to-info/5 border border-info/20 shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center gap-3">
                      <div class="p-2 bg-info/20 rounded-lg">
                        <Icon name="info" size="sm" class="text-info" />
                      </div>
                      <span class="font-medium text-info">Information alert</span>
                    </div>
                  </div>
                </div>
                <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20 shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center gap-3">
                      <div class="p-2 bg-success/20 rounded-lg">
                        <Icon name="check" size="sm" class="text-success" />
                      </div>
                      <span class="font-medium text-success">Success alert</span>
                    </div>
                  </div>
                </div>
                <div class="card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20 shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center gap-3">
                      <div class="p-2 bg-warning/20 rounded-lg">
                        <Icon name="warning" size="sm" class="text-warning" />
                      </div>
                      <span class="font-medium text-warning">Warning alert</span>
                    </div>
                  </div>
                </div>
                <div class="card bg-gradient-to-br from-error/10 to-error/5 border border-error/20 shadow-lg">
                  <div class="card-body p-4">
                    <div class="flex items-center gap-3">
                      <div class="p-2 bg-error/20 rounded-lg">
                        <Icon name="x" size="sm" class="text-error" />
                      </div>
                      <span class="font-medium text-error">Error alert</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Cards -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h3 class="card-title text-primary mb-6">Cards</h3>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div class="card bg-primary text-primary-content shadow-lg">
                  <div class="card-body p-4">
                    <h4 class="card-title text-sm">Primary Card</h4>
                    <p class="text-xs opacity-80">Sample content</p>
                  </div>
                </div>
                <div class="card bg-secondary text-secondary-content shadow-lg">
                  <div class="card-body p-4">
                    <h4 class="card-title text-sm">Secondary Card</h4>
                    <p class="text-xs opacity-80">Sample content</p>
                  </div>
                </div>
                <div class="card bg-accent text-accent-content shadow-lg">
                  <div class="card-body p-4">
                    <h4 class="card-title text-sm">Accent Card</h4>
                    <p class="text-xs opacity-80">Sample content</p>
                  </div>
                </div>
                <div class="card bg-neutral text-neutral-content shadow-lg">
                  <div class="card-body p-4">
                    <h4 class="card-title text-sm">Neutral Card</h4>
                    <p class="text-xs opacity-80">Sample content</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Stats -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h3 class="card-title text-primary mb-6">Statistics</h3>
              <div class="stats stats-vertical lg:stats-horizontal shadow">
                <div class="stat">
                  <div class="stat-figure text-primary">
                    <Icon name="chart-bar" size="lg" />
                  </div>
                  <div class="stat-title">Total Users</div>
                  <div class="stat-value text-primary">31K</div>
                  <div class="stat-desc">21% more than last month</div>
                </div>
                
                <div class="stat">
                  <div class="stat-figure text-secondary">
                    <Icon name="users" size="lg" />
                  </div>
                  <div class="stat-title">New Users</div>
                  <div class="stat-value text-secondary">4,200</div>
                  <div class="stat-desc">↗︎ 400 (22%)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Theme Comparison -->
    <section class="py-16 bg-base-200">
      <div class="max-w-7xl mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12 text-base-content">
          Theme Comparison
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Light Theme Preview -->
          <div class="card bg-white shadow-xl border">
            <div class="card-body">
              <h3 class="card-title text-[#12816c] mb-4">
                <Icon name="sun" size="md" class="text-[#eaaa34]" />
                HLenergy Light
              </h3>
              <div class="space-y-3">
                <div class="bg-[#f8fffe] p-3 rounded-lg">
                  <p class="text-[#02342b] text-sm">Clean, professional design perfect for daytime use</p>
                </div>
                <div class="flex gap-2">
                  <div class="badge bg-[#12816c] text-white">Primary</div>
                  <div class="badge bg-[#389868] text-white">Secondary</div>
                  <div class="badge bg-[#eaaa34] text-[#02342b]">Accent</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dark Theme Preview -->
          <div class="card bg-[#0a1f1b] shadow-xl border border-[#1a3d35]">
            <div class="card-body">
              <h3 class="card-title text-[#5cad64] mb-4">
                <Icon name="moon" size="md" class="text-[#eaaa34]" />
                HLenergy Dark
              </h3>
              <div class="space-y-3">
                <div class="bg-[#0f2a24] p-3 rounded-lg">
                  <p class="text-[#e8f5f2] text-sm">Elegant dark design that's easy on the eyes</p>
                </div>
                <div class="flex gap-2">
                  <div class="badge bg-[#5cad64] text-white">Primary</div>
                  <div class="badge bg-[#7fbf60] text-[#02342b]">Secondary</div>
                  <div class="badge bg-[#eaaa34] text-[#02342b]">Accent</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Back to App -->
    <section class="py-16 bg-base-100">
      <div class="max-w-4xl mx-auto px-6 text-center">
        <h2 class="text-3xl font-bold mb-6 text-base-content">
          Ready to Experience HLenergy?
        </h2>
        <p class="text-lg mb-8 text-base-content/70">
          Choose your preferred theme and start exploring our energy consultation services
        </p>
        <div class="flex flex-wrap justify-center gap-4">
          <RouterLink to="/" class="btn btn-primary btn-lg">
            <Icon name="home" size="sm" class="mr-2" />
            Back to Home
          </RouterLink>
          <RouterLink to="/dashboard" class="btn btn-secondary btn-lg">
            <Icon name="chart-bar" size="sm" class="mr-2" />
            Go to Dashboard
          </RouterLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import Icon from '@/components/common/Icon.vue'

// Get current theme from document
const currentTheme = computed(() => {
  return document.documentElement.getAttribute('data-theme') || 'hlenergy-light'
})
</script>

<style scoped>
.theme-demo {
  transition: all 0.3s ease-in-out;
}

/* Custom gradient backgrounds */
.hero {
  background: linear-gradient(135deg, hsl(var(--p)) 0%, hsl(var(--s)) 100%);
}

/* Enhanced card hover effects */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease-in-out;
}

/* Color palette cards */
.card-body .w-16.h-16 {
  transition: transform 0.2s ease-in-out;
}

.card:hover .w-16.h-16 {
  transform: scale(1.1);
}

/* Stats styling */
.stats {
  background: hsl(var(--b1));
  border: 1px solid hsl(var(--b3));
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .grid.grid-cols-6 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
