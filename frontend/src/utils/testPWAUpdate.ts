/**
 * PWA Update Testing Utilities
 * Provides functions to test update prompts and functionality
 */

// Simulate PWA update events for testing
export function simulatePWAUpdate() {
  console.log('🔄 Simulating PWA update...')
  
  // Dispatch a custom event to trigger update prompt
  const updateEvent = new CustomEvent('pwa-update-available', {
    detail: {
      type: 'update',
      message: 'A new version is available'
    }
  })
  
  window.dispatchEvent(updateEvent)
}

// Simulate offline ready event
export function simulateOfflineReady() {
  console.log('✅ Simulating offline ready...')
  
  const offlineEvent = new CustomEvent('pwa-offline-ready', {
    detail: {
      type: 'offline-ready',
      message: 'App is ready to work offline'
    }
  })
  
  window.dispatchEvent(offlineEvent)
}

// Test update prompt UI
export function testUpdatePromptUI() {
  console.group('🧪 Testing PWA Update Prompt UI')
  
  // Check if update prompt elements exist
  const updatePrompts = document.querySelectorAll('[class*="update"]')
  console.log(`Found ${updatePrompts.length} update-related elements`)
  
  // Check for close buttons
  const closeButtons = document.querySelectorAll('button[title*="Dismiss"], button[title*="dismiss"], button:contains("✕")')
  console.log(`Found ${closeButtons.length} close buttons`)
  
  // Check for update buttons
  const updateButtons = document.querySelectorAll('button:contains("Update"), button:contains("update")')
  console.log(`Found ${updateButtons.length} update buttons`)
  
  // Test button functionality
  closeButtons.forEach((button, index) => {
    console.log(`Close button ${index + 1}:`, {
      text: button.textContent?.trim(),
      title: button.getAttribute('title'),
      classes: button.className,
      clickable: !button.hasAttribute('disabled')
    })
  })
  
  updateButtons.forEach((button, index) => {
    console.log(`Update button ${index + 1}:`, {
      text: button.textContent?.trim(),
      classes: button.className,
      clickable: !button.hasAttribute('disabled')
    })
  })
  
  console.groupEnd()
}

// Test layout and positioning
export function testUpdatePromptLayout() {
  console.group('📐 Testing PWA Update Prompt Layout')
  
  // Check for overlapping elements
  const fixedElements = document.querySelectorAll('.fixed, [style*="position: fixed"]')
  console.log(`Found ${fixedElements.length} fixed positioned elements`)
  
  fixedElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect()
    const styles = window.getComputedStyle(element)
    
    console.log(`Fixed element ${index + 1}:`, {
      tag: element.tagName,
      classes: element.className,
      position: {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      },
      zIndex: styles.zIndex,
      visible: rect.width > 0 && rect.height > 0
    })
  })
  
  // Check for container overflow
  const containers = document.querySelectorAll('.container, .max-w-7xl, .max-w-4xl')
  containers.forEach((container, index) => {
    const rect = container.getBoundingClientRect()
    const hasOverflow = rect.width > window.innerWidth
    
    if (hasOverflow) {
      console.warn(`Container ${index + 1} overflows viewport:`, {
        containerWidth: rect.width,
        viewportWidth: window.innerWidth,
        overflow: rect.width - window.innerWidth
      })
    }
  })
  
  console.groupEnd()
}

// Comprehensive PWA update test
export function runPWAUpdateTests() {
  console.group('🚀 PWA Update Tests')
  
  console.log('Running comprehensive PWA update tests...')
  
  // Test 1: UI Elements
  testUpdatePromptUI()
  
  // Test 2: Layout
  testUpdatePromptLayout()
  
  // Test 3: Functionality
  console.group('⚙️ Testing Functionality')
  
  // Check if PWA composables are available
  try {
    // This would need to be called from within a Vue component
    console.log('✅ PWA composables should be tested from within Vue components')
  } catch (error) {
    console.error('❌ PWA composables not available:', error)
  }
  
  console.groupEnd()
  
  // Test 4: Accessibility
  console.group('♿ Testing Accessibility')
  
  const closeButtons = document.querySelectorAll('button[title*="Dismiss"], button[title*="dismiss"]')
  closeButtons.forEach((button, index) => {
    const hasTitle = button.hasAttribute('title')
    const hasAriaLabel = button.hasAttribute('aria-label')
    const hasAccessibleText = button.textContent?.trim() || hasTitle || hasAriaLabel
    
    console.log(`Close button ${index + 1} accessibility:`, {
      hasTitle,
      hasAriaLabel,
      hasAccessibleText,
      accessible: hasAccessibleText
    })
  })
  
  console.groupEnd()
  
  console.log('✅ PWA update tests completed')
  console.groupEnd()
}

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Add test commands to window for manual testing
  Object.assign(window, {
    testPWAUpdate: runPWAUpdateTests,
    simulatePWAUpdate,
    simulateOfflineReady,
    testUpdatePromptUI,
    testUpdatePromptLayout
  })
  
  console.log('🧪 PWA Update test utilities loaded. Available commands:')
  console.log('- testPWAUpdate() - Run all tests')
  console.log('- simulatePWAUpdate() - Simulate update available')
  console.log('- simulateOfflineReady() - Simulate offline ready')
  console.log('- testUpdatePromptUI() - Test UI elements')
  console.log('- testUpdatePromptLayout() - Test layout')
}
