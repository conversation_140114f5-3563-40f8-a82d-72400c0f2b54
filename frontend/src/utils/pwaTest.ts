/**
 * PWA Testing Utilities
 * Provides functions to test and validate PWA functionality
 */

export interface PWATestResult {
  feature: string
  supported: boolean
  status: 'pass' | 'fail' | 'warning'
  message: string
}

export class PWATestSuite {
  private results: PWATestResult[] = []

  /**
   * Run all PWA tests
   */
  async runAllTests(): Promise<PWATestResult[]> {
    this.results = []
    
    // Test basic PWA support
    this.testServiceWorkerSupport()
    this.testManifestSupport()
    this.testNotificationSupport()
    this.testCacheAPISupport()
    this.testInstallPromptSupport()
    
    // Test advanced features
    this.testOfflineSupport()
    await this.testCacheEffectiveness()
    this.testPushNotificationSupport()
    
    return this.results
  }

  /**
   * Test Service Worker support
   */
  private testServiceWorkerSupport(): void {
    const supported = 'serviceWorker' in navigator
    this.addResult({
      feature: 'Service Worker',
      supported,
      status: supported ? 'pass' : 'fail',
      message: supported 
        ? 'Service Worker API is supported' 
        : 'Service Worker API is not supported'
    })
  }

  /**
   * Test Web App Manifest support
   */
  private testManifestSupport(): void {
    const manifestLink = document.querySelector('link[rel="manifest"]')
    const supported = !!manifestLink
    
    this.addResult({
      feature: 'Web App Manifest',
      supported,
      status: supported ? 'pass' : 'fail',
      message: supported 
        ? 'Web App Manifest is present' 
        : 'Web App Manifest is missing'
    })
  }

  /**
   * Test Notification API support
   */
  private testNotificationSupport(): void {
    const supported = 'Notification' in window
    const permission = supported ? Notification.permission : 'unsupported'
    
    let status: 'pass' | 'fail' | 'warning' = 'fail'
    let message = 'Notification API is not supported'
    
    if (supported) {
      if (permission === 'granted') {
        status = 'pass'
        message = 'Notifications are enabled'
      } else if (permission === 'default') {
        status = 'warning'
        message = 'Notifications permission not requested'
      } else {
        status = 'warning'
        message = 'Notifications are blocked'
      }
    }

    this.addResult({
      feature: 'Push Notifications',
      supported,
      status,
      message
    })
  }

  /**
   * Test Cache API support
   */
  private testCacheAPISupport(): void {
    const supported = 'caches' in window
    this.addResult({
      feature: 'Cache API',
      supported,
      status: supported ? 'pass' : 'fail',
      message: supported 
        ? 'Cache API is supported' 
        : 'Cache API is not supported'
    })
  }

  /**
   * Test Install Prompt support
   */
  private testInstallPromptSupport(): void {
    // This is harder to test directly, so we check for related APIs
    const supported = 'BeforeInstallPromptEvent' in window || 
                     'onbeforeinstallprompt' in window
    
    this.addResult({
      feature: 'Install Prompt',
      supported,
      status: supported ? 'pass' : 'warning',
      message: supported 
        ? 'Install prompt API is supported' 
        : 'Install prompt may not be available'
    })
  }

  /**
   * Test offline support
   */
  private testOfflineSupport(): void {
    const supported = 'navigator' in window && 'onLine' in navigator
    const isOnline = supported ? navigator.onLine : true
    
    this.addResult({
      feature: 'Offline Detection',
      supported,
      status: supported ? 'pass' : 'fail',
      message: supported 
        ? `Offline detection works (currently ${isOnline ? 'online' : 'offline'})` 
        : 'Offline detection is not supported'
    })
  }

  /**
   * Test cache effectiveness
   */
  private async testCacheEffectiveness(): Promise<void> {
    if (!('caches' in window)) {
      this.addResult({
        feature: 'Cache Effectiveness',
        supported: false,
        status: 'fail',
        message: 'Cache API not available'
      })
      return
    }

    try {
      const cacheNames = await caches.keys()
      const totalCaches = cacheNames.length
      
      let totalCachedItems = 0
      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName)
        const keys = await cache.keys()
        totalCachedItems += keys.length
      }

      const status = totalCachedItems > 0 ? 'pass' : 'warning'
      const message = totalCachedItems > 0 
        ? `${totalCachedItems} items cached across ${totalCaches} caches`
        : 'No items found in cache'

      this.addResult({
        feature: 'Cache Effectiveness',
        supported: true,
        status,
        message
      })
    } catch (error) {
      this.addResult({
        feature: 'Cache Effectiveness',
        supported: true,
        status: 'fail',
        message: `Cache test failed: ${error}`
      })
    }
  }

  /**
   * Test Push Notification support
   */
  private testPushNotificationSupport(): void {
    const swSupported = 'serviceWorker' in navigator
    const pushSupported = 'PushManager' in window
    const notificationSupported = 'Notification' in window
    
    const supported = swSupported && pushSupported && notificationSupported
    
    let message = 'Push notifications are fully supported'
    if (!swSupported) message = 'Service Worker not supported'
    else if (!pushSupported) message = 'PushManager not supported'
    else if (!notificationSupported) message = 'Notification API not supported'

    this.addResult({
      feature: 'Push Notifications',
      supported,
      status: supported ? 'pass' : 'fail',
      message
    })
  }

  /**
   * Add a test result
   */
  private addResult(result: PWATestResult): void {
    this.results.push(result)
  }

  /**
   * Get test summary
   */
  getTestSummary(): { total: number, passed: number, failed: number, warnings: number } {
    const total = this.results.length
    const passed = this.results.filter(r => r.status === 'pass').length
    const failed = this.results.filter(r => r.status === 'fail').length
    const warnings = this.results.filter(r => r.status === 'warning').length

    return { total, passed, failed, warnings }
  }

  /**
   * Generate test report
   */
  generateReport(): string {
    const summary = this.getTestSummary()
    let report = `PWA Test Report\n`
    report += `===============\n\n`
    report += `Summary: ${summary.passed}/${summary.total} tests passed\n`
    report += `Warnings: ${summary.warnings}\n`
    report += `Failed: ${summary.failed}\n\n`
    
    report += `Detailed Results:\n`
    report += `-----------------\n`
    
    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'
      report += `${icon} ${result.feature}: ${result.message}\n`
    })

    return report
  }
}

/**
 * Quick PWA test function
 */
export async function testPWAFeatures(): Promise<PWATestResult[]> {
  const testSuite = new PWATestSuite()
  return await testSuite.runAllTests()
}

/**
 * Log PWA test results to console
 */
export async function logPWATestResults(): Promise<void> {
  console.group('🚀 PWA Feature Test Results')
  
  const testSuite = new PWATestSuite()
  const results = await testSuite.runAllTests()
  const summary = testSuite.getTestSummary()
  
  console.log(`📊 Summary: ${summary.passed}/${summary.total} tests passed`)
  console.log(`⚠️ Warnings: ${summary.warnings}`)
  console.log(`❌ Failed: ${summary.failed}`)
  console.log('')
  
  results.forEach(result => {
    const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'
    console.log(`${icon} ${result.feature}: ${result.message}`)
  })
  
  console.groupEnd()
  
  // Also log the full report
  console.log('\n' + testSuite.generateReport())
}

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Run tests after a short delay to ensure everything is loaded
  setTimeout(() => {
    logPWATestResults()
  }, 2000)
}
