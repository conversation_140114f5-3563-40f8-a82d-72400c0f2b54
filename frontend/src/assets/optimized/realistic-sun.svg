<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Core sun gradient - simplified -->
    <radialGradient id="sunCore" cx="200" cy="200" r="120" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="60%" stop-color="#FFEE80" />
      <stop offset="100%" stop-color="#FF9E00" />
    </radialGradient>

    <!-- Outer glow gradient - simplified -->
    <radialGradient id="sunGlow" cx="200" cy="200" r="200" gradientUnits="userSpaceOnUse">
      <stop offset="50%" stop-color="#FFCC00" stop-opacity="0.7" />
      <stop offset="100%" stop-color="#FF6D00" stop-opacity="0" />
    </radialGradient>
  </defs>

  <!-- Outer glow -->
  <circle cx="200" cy="200" r="180" fill="url(#sunGlow)" />

  <!-- Main sun body - simplified -->
  <circle cx="200" cy="200" r="120" fill="url(#sunCore)" />

  <!-- Subtle surface details - reduced -->
  <g opacity="0.4">
    <ellipse cx="170" cy="170" rx="35" ry="25" fill="#FFFFFF" />
    <ellipse cx="230" cy="220" rx="30" ry="20" fill="#FFFFFF" />
  </g>

  <!-- Subtle lens flare effect -->
  <circle cx="200" cy="200" r="15" fill="#FFFFFF" opacity="0.7" />
</svg>
