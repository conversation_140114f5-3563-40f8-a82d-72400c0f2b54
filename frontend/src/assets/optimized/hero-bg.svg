<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="1080" viewBox="0 0 1920 1080">
  <defs>
    <linearGradient id="hero-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#02342b" />
      <stop offset="50%" stop-color="#12816c" />
      <stop offset="100%" stop-color="#eaaa34" />
    </linearGradient>
    <pattern id="pattern-grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255, 255, 255, 0.1)" stroke-width="1" />
    </pattern>
  </defs>

  <!-- Background -->
  <rect width="1920" height="1080" fill="url(#hero-gradient)" />

  <!-- Grid pattern overlay -->
  <rect width="1920" height="1080" fill="url(#pattern-grid)" />

  <!-- Abstract shapes -->
  <circle cx="300" cy="300" r="200" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="1600" cy="800" r="250" fill="rgba(255, 255, 255, 0.05)" />

  <!-- Energy-related icons -->
  <g transform="translate(960, 540) scale(5)" fill="rgba(255, 255, 255, 0.2)">
    <path d="M0 -20 L5 -10 L-5 -10 L0 0 L5 10 L-5 10 L0 20 Z" />
  </g>
</svg>
