import { apiService, type PaginatedResponse } from './api'

// CRM Types
export interface Customer {
  id: number
  firstName: string
  lastName: string
  email: string
  phone?: string
  companyName?: string
  jobTitle?: string
  industry?: string
  companySize?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+'
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  leadSource?: string
  leadScore: number
  status: 'lead' | 'prospect' | 'customer' | 'inactive'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  energyNeeds?: any
  currentEnergyProvider?: string
  monthlyEnergyBudget?: number
  assignedTo?: number
  tags?: string[]
  notes?: string
  firstContactDate?: string
  lastContactDate?: string
  nextFollowUpDate?: string
  estimatedValue?: number
  actualValue?: number
  conversionDate?: string
  preferredContactMethod: 'email' | 'phone' | 'text' | 'in_person'
  communicationFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  isActive: boolean
  createdAt: string
  updatedAt: string
  assignedUser?: {
    id: number
    name: string
    email: string
  }
  projects?: Project[]
  communications?: Communication[]
}

export interface Project {
  id: number
  projectNumber: string
  title: string
  description?: string
  customerId: number
  assignedTo?: number
  teamMembers?: number[]
  type: 'energy_audit' | 'consultation' | 'implementation' | 'monitoring' | 'compliance' | 'financing' | 'maintenance'
  category: 'residential' | 'commercial' | 'industrial' | 'municipal'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'planning' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled' | 'archived'
  startDate?: string
  endDate?: string
  estimatedDuration?: number
  actualDuration?: number
  estimatedCost?: number
  actualCost?: number
  budget?: number
  currency: string
  currentEnergyUsage?: any
  projectedSavings?: any
  actualSavings?: any
  siteAddress?: string
  siteCity?: string
  siteState?: string
  siteZipCode?: string
  siteCountry?: string
  scope?: any
  requirements?: any
  deliverables?: any
  completionPercentage: number
  milestones?: any[]
  risks?: any[]
  issues?: any[]
  notes?: string
  tags?: string[]
  qualityScore?: number
  clientSatisfaction?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  customer?: Customer
  assignedUser?: {
    id: number
    name: string
    email: string
  }
}

export interface Communication {
  id: number
  customerId: number
  projectId?: number
  userId: number
  type: 'email' | 'phone_call' | 'meeting' | 'video_call' | 'text_message' | 'in_person' | 'note' | 'task' | 'reminder'
  direction: 'inbound' | 'outbound' | 'internal'
  subject?: string
  content?: string
  summary?: string
  scheduledAt?: string
  occurredAt?: string
  duration?: number
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_response' | 'failed'
  outcome?: 'successful' | 'follow_up_needed' | 'not_interested' | 'callback_requested' | 'meeting_scheduled' | 'proposal_requested' | 'closed_deal' | 'lost_opportunity'
  contactMethod?: string
  participants?: any[]
  followUpRequired: boolean
  followUpDate?: string
  followUpNotes?: string
  attachments?: any[]
  relatedDocuments?: any[]
  tags?: string[]
  category?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  sentiment?: 'positive' | 'neutral' | 'negative'
  qualityRating?: number
  isRead: boolean
  readAt?: string
  createdAt: string
  updatedAt: string
  customer?: Customer
  project?: Project
  user?: {
    id: number
    name: string
    email: string
  }
}

export interface Document {
  id: number
  filename: string
  originalName: string
  mimeType: string
  fileSize: number
  filePath: string
  customerId?: number
  projectId?: number
  communicationId?: number
  uploadedBy: number
  type: 'contract' | 'proposal' | 'report' | 'invoice' | 'receipt' | 'energy_audit' | 'technical_spec' | 'compliance_doc' | 'correspondence' | 'presentation' | 'image' | 'other'
  category?: string
  title?: string
  description?: string
  version: string
  parentDocumentId?: number
  isLatestVersion: boolean
  visibility: 'private' | 'team' | 'customer' | 'public'
  accessLevel: 'view' | 'download' | 'edit' | 'admin'
  sharedWith?: number[]
  status: 'draft' | 'review' | 'approved' | 'archived' | 'deleted'
  tags?: string[]
  downloadCount: number
  lastAccessedAt?: string
  expiresAt?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  uploader?: {
    id: number
    name: string
    email: string
  }
}

export interface CustomerListParams {
  page?: number
  limit?: number
  status?: string
  priority?: string
  assignedTo?: number
  leadSource?: string
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ProjectListParams {
  page?: number
  limit?: number
  customerId?: number
  status?: string
  type?: string
  priority?: string
  assignedTo?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface CommunicationListParams {
  page?: number
  limit?: number
  customerId?: number
  projectId?: number
  type?: string
  status?: string
  userId?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// CRM Service
class CRMService {
  // Customer Management
  async getCustomers(params: CustomerListParams = {}): Promise<PaginatedResponse<Customer>> {
    return await apiService.get<PaginatedResponse<Customer>>('/crm/customers', params)
  }

  async getCustomer(id: number): Promise<Customer> {
    return await apiService.get<Customer>(`/crm/customers/${id}`)
  }

  async createCustomer(customerData: Partial<Customer>): Promise<Customer> {
    return await apiService.post<Customer>('/crm/customers', customerData)
  }

  async updateCustomer(id: number, customerData: Partial<Customer>): Promise<Customer> {
    return await apiService.put<Customer>(`/crm/customers/${id}`, customerData)
  }

  async deleteCustomer(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/crm/customers/${id}`)
  }

  async convertToCustomer(id: number, conversionData?: any): Promise<Customer> {
    return await apiService.post<Customer>(`/crm/customers/${id}/convert`, conversionData)
  }

  async getCustomerStats(timeRange: string = '30d'): Promise<any> {
    return await apiService.get(`/crm/customers/stats?timeRange=${timeRange}`)
  }

  // Project Management
  async getProjects(params: ProjectListParams = {}): Promise<PaginatedResponse<Project>> {
    return await apiService.get<PaginatedResponse<Project>>('/crm/projects', params)
  }

  async getProject(id: number): Promise<Project> {
    return await apiService.get<Project>(`/crm/projects/${id}`)
  }

  async createProject(projectData: Partial<Project>): Promise<Project> {
    return await apiService.post<Project>('/crm/projects', projectData)
  }

  async updateProject(id: number, projectData: Partial<Project>): Promise<Project> {
    return await apiService.put<Project>(`/crm/projects/${id}`, projectData)
  }

  async deleteProject(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/crm/projects/${id}`)
  }

  async getProjectStats(timeRange: string = '30d'): Promise<any> {
    return await apiService.get(`/crm/projects/stats?timeRange=${timeRange}`)
  }

  // Communication Management
  async getCommunications(params: CommunicationListParams = {}): Promise<PaginatedResponse<Communication>> {
    return await apiService.get<PaginatedResponse<Communication>>('/crm/communications', params)
  }

  async getCommunication(id: number): Promise<Communication> {
    return await apiService.get<Communication>(`/crm/communications/${id}`)
  }

  async createCommunication(communicationData: Partial<Communication>): Promise<Communication> {
    return await apiService.post<Communication>('/crm/communications', communicationData)
  }

  async updateCommunication(id: number, communicationData: Partial<Communication>): Promise<Communication> {
    return await apiService.put<Communication>(`/crm/communications/${id}`, communicationData)
  }

  async deleteCommunication(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/crm/communications/${id}`)
  }

  async markAsRead(id: number): Promise<Communication> {
    return await apiService.post<Communication>(`/crm/communications/${id}/read`)
  }

  // Document Management
  async getDocuments(params: any = {}): Promise<PaginatedResponse<Document>> {
    return await apiService.get<PaginatedResponse<Document>>('/crm/documents', params)
  }

  async getDocument(id: number): Promise<Document> {
    return await apiService.get<Document>(`/crm/documents/${id}`)
  }

  async uploadDocument(file: File, metadata: Partial<Document>): Promise<Document> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('metadata', JSON.stringify(metadata))

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/crm/documents/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Upload failed')
    }

    const result = await response.json()
    return result.data
  }

  async updateDocument(id: number, documentData: Partial<Document>): Promise<Document> {
    return await apiService.put<Document>(`/crm/documents/${id}`, documentData)
  }

  async deleteDocument(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/crm/documents/${id}`)
  }

  async downloadDocument(id: number): Promise<Blob> {
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/v1/crm/documents/${id}/download`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Download failed')
    }

    return await response.blob()
  }

  // Dashboard & Analytics
  async getCRMDashboard(): Promise<any> {
    return await apiService.get('/crm/dashboard')
  }

  async getCRMAnalytics(timeRange: string = '30d'): Promise<any> {
    return await apiService.get(`/crm/analytics?timeRange=${timeRange}`)
  }
}

// Export singleton instance
export const crmService = new CRMService()
export default crmService
