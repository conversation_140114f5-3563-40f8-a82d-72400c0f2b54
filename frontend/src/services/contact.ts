import { apiService, type PaginatedResponse } from './api'

// Contact Types
export interface ContactSubmission {
  id?: number
  name: string
  email: string
  phone?: string
  message: string
  status?: 'new' | 'in_progress' | 'resolved' | 'closed'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  source?: string
  created_at?: string
  updated_at?: string
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  message: string
  source?: string
}

export interface ContactResponse {
  submission: ContactSubmission
  emailQueued: boolean
  emailId?: number
  message: string
}

export interface ContactListParams {
  page?: number
  limit?: number
  status?: string
  priority?: string
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Contact Service
class ContactService {
  // Submit contact form
  async submitContactForm(formData: ContactFormData): Promise<ContactResponse> {
    return await apiService.post<ContactResponse>('/contact/submit', {
      ...formData,
      source: formData.source || 'website',
    })
  }

  // Get contact submissions (admin only)
  async getContactSubmissions(params: ContactListParams = {}): Promise<PaginatedResponse<ContactSubmission>> {
    return await apiService.get<PaginatedResponse<ContactSubmission>>('/contact/submissions', params)
  }

  // Get specific contact submission (admin only)
  async getContactSubmission(id: number): Promise<ContactSubmission> {
    return await apiService.get<ContactSubmission>(`/contact/submissions/${id}`)
  }

  // Update contact submission status (admin only)
  async updateContactSubmission(
    id: number,
    updates: Partial<ContactSubmission>
  ): Promise<ContactSubmission> {
    return await apiService.put<ContactSubmission>(`/contact/submissions/${id}`, updates)
  }

  // Delete contact submission (admin only)
  async deleteContactSubmission(id: number): Promise<{ message: string }> {
    return await apiService.delete<{ message: string }>(`/contact/submissions/${id}`)
  }

  // Get contact statistics (admin only)
  async getContactStats(): Promise<{
    total: number
    byStatus: Record<string, number>
    byPriority: Record<string, number>
    recent: number
    responseTime: {
      average: number
      median: number
    }
  }> {
    return await apiService.get('/contact/stats')
  }

  // Export contact submissions (admin only)
  async exportContactSubmissions(params: ContactListParams = {}): Promise<Blob> {
    const response = await apiService.getRaw('/contact/export', params)
    // Handle blob response for CSV/Excel export
    return new Blob([JSON.stringify(response.data)], { type: 'application/json' })
  }

  // Bulk update contact submissions (admin only)
  async bulkUpdateSubmissions(
    ids: number[],
    updates: Partial<ContactSubmission>
  ): Promise<{ updated: number; message: string }> {
    return await apiService.post('/contact/bulk-update', {
      ids,
      updates,
    })
  }

  // Add note to contact submission (admin only)
  async addNote(
    id: number,
    note: string
  ): Promise<{ message: string }> {
    return await apiService.post(`/contact/submissions/${id}/notes`, {
      note,
    })
  }

  // Assign contact submission to user (admin only)
  async assignSubmission(
    id: number,
    assignedTo: number
  ): Promise<{ message: string }> {
    return await apiService.post(`/contact/submissions/${id}/assign`, {
      assignedTo,
    })
  }
}

// Export singleton instance
export const contactService = new ContactService()
export default contactService
