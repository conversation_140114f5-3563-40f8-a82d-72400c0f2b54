/**
 * Biometric Authentication Service
 * Handles WebAuthn API for fingerprint and Face ID authentication
 */

export interface BiometricCredential {
  id: string
  type: 'public-key'
  rawId: ArrayBuffer
  response: AuthenticatorAttestationResponse | AuthenticatorAssertionResponse
}

export interface BiometricRegistrationOptions {
  challenge: Uint8Array
  rp: {
    name: string
    id: string
  }
  user: {
    id: Uint8Array
    name: string
    displayName: string
  }
  pubKeyCredParams: PublicKeyCredentialParameters[]
  authenticatorSelection?: AuthenticatorSelectionCriteria
  timeout?: number
  attestation?: AttestationConveyancePreference
}

export interface BiometricAuthenticationOptions {
  challenge: Uint8Array
  allowCredentials?: PublicKeyCredentialDescriptor[]
  timeout?: number
  userVerification?: UserVerificationRequirement
}

class BiometricService {
  private isSupported: boolean = false

  constructor() {
    this.checkSupport()
  }

  /**
   * Check if biometric authentication is supported
   */
  private checkSupport(): void {
    this.isSupported = !!(
      window.PublicKeyCredential &&
      navigator.credentials &&
      navigator.credentials.create &&
      navigator.credentials.get
    )
  }

  /**
   * Check if biometric authentication is available
   */
  async isAvailable(): Promise<boolean> {
    if (!this.isSupported) {
      return false
    }

    try {
      // Check if platform authenticator is available
      const available = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
      return available
    } catch (error) {
      console.warn('Error checking biometric availability:', error)
      return false
    }
  }

  /**
   * Get supported authenticator types
   */
  async getSupportedAuthenticators(): Promise<string[]> {
    const supported: string[] = []

    if (!this.isSupported) {
      return supported
    }

    try {
      // Check for platform authenticator (Touch ID, Face ID, Windows Hello)
      const platformAvailable = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
      if (platformAvailable) {
        supported.push('platform')
      }

      // Cross-platform authenticators are generally supported if WebAuthn is available
      if (this.isSupported) {
        supported.push('cross-platform')
      }
    } catch (error) {
      console.warn('Error checking authenticator support:', error)
    }

    return supported
  }

  /**
   * Register a new biometric credential
   */
  async register(options: Partial<BiometricRegistrationOptions> = {}): Promise<BiometricCredential> {
    if (!this.isSupported) {
      throw new Error('Biometric authentication is not supported on this device')
    }

    const defaultOptions: BiometricRegistrationOptions = {
      challenge: this.generateChallenge(),
      rp: {
        name: 'HLenergy Admin',
        id: window.location.hostname
      },
      user: {
        id: this.stringToUint8Array('admin-user'),
        name: '<EMAIL>',
        displayName: 'HLenergy Administrator'
      },
      pubKeyCredParams: [
        { alg: -7, type: 'public-key' }, // ES256
        { alg: -257, type: 'public-key' } // RS256
      ],
      authenticatorSelection: {
        authenticatorAttachment: 'platform',
        userVerification: 'required',
        residentKey: 'preferred'
      },
      timeout: 60000,
      attestation: 'direct'
    }

    const registrationOptions = { ...defaultOptions, ...options }

    try {
      const credential = await navigator.credentials.create({
        publicKey: registrationOptions
      }) as PublicKeyCredential

      if (!credential) {
        throw new Error('Failed to create credential')
      }

      return {
        id: credential.id,
        type: credential.type as 'public-key',
        rawId: credential.rawId,
        response: credential.response as AuthenticatorAttestationResponse
      }
    } catch (error: any) {
      console.error('Biometric registration failed:', error)
      throw new Error(this.getErrorMessage(error))
    }
  }

  /**
   * Authenticate using biometric credential
   */
  async authenticate(options: Partial<BiometricAuthenticationOptions> = {}): Promise<BiometricCredential> {
    if (!this.isSupported) {
      throw new Error('Biometric authentication is not supported on this device')
    }

    const defaultOptions: BiometricAuthenticationOptions = {
      challenge: this.generateChallenge(),
      timeout: 60000,
      userVerification: 'required'
    }

    const authenticationOptions = { ...defaultOptions, ...options }

    try {
      const credential = await navigator.credentials.get({
        publicKey: authenticationOptions
      }) as PublicKeyCredential

      if (!credential) {
        throw new Error('Authentication failed')
      }

      return {
        id: credential.id,
        type: credential.type as 'public-key',
        rawId: credential.rawId,
        response: credential.response as AuthenticatorAssertionResponse
      }
    } catch (error: any) {
      console.error('Biometric authentication failed:', error)
      throw new Error(this.getErrorMessage(error))
    }
  }

  /**
   * Check if user has registered biometric credentials
   */
  async hasRegisteredCredentials(): Promise<boolean> {
    try {
      // Try to get credentials without specific allowCredentials
      // This will show available credentials to the user
      const credential = await navigator.credentials.get({
        publicKey: {
          challenge: this.generateChallenge(),
          timeout: 5000,
          userVerification: 'discouraged'
        }
      })

      return !!credential
    } catch (error) {
      // If no credentials are available, this will throw an error
      return false
    }
  }

  /**
   * Get biometric capability info
   */
  async getCapabilities(): Promise<{
    supported: boolean
    available: boolean
    authenticators: string[]
    hasCredentials: boolean
  }> {
    const supported = this.isSupported
    const available = supported ? await this.isAvailable() : false
    const authenticators = supported ? await this.getSupportedAuthenticators() : []
    const hasCredentials = supported && available ? await this.hasRegisteredCredentials() : false

    return {
      supported,
      available,
      authenticators,
      hasCredentials
    }
  }

  /**
   * Generate a random challenge
   */
  private generateChallenge(): Uint8Array {
    const challenge = new Uint8Array(32)
    crypto.getRandomValues(challenge)
    return challenge
  }

  /**
   * Convert string to Uint8Array
   */
  private stringToUint8Array(str: string): Uint8Array {
    const encoder = new TextEncoder()
    return encoder.encode(str)
  }

  /**
   * Get user-friendly error message
   */
  private getErrorMessage(error: any): string {
    if (error.name === 'NotSupportedError') {
      return 'Biometric authentication is not supported on this device'
    }
    if (error.name === 'NotAllowedError') {
      return 'Biometric authentication was cancelled or not allowed'
    }
    if (error.name === 'SecurityError') {
      return 'Security error occurred during biometric authentication'
    }
    if (error.name === 'AbortError') {
      return 'Biometric authentication was aborted'
    }
    if (error.name === 'ConstraintError') {
      return 'Biometric authentication constraints not satisfied'
    }
    if (error.name === 'InvalidStateError') {
      return 'Biometric authenticator is in an invalid state'
    }
    if (error.name === 'UnknownError') {
      return 'An unknown error occurred during biometric authentication'
    }

    return error.message || 'Biometric authentication failed'
  }

  /**
   * Test biometric authentication (for development)
   */
  async testAuthentication(): Promise<boolean> {
    try {
      const capabilities = await this.getCapabilities()
      console.log('Biometric capabilities:', capabilities)

      if (!capabilities.available) {
        console.warn('Biometric authentication not available')
        return false
      }

      // Try to authenticate
      const credential = await this.authenticate()
      console.log('Biometric authentication successful:', credential)
      return true
    } catch (error) {
      console.error('Biometric test failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const biometricService = new BiometricService()
export default biometricService
