import { apiService } from './api'

// Auth Types
export interface User {
  id: number
  name: string
  email: string
  role: 'admin' | 'staff' | 'client'
  email_verified: boolean
  is_active: boolean
  profile?: any
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
}

export interface LoginResponse {
  user: User
  accessToken: string
  refreshToken: string
  expiresIn: number
  queued?: boolean
  emailId?: number
}

export interface RefreshTokenResponse {
  token: string
  expiresIn: number
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  password: string
}

export interface EmailVerificationRequest {
  token: string
}

export interface PinLoginRequest {
  userId: number
  pin: string
  deviceFingerprint?: string
}

export interface BiometricLoginRequest {
  credentialId: string
  signature: string
  clientData: string
  deviceFingerprint?: string
}

export interface BiometricRegisterRequest {
  credentialId: string
  publicKey: string
  deviceType?: string
  deviceName?: string
  transport?: string[]
  deviceFingerprint?: string
}

// Authentication Service
class AuthService {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/login', credentials)
  }

  // Register new user
  async register(userData: RegisterRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/register', userData)
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return await apiService.post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken,
    })
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error)
    }
  }

  // Get current user profile
  async getProfile(): Promise<User> {
    return await apiService.get<User>('/auth/profile')
  }

  // Update user profile
  async updateProfile(profileData: Partial<User>): Promise<User> {
    return await apiService.put<User>('/auth/profile', profileData)
  }

  // Request password reset
  async requestPasswordReset(data: PasswordResetRequest): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/forgot-password', data)
  }

  // Confirm password reset
  async confirmPasswordReset(data: PasswordResetConfirm): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/reset-password', data)
  }

  // Verify email address
  async verifyEmail(data: EmailVerificationRequest): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/verify-email', data)
  }

  // Resend email verification
  async resendEmailVerification(): Promise<{ message: string; emailId?: number }> {
    return await apiService.post<{ message: string; emailId?: number }>('/auth/resend-verification')
  }

  // Change password (when logged in)
  async changePassword(currentPassword: string, newPassword: string): Promise<{ message: string }> {
    return await apiService.post<{ message: string }>('/auth/change-password', {
      currentPassword,
      newPassword,
    })
  }

  // Check if email exists (for registration validation)
  async checkEmailExists(email: string): Promise<{ exists: boolean }> {
    return await apiService.get<{ exists: boolean }>(`/auth/check-email?email=${encodeURIComponent(email)}`)
  }

  // Validate token (check if user is still authenticated)
  async validateToken(): Promise<User> {
    return await apiService.get<User>('/auth/validate')
  }

  // PIN Authentication
  async loginWithPin(data: PinLoginRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/pin/login', data)
  }

  async verifyPin(data: PinLoginRequest): Promise<{ success: boolean; message?: string }> {
    const response = await apiService.postRaw<{ success: boolean; message?: string }>('/auth/pin/verify', data)
    return {
      success: response.success,
      message: response.message
    }
  }

  async createPin(pin: string, deviceFingerprint?: string): Promise<{ success: boolean; pinId: string; message?: string }> {
    try {
      const response = await apiService.postRaw<{ pinId: string }>('/auth/pin/create', {
        pin,
        deviceFingerprint
      })

      return {
        success: response.success,
        pinId: response.data?.pinId || '',
        message: response.message
      }
    } catch (error: any) {
      // Extract detailed validation errors if available
      if (error.response?.data?.error?.validationErrors) {
        throw new Error(error.response.data.error.validationErrors)
      } else if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message)
      } else {
        throw error
      }
    }
  }

  // Biometric Authentication
  async loginWithBiometric(data: BiometricLoginRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/biometric/login', data)
  }

  // Biometric Session Unlock (requires existing session)
  async unlockWithBiometric(data: BiometricLoginRequest): Promise<LoginResponse> {
    return await apiService.post<LoginResponse>('/auth/biometric/unlock', data)
  }

  async registerBiometric(data: BiometricRegisterRequest): Promise<{ success: boolean; credentialId: string; data?: any; message?: string }> {
    try {
      const response = await apiService.postRaw<{ credentialId: string }>('/auth/biometric/register', data)

      return {
        success: response.success,
        credentialId: response.data?.credentialId || '',
        data: response.data,
        message: response.message
      }
    } catch (error: any) {
      // Extract detailed validation errors if available
      if (error.response?.data?.error?.validationErrors) {
        throw new Error(error.response.data.error.validationErrors)
      } else if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message)
      } else {
        throw error
      }
    }
  }

  // Check available authentication methods
  async getAuthMethods(): Promise<{ methods: string[]; hasPin: boolean; biometricCount: number }> {
    // The apiService.get() method automatically extracts response.data.data
    // So we get the actual data object directly
    const data = await apiService.get<{ methods: string[]; hasPin: boolean; biometricCount: number }>('/auth/methods')
    console.log('Auth methods data:', data)
    return data
  }

  // PIN Management
  async updatePin(currentPin: string, newPin: string, deviceFingerprint?: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await apiService.putRaw<any>('/auth/pin/update', {
        currentPin,
        newPin,
        deviceFingerprint
      })

      return {
        success: response.success,
        message: response.message
      }
    } catch (error: any) {
      // Extract detailed validation errors if available
      if (error.response?.data?.error?.validationErrors) {
        throw new Error(error.response.data.error.validationErrors)
      } else if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message)
      } else {
        throw error
      }
    }
  }

  async removePin(currentPin: string, deviceFingerprint?: string): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await apiService.deleteRaw<any>('/auth/pin/remove', {
        currentPin,
        deviceFingerprint
      })

      return {
        success: response.success,
        message: response.message
      }
    } catch (error: any) {
      // Extract detailed validation errors if available
      if (error.response?.data?.error?.validationErrors) {
        throw new Error(error.response.data.error.validationErrors)
      } else if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message)
      } else {
        throw error
      }
    }
  }

  // Biometric Management
  async removeBiometric(): Promise<{ success: boolean; message?: string }> {
    try {
      const response = await apiService.deleteRaw<any>('/auth/biometric/remove')

      return {
        success: response.success,
        message: response.message
      }
    } catch (error: any) {
      // Extract detailed validation errors if available
      if (error.response?.data?.error?.validationErrors) {
        throw new Error(error.response.data.error.validationErrors)
      } else if (error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message)
      } else {
        throw error
      }
    }
  }

  // Security Preferences
  async updateSecurityPreferences(preferences: any): Promise<{ success: boolean }> {
    return await apiService.put<{ success: boolean }>('/auth/security-preferences', preferences)
  }

  async getSecurityPreferences(): Promise<any> {
    return await apiService.get<any>('/auth/security-preferences')
  }
}

// Export singleton instance
export const authService = new AuthService()
export default authService
