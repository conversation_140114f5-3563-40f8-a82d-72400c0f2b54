import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { crmService, type Customer, type Project, type Communication, type Document } from '@/services/crm'

export const useCRMStore = defineStore('crm', () => {
  // State
  const customers = ref<Customer[]>([])
  const currentCustomer = ref<Customer | null>(null)
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const communications = ref<Communication[]>([])
  const documents = ref<Document[]>([])
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Pagination
  const customerPagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  
  const projectPagination = ref({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // Filters
  const customerFilters = ref({
    status: '',
    priority: '',
    assignedTo: null as number | null,
    leadSource: '',
    search: ''
  })

  const projectFilters = ref({
    status: '',
    type: '',
    priority: '',
    assignedTo: null as number | null,
    customerId: null as number | null,
    search: ''
  })

  // Dashboard data
  const dashboardData = ref<any>(null)
  const customerStats = ref<any>(null)
  const projectStats = ref<any>(null)

  // Getters
  const totalCustomers = computed(() => customerPagination.value.total)
  const totalProjects = computed(() => projectPagination.value.total)
  
  const activeCustomers = computed(() => 
    customers.value.filter(c => c.status !== 'inactive')
  )
  
  const highPriorityCustomers = computed(() =>
    customers.value.filter(c => c.priority === 'high' || c.priority === 'urgent')
  )
  
  const activeProjects = computed(() =>
    projects.value.filter(p => p.status === 'in_progress' || p.status === 'planning')
  )
  
  const overdueProjects = computed(() =>
    projects.value.filter(p => {
      if (!p.endDate || p.status === 'completed') return false
      return new Date(p.endDate) < new Date()
    })
  )

  const unreadCommunications = computed(() =>
    communications.value.filter(c => !c.isRead)
  )

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // Customer Actions
  const fetchCustomers = async (params: any = {}) => {
    try {
      isLoading.value = true
      clearError()

      const mergedParams = {
        ...customerFilters.value,
        page: customerPagination.value.page,
        limit: customerPagination.value.limit,
        ...params
      }

      const response = await crmService.getCustomers(mergedParams)

      customers.value = response.items
      customerPagination.value = response.pagination

    } catch (err: any) {
      setError(err.message || 'Failed to fetch customers')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchCustomer = async (id: number) => {
    try {
      isLoading.value = true
      clearError()

      const customer = await crmService.getCustomer(id)
      currentCustomer.value = customer

      return customer
    } catch (err: any) {
      setError(err.message || 'Failed to fetch customer')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const createCustomer = async (customerData: Partial<Customer>) => {
    try {
      isLoading.value = true
      clearError()

      const customer = await crmService.createCustomer(customerData)
      
      // Add to local state
      customers.value.unshift(customer)
      customerPagination.value.total += 1

      return customer
    } catch (err: any) {
      setError(err.message || 'Failed to create customer')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateCustomer = async (id: number, customerData: Partial<Customer>) => {
    try {
      isLoading.value = true
      clearError()

      const customer = await crmService.updateCustomer(id, customerData)
      
      // Update local state
      const index = customers.value.findIndex(c => c.id === id)
      if (index !== -1) {
        customers.value[index] = customer
      }
      
      if (currentCustomer.value?.id === id) {
        currentCustomer.value = customer
      }

      return customer
    } catch (err: any) {
      setError(err.message || 'Failed to update customer')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deleteCustomer = async (id: number) => {
    try {
      isLoading.value = true
      clearError()

      await crmService.deleteCustomer(id)
      
      // Remove from local state
      customers.value = customers.value.filter(c => c.id !== id)
      customerPagination.value.total -= 1
      
      if (currentCustomer.value?.id === id) {
        currentCustomer.value = null
      }

    } catch (err: any) {
      setError(err.message || 'Failed to delete customer')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const convertToCustomer = async (id: number, conversionData?: any) => {
    try {
      isLoading.value = true
      clearError()

      const customer = await crmService.convertToCustomer(id, conversionData)
      
      // Update local state
      const index = customers.value.findIndex(c => c.id === id)
      if (index !== -1) {
        customers.value[index] = customer
      }
      
      if (currentCustomer.value?.id === id) {
        currentCustomer.value = customer
      }

      return customer
    } catch (err: any) {
      setError(err.message || 'Failed to convert customer')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Project Actions
  const fetchProjects = async (params: any = {}) => {
    try {
      isLoading.value = true
      clearError()

      const mergedParams = {
        ...projectFilters.value,
        page: projectPagination.value.page,
        limit: projectPagination.value.limit,
        ...params
      }

      const response = await crmService.getProjects(mergedParams)

      projects.value = response.items
      projectPagination.value = response.pagination

    } catch (err: any) {
      setError(err.message || 'Failed to fetch projects')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchProject = async (id: number) => {
    try {
      isLoading.value = true
      clearError()

      const project = await crmService.getProject(id)
      currentProject.value = project

      return project
    } catch (err: any) {
      setError(err.message || 'Failed to fetch project')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const createProject = async (projectData: Partial<Project>) => {
    try {
      isLoading.value = true
      clearError()

      const project = await crmService.createProject(projectData)
      
      // Add to local state
      projects.value.unshift(project)
      projectPagination.value.total += 1

      return project
    } catch (err: any) {
      setError(err.message || 'Failed to create project')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateProject = async (id: number, projectData: Partial<Project>) => {
    try {
      isLoading.value = true
      clearError()

      const project = await crmService.updateProject(id, projectData)
      
      // Update local state
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = project
      }
      
      if (currentProject.value?.id === id) {
        currentProject.value = project
      }

      return project
    } catch (err: any) {
      setError(err.message || 'Failed to update project')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Communication Actions
  const fetchCommunications = async (params: any = {}) => {
    try {
      const response = await crmService.getCommunications(params)
      communications.value = response.items
      return response
    } catch (err: any) {
      setError(err.message || 'Failed to fetch communications')
      throw err
    }
  }

  const createCommunication = async (communicationData: Partial<Communication>) => {
    try {
      const communication = await crmService.createCommunication(communicationData)
      communications.value.unshift(communication)
      return communication
    } catch (err: any) {
      setError(err.message || 'Failed to create communication')
      throw err
    }
  }

  // Dashboard Actions
  const fetchDashboardData = async () => {
    try {
      isLoading.value = true
      clearError()

      const response = await crmService.getCRMDashboard()
      dashboardData.value = response

    } catch (err: any) {
      setError(err.message || 'Failed to fetch dashboard data')
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Filter Actions
  const setCustomerFilters = (filters: any) => {
    customerFilters.value = { ...customerFilters.value, ...filters }
    customerPagination.value.page = 1 // Reset to first page
  }

  const setProjectFilters = (filters: any) => {
    projectFilters.value = { ...projectFilters.value, ...filters }
    projectPagination.value.page = 1 // Reset to first page
  }

  const clearFilters = () => {
    customerFilters.value = {
      status: '',
      priority: '',
      assignedTo: null,
      leadSource: '',
      search: ''
    }
    projectFilters.value = {
      status: '',
      type: '',
      priority: '',
      assignedTo: null,
      customerId: null,
      search: ''
    }
  }

  return {
    // State
    customers,
    currentCustomer,
    projects,
    currentProject,
    communications,
    documents,
    isLoading,
    error,
    customerPagination,
    projectPagination,
    customerFilters,
    projectFilters,
    dashboardData,
    customerStats,
    projectStats,

    // Getters
    totalCustomers,
    totalProjects,
    activeCustomers,
    highPriorityCustomers,
    activeProjects,
    overdueProjects,
    unreadCommunications,

    // Actions
    setError,
    clearError,
    fetchCustomers,
    fetchCustomer,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    convertToCustomer,
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    fetchCommunications,
    createCommunication,
    fetchDashboardData,
    setCustomerFilters,
    setProjectFilters,
    clearFilters
  }
})
