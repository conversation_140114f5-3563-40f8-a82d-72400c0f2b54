import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { contactService, type ContactFormData } from '@/services/contact'

export interface OfflineSubmission {
  id: string
  type: 'contact' | 'auth' | 'other'
  data: any
  timestamp: number
  retryCount: number
  maxRetries: number
  status: 'pending' | 'syncing' | 'synced' | 'failed'
  error?: string
}

export const useOfflineStore = defineStore('offline', () => {
  // State
  const isOnline = ref(navigator.onLine)
  const pendingSubmissions = ref<OfflineSubmission[]>([])
  const syncInProgress = ref(false)
  const lastSyncTime = ref<number | null>(null)

  // Getters
  const hasPendingSubmissions = computed(() => pendingSubmissions.value.length > 0)
  const pendingCount = computed(() => pendingSubmissions.value.filter(s => s.status === 'pending').length)
  const failedCount = computed(() => pendingSubmissions.value.filter(s => s.status === 'failed').length)

  // Load pending submissions from localStorage
  const loadPendingSubmissions = () => {
    try {
      const stored = localStorage.getItem('offline-submissions')
      if (stored) {
        pendingSubmissions.value = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Error loading pending submissions:', error)
      pendingSubmissions.value = []
    }
  }

  // Save pending submissions to localStorage
  const savePendingSubmissions = () => {
    try {
      localStorage.setItem('offline-submissions', JSON.stringify(pendingSubmissions.value))
    } catch (error) {
      console.error('Error saving pending submissions:', error)
    }
  }

  // Add submission to offline queue
  const addOfflineSubmission = (type: string, data: any, maxRetries = 3): string => {
    const submission: OfflineSubmission = {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: type as any,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      status: 'pending'
    }

    pendingSubmissions.value.push(submission)
    savePendingSubmissions()

    // Try to sync immediately if online
    if (isOnline.value) {
      syncPendingSubmissions()
    }

    return submission.id
  }

  // Submit contact form (with offline support)
  const submitContactForm = async (formData: ContactFormData): Promise<{ success: boolean; offline?: boolean; submissionId?: string }> => {
    if (!isOnline.value) {
      // Store for later sync
      const submissionId = addOfflineSubmission('contact', formData)
      return { success: true, offline: true, submissionId }
    }

    try {
      // Try to submit online
      await contactService.submitContactForm(formData)
      return { success: true, offline: false }
    } catch (error) {
      // If online submission fails, store for later
      const submissionId = addOfflineSubmission('contact', formData)
      return { success: true, offline: true, submissionId }
    }
  }

  // Sync a single submission
  const syncSubmission = async (submission: OfflineSubmission): Promise<boolean> => {
    try {
      submission.status = 'syncing'
      savePendingSubmissions()

      switch (submission.type) {
        case 'contact':
          await contactService.submitContactForm(submission.data)
          break
        default:
          throw new Error(`Unknown submission type: ${submission.type}`)
      }

      submission.status = 'synced'
      submission.retryCount = 0
      savePendingSubmissions()
      
      return true
    } catch (error: any) {
      submission.retryCount++
      submission.error = error.message || 'Sync failed'
      
      if (submission.retryCount >= submission.maxRetries) {
        submission.status = 'failed'
      } else {
        submission.status = 'pending'
      }
      
      savePendingSubmissions()
      return false
    }
  }

  // Sync all pending submissions
  const syncPendingSubmissions = async (): Promise<void> => {
    if (syncInProgress.value || !isOnline.value) return

    syncInProgress.value = true
    
    try {
      const pendingItems = pendingSubmissions.value.filter(s => s.status === 'pending')
      
      for (const submission of pendingItems) {
        await syncSubmission(submission)
        
        // Small delay between syncs to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // Remove successfully synced items
      pendingSubmissions.value = pendingSubmissions.value.filter(s => s.status !== 'synced')
      savePendingSubmissions()
      
      lastSyncTime.value = Date.now()
    } catch (error) {
      console.error('Error during sync:', error)
    } finally {
      syncInProgress.value = false
    }
  }

  // Retry failed submission
  const retrySubmission = async (submissionId: string): Promise<boolean> => {
    const submission = pendingSubmissions.value.find(s => s.id === submissionId)
    if (!submission) return false

    submission.status = 'pending'
    submission.retryCount = 0
    submission.error = undefined
    savePendingSubmissions()

    if (isOnline.value) {
      return await syncSubmission(submission)
    }

    return true
  }

  // Remove submission from queue
  const removeSubmission = (submissionId: string): void => {
    pendingSubmissions.value = pendingSubmissions.value.filter(s => s.id !== submissionId)
    savePendingSubmissions()
  }

  // Clear all submissions
  const clearAllSubmissions = (): void => {
    pendingSubmissions.value = []
    savePendingSubmissions()
  }

  // Update online status
  const updateOnlineStatus = (online: boolean): void => {
    isOnline.value = online
    
    if (online && hasPendingSubmissions.value) {
      // Auto-sync when coming back online
      setTimeout(() => {
        syncPendingSubmissions()
      }, 1000) // Small delay to ensure connection is stable
    }
  }

  // Initialize offline store
  const initializeOfflineStore = (): void => {
    loadPendingSubmissions()
    
    // Listen for online/offline events
    window.addEventListener('online', () => updateOnlineStatus(true))
    window.addEventListener('offline', () => updateOnlineStatus(false))
    
    // Initial sync if online and has pending items
    if (isOnline.value && hasPendingSubmissions.value) {
      syncPendingSubmissions()
    }
  }

  return {
    // State
    isOnline,
    pendingSubmissions,
    syncInProgress,
    lastSyncTime,
    
    // Getters
    hasPendingSubmissions,
    pendingCount,
    failedCount,
    
    // Actions
    submitContactForm,
    syncPendingSubmissions,
    retrySubmission,
    removeSubmission,
    clearAllSubmissions,
    updateOnlineStatus,
    initializeOfflineStore,
  }
})
