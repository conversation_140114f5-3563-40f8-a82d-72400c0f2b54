import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { analyticsService, type AnalyticsMetrics, type BusinessMetrics, type HeatmapData } from '@/services/analytics'

export const useAnalyticsStore = defineStore('analytics', () => {
  // State
  const analyticsMetrics = ref<AnalyticsMetrics | null>(null)
  const businessMetrics = ref<BusinessMetrics | null>(null)
  const heatmapData = ref<Record<string, HeatmapData>>({})
  const conversionFunnel = ref<Array<{ step: string; users: number; conversionRate: number }>>([])
  
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const lastUpdated = ref<Date | null>(null)
  const selectedTimeRange = ref<'24h' | '7d' | '30d' | '90d'>('7d')

  // Getters
  const totalUsers = computed(() => analyticsMetrics.value?.totalUsers || 0)
  const conversionRate = computed(() => businessMetrics.value?.conversionRate || 0)
  const totalLeads = computed(() => businessMetrics.value?.totalLeads || 0)
  const qualifiedLeads = computed(() => businessMetrics.value?.qualifiedLeads || 0)
  
  const topPerformingPages = computed(() => 
    analyticsMetrics.value?.topPages?.slice(0, 5) || []
  )
  
  const leadSourcesData = computed(() => 
    businessMetrics.value?.leadSources || []
  )
  
  const serviceInterestData = computed(() => 
    businessMetrics.value?.serviceInterest || []
  )
  
  const timeSeriesData = computed(() => 
    businessMetrics.value?.timeSeriesData || []
  )

  // Real-time metrics (simulated)
  const realTimeUsers = ref(0)
  const realTimePageViews = ref(0)
  const realTimeConversions = ref(0)

  // Actions
  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTimeRange = (range: '24h' | '7d' | '30d' | '90d') => {
    selectedTimeRange.value = range
    // Automatically refresh data when time range changes
    refreshAllData()
  }

  // Fetch analytics metrics
  const fetchAnalyticsMetrics = async (timeRange?: '24h' | '7d' | '30d' | '90d') => {
    try {
      clearError()
      const range = timeRange || selectedTimeRange.value
      const data = await analyticsService.getAnalyticsMetrics(range)
      analyticsMetrics.value = data
      return data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch analytics metrics')
      throw err
    }
  }

  // Fetch business metrics
  const fetchBusinessMetrics = async (timeRange?: '24h' | '7d' | '30d' | '90d') => {
    try {
      clearError()
      const range = timeRange || selectedTimeRange.value
      const data = await analyticsService.getBusinessMetrics(range)
      businessMetrics.value = data
      return data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch business metrics')
      throw err
    }
  }

  // Fetch heatmap data for a specific page
  const fetchHeatmapData = async (page: string, timeRange?: '24h' | '7d' | '30d') => {
    try {
      clearError()
      const range = timeRange || '7d'
      const data = await analyticsService.getHeatmapData(page, range)
      heatmapData.value[page] = data
      return data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch heatmap data')
      throw err
    }
  }

  // Fetch conversion funnel
  const fetchConversionFunnel = async () => {
    try {
      clearError()
      const data = await analyticsService.getConversionFunnel()
      conversionFunnel.value = data
      return data
    } catch (err: any) {
      setError(err.message || 'Failed to fetch conversion funnel')
      throw err
    }
  }

  // Refresh all dashboard data
  const refreshAllData = async () => {
    if (isLoading.value) return

    try {
      isLoading.value = true
      clearError()

      await Promise.all([
        fetchAnalyticsMetrics(),
        fetchBusinessMetrics(),
        fetchConversionFunnel()
      ])

      lastUpdated.value = new Date()
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard data')
    } finally {
      isLoading.value = false
    }
  }

  // Get dashboard summary
  const getDashboardSummary = computed(() => {
    if (!analyticsMetrics.value || !businessMetrics.value) return null

    return {
      users: {
        total: analyticsMetrics.value.totalUsers,
        active: analyticsMetrics.value.activeUsers,
        growth: calculateGrowth(analyticsMetrics.value.totalUsers, 100) // Mock previous period
      },
      leads: {
        total: businessMetrics.value.totalLeads,
        qualified: businessMetrics.value.qualifiedLeads,
        conversionRate: businessMetrics.value.conversionRate,
        growth: calculateGrowth(businessMetrics.value.totalLeads, 50) // Mock previous period
      },
      engagement: {
        pageViews: analyticsMetrics.value.pageViews,
        sessions: analyticsMetrics.value.sessions,
        bounceRate: analyticsMetrics.value.bounceRate,
        avgSessionDuration: analyticsMetrics.value.avgSessionDuration
      },
      performance: {
        topPage: analyticsMetrics.value.topPages[0]?.page || 'N/A',
        topReferrer: analyticsMetrics.value.topReferrers[0]?.referrer || 'Direct',
        bestConvertingSource: businessMetrics.value.leadSources[0]?.source || 'N/A'
      }
    }
  })

  // Calculate growth percentage
  const calculateGrowth = (current: number, previous: number): number => {
    if (previous === 0) return 0
    return Math.round(((current - previous) / previous) * 100)
  }

  // Real-time data simulation (in production, this would come from WebSocket)
  const startRealTimeUpdates = () => {
    const updateInterval = setInterval(() => {
      // Simulate real-time updates
      realTimeUsers.value = Math.floor(Math.random() * 50) + 10
      realTimePageViews.value += Math.floor(Math.random() * 5)
      
      if (Math.random() > 0.9) { // 10% chance of conversion
        realTimeConversions.value += 1
      }
    }, 5000) // Update every 5 seconds

    return updateInterval
  }

  const stopRealTimeUpdates = (intervalId: number) => {
    clearInterval(intervalId)
  }

  // Export data for reports
  const exportAnalyticsData = async (format: 'csv' | 'json' | 'pdf' = 'csv') => {
    try {
      const data = {
        analytics: analyticsMetrics.value,
        business: businessMetrics.value,
        funnel: conversionFunnel.value,
        timeRange: selectedTimeRange.value,
        exportedAt: new Date().toISOString()
      }

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        return blob
      }

      // For CSV and PDF, you would typically call a backend endpoint
      // For now, return JSON blob
      return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    } catch (err: any) {
      setError(err.message || 'Failed to export data')
      throw err
    }
  }

  // Initialize analytics store
  const initializeAnalyticsStore = async () => {
    await refreshAllData()
  }

  return {
    // State
    analyticsMetrics,
    businessMetrics,
    heatmapData,
    conversionFunnel,
    isLoading,
    error,
    lastUpdated,
    selectedTimeRange,
    realTimeUsers,
    realTimePageViews,
    realTimeConversions,

    // Getters
    totalUsers,
    conversionRate,
    totalLeads,
    qualifiedLeads,
    topPerformingPages,
    leadSourcesData,
    serviceInterestData,
    timeSeriesData,
    getDashboardSummary,

    // Actions
    setTimeRange,
    fetchAnalyticsMetrics,
    fetchBusinessMetrics,
    fetchHeatmapData,
    fetchConversionFunnel,
    refreshAllData,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    exportAnalyticsData,
    initializeAnalyticsStore,
    setError,
    clearError
  }
})
