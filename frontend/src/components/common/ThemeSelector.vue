<template>
  <div class="dropdown dropdown-end">
    <div tabindex="0" role="button" class="btn btn-ghost btn-sm sm:btn-md gap-1 sm:gap-2">
      <Icon name="sun" size="sm" />
      <span class="hidden sm:inline text-sm">Theme</span>
      <Icon name="chevron-down" size="xs" />
    </div>
    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-56 sm:w-64 p-3 shadow-lg border border-base-300 max-h-80 sm:max-h-96 overflow-y-auto">
      <li class="menu-title">
        <span>Choose Theme</span>
      </li>
      <li v-for="theme in themes" :key="theme.value">
        <a 
          @click="setTheme(theme.value)"
          :class="{ 'active': currentTheme === theme.value }"
          class="flex items-center justify-between"
        >
          <span>{{ theme.name }}</span>
          <div class="flex gap-1">
            <div 
              v-for="color in theme.colors" 
              :key="color"
              :class="color"
              class="w-3 h-3 rounded-full"
            ></div>
          </div>
        </a>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Icon from './Icon.vue'

const currentTheme = ref('light')

const themes = [
  {
    name: 'Light',
    value: 'light',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Clean light theme'
  },
  {
    name: 'Dark',
    value: 'dark',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Modern dark theme'
  },
  {
    name: 'Corporate',
    value: 'corporate',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Professional business theme'
  },
  {
    name: 'Business',
    value: 'business',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Elegant business theme'
  },
  {
    name: 'Emerald',
    value: 'emerald',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Fresh green theme'
  },
  {
    name: 'Forest',
    value: 'forest',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Nature-inspired theme'
  },
  {
    name: 'Synthwave',
    value: 'synthwave',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Retro neon theme'
  },
  {
    name: 'Cyberpunk',
    value: 'cyberpunk',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Futuristic cyber theme'
  },
  {
    name: 'Dracula',
    value: 'dracula',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Popular dark theme'
  },
  {
    name: 'Cupcake',
    value: 'cupcake',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Sweet pastel theme'
  },
  {
    name: 'Retro',
    value: 'retro',
    colors: ['bg-primary', 'bg-secondary', 'bg-accent'],
    description: 'Vintage retro theme'
  }
]

const setTheme = (theme: string) => {
  currentTheme.value = theme
  document.documentElement.setAttribute('data-theme', theme)
  localStorage.setItem('theme', theme)
}

onMounted(() => {
  // Load saved theme or default to light
  const savedTheme = localStorage.getItem('theme') || 'light'
  setTheme(savedTheme)
})
</script>

<style scoped>
.menu-title {
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0.7;
  padding: 0.5rem 0.75rem;
}

.dropdown-content {
  margin-top: 0.5rem;
}
</style>
