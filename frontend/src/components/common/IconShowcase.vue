<template>
  <div class="p-6 space-y-6">
    <div class="text-center">
      <h2 class="text-3xl font-bold text-base-content mb-2">Icon Library</h2>
      <p class="text-base-content opacity-70">Complete Heroicons integration with DaisyUI</p>
    </div>

    <!-- Search -->
    <div class="form-control max-w-md mx-auto">
      <div class="input-group">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="Search icons..." 
          class="input input-bordered flex-1"
        />
        <button class="btn btn-square">
          <Icon name="search" size="sm" />
        </button>
      </div>
    </div>

    <!-- Icon Categories -->
    <div class="tabs tabs-boxed justify-center">
      <a 
        v-for="category in categories" 
        :key="category"
        @click="selectedCategory = category"
        :class="{ 'tab-active': selectedCategory === category }"
        class="tab"
      >
        {{ category }}
      </a>
    </div>

    <!-- Icon Grid -->
    <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-3 sm:gap-4">
      <div
        v-for="icon in filteredIcons"
        :key="icon.name"
        @click="copyIconName(icon.name)"
        class="card bg-base-100 hover:bg-base-200 cursor-pointer transition-all duration-200 border border-base-300 hover:border-primary hover:shadow-md"
        :title="`Click to copy: <Icon name='${icon.name}' />`"
      >
        <div class="card-body p-3 sm:p-4">
          <div class="flex flex-col items-center space-y-2">
            <Icon :name="icon.name" size="lg" class="text-primary" />
            <span class="text-xs text-center font-mono leading-tight">{{ icon.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Copy Toast -->
    <div v-if="showCopyToast" class="toast toast-top toast-center">
      <div class="alert alert-success">
        <Icon name="check" size="sm" />
        <span>Icon name copied!</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Icon from './Icon.vue'

const searchQuery = ref('')
const selectedCategory = ref('All')
const showCopyToast = ref(false)

const categories = ['All', 'Navigation', 'Communication', 'Business', 'Energy', 'Actions', 'Status', 'Security', 'Technology']

const icons = [
  // Navigation & UI
  { name: 'home', category: 'Navigation' },
  { name: 'user', category: 'Navigation' },
  { name: 'users', category: 'Navigation' },
  { name: 'settings', category: 'Navigation' },
  { name: 'bell', category: 'Navigation' },
  { name: 'menu', category: 'Navigation' },
  { name: 'search', category: 'Navigation' },
  { name: 'filter', category: 'Navigation' },
  { name: 'more', category: 'Navigation' },
  
  // Communication
  { name: 'email', category: 'Communication' },
  { name: 'phone', category: 'Communication' },
  { name: 'chat', category: 'Communication' },
  { name: 'video', category: 'Communication' },
  { name: 'microphone', category: 'Communication' },
  { name: 'speaker', category: 'Communication' },
  
  // Time & Calendar
  { name: 'calendar', category: 'Navigation' },
  { name: 'clock', category: 'Navigation' },
  
  // Documents & Files
  { name: 'document', category: 'Business' },
  { name: 'document-text', category: 'Business' },
  { name: 'folder', category: 'Business' },
  { name: 'archive', category: 'Business' },
  { name: 'clipboard', category: 'Business' },
  { name: 'clipboard-list', category: 'Business' },
  
  // Charts & Analytics
  { name: 'chart-bar', category: 'Business' },
  { name: 'chart-pie', category: 'Business' },
  { name: 'presentation-chart', category: 'Business' },
  { name: 'table', category: 'Business' },
  
  // Business & Finance
  { name: 'currency', category: 'Business' },
  { name: 'banknotes', category: 'Business' },
  { name: 'credit-card', category: 'Business' },
  { name: 'building', category: 'Business' },
  { name: 'shopping-cart', category: 'Business' },
  { name: 'truck', category: 'Business' },
  
  // Energy & Environment
  { name: 'lightbulb', category: 'Energy' },
  { name: 'bolt', category: 'Energy' },
  { name: 'fire', category: 'Energy' },
  { name: 'sun', category: 'Energy' },
  { name: 'moon', category: 'Energy' },
  { name: 'globe', category: 'Energy' },
  
  // Actions
  { name: 'plus', category: 'Actions' },
  { name: 'minus', category: 'Actions' },
  { name: 'edit', category: 'Actions' },
  { name: 'trash', category: 'Actions' },
  { name: 'close', category: 'Actions' },
  { name: 'check', category: 'Actions' },
  
  // Arrows & Navigation
  { name: 'arrow-right', category: 'Navigation' },
  { name: 'arrow-left', category: 'Navigation' },
  { name: 'arrow-up', category: 'Navigation' },
  { name: 'arrow-down', category: 'Navigation' },
  { name: 'chevron-right', category: 'Navigation' },
  { name: 'chevron-left', category: 'Navigation' },
  { name: 'chevron-up', category: 'Navigation' },
  { name: 'chevron-down', category: 'Navigation' },
  
  // Status & Feedback
  { name: 'warning', category: 'Status' },
  { name: 'info', category: 'Status' },
  { name: 'success', category: 'Status' },
  { name: 'error', category: 'Status' },
  { name: 'question', category: 'Status' },
  { name: 'exclamation', category: 'Status' },
  
  // Social & Engagement
  { name: 'star', category: 'Actions' },
  { name: 'heart', category: 'Actions' },
  { name: 'thumb-up', category: 'Actions' },
  { name: 'thumb-down', category: 'Actions' },
  { name: 'face-smile', category: 'Actions' },
  { name: 'face-frown', category: 'Actions' },
  
  // Security
  { name: 'lock', category: 'Security' },
  { name: 'lock-open', category: 'Security' },
  { name: 'key', category: 'Security' },
  { name: 'shield', category: 'Security' },
  { name: 'eye', category: 'Security' },
  { name: 'eye-slash', category: 'Security' },
  { name: 'fingerprint', category: 'Security' },
  
  // Technology
  { name: 'computer', category: 'Technology' },
  { name: 'mobile', category: 'Technology' },
  { name: 'wifi', category: 'Technology' },
  { name: 'signal', category: 'Technology' },
  { name: 'server', category: 'Technology' },
  { name: 'cloud', category: 'Technology' },
  { name: 'qr', category: 'Technology' },
  
  // Achievements & Gamification
  { name: 'trophy', category: 'Actions' },
  { name: 'rocket', category: 'Actions' },
  { name: 'sparkles', category: 'Actions' },
  { name: 'academic', category: 'Business' },
  { name: 'beaker', category: 'Technology' },
  { name: 'calculator', category: 'Business' },
]

const filteredIcons = computed(() => {
  let filtered = icons

  // Filter by category
  if (selectedCategory.value !== 'All') {
    filtered = filtered.filter(icon => icon.category === selectedCategory.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    filtered = filtered.filter(icon => 
      icon.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  return filtered
})

const copyIconName = async (iconName: string) => {
  const iconCode = `<Icon name="${iconName}" />`
  
  try {
    await navigator.clipboard.writeText(iconCode)
    showCopyToast.value = true
    setTimeout(() => {
      showCopyToast.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}
</script>

<style scoped>
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.toast {
  z-index: 1000;
}
</style>
