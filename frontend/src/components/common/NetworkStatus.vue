<template>
  <div v-if="!isOnline || hasPendingSubmissions" class="fixed top-16 left-4 right-4 z-40 md:left-auto md:right-4 md:w-80">
    <!-- Offline Status -->
    <div v-if="!isOnline" class="alert bg-gradient-to-r from-warning/20 to-warning/10 border border-warning/30 shadow-xl mb-3 backdrop-blur-sm">
      <div class="flex-1">
        <div class="p-2 bg-warning/20 rounded-lg">
          <Icon name="wifi" size="md" class="text-warning" />
        </div>
        <div>
          <h3 class="font-bold text-warning">{{ $t('network.offline_title') }}</h3>
          <div class="text-sm text-base-content/70">{{ $t('network.offline_message') }}</div>
        </div>
      </div>
    </div>

    <!-- Pending Submissions -->
    <div v-if="hasPendingSubmissions && !isDismissed" class="card bg-gradient-to-br from-info/10 to-info/5 border border-info/20 shadow-xl mb-3 backdrop-blur-sm">
      <div class="card-body p-4">
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-3 flex-1">
            <div class="p-2 bg-info/20 rounded-lg flex-shrink-0">
              <Icon name="refresh" size="md" class="text-info" :class="{ 'animate-spin': syncInProgress }" />
            </div>
            <div class="flex-1">
              <h3 class="font-bold text-info mb-1">
                {{ totalItems }} item{{ totalItems !== 1 ? 's' : '' }} in sync queue
              </h3>
              <div class="text-sm text-base-content/70 mb-3">
                <span v-if="syncInProgress" class="flex items-center gap-1">
                  <span class="loading loading-spinner loading-xs"></span>
                  Syncing...
                </span>
                <span v-else-if="pendingCount > 0 && isOnline">{{ pendingCount }} pending sync</span>
                <span v-else-if="failedCount > 0">{{ failedCount }} failed, {{ pendingCount }} pending</span>
                <span v-else-if="isOnline">Will sync automatically</span>
                <span v-else>Will sync when online</span>
              </div>
              <div class="flex flex-wrap gap-2">
                <button
                  v-if="isOnline && !syncInProgress"
                  @click="handleSync"
                  class="btn btn-sm btn-info hover:btn-info-focus transition-all duration-200"
                >
                  <Icon name="refresh" size="sm" class="mr-1" />
                  Sync Now
                </button>
                <button
                  v-if="totalItems > 0"
                  @click="showPendingItems = !showPendingItems"
                  class="btn btn-sm btn-ghost hover:bg-info/10 transition-all duration-200"
                >
                  <Icon :name="showPendingItems ? 'chevron-up' : 'chevron-down'" size="sm" class="mr-1" />
                  {{ showPendingItems ? 'Hide' : 'Show' }}
                </button>
              </div>
            </div>
          </div>
          <button
            @click="dismissAlert"
            class="btn btn-sm btn-ghost hover:btn-error transition-all duration-200 flex-shrink-0"
            title="Dismiss alert (will reappear if new items are added)"
          >
            <Icon name="x" size="sm" />
          </button>
        </div>
      </div>
    </div>

    <!-- Pending Items Details -->
    <div v-if="showPendingItems && pendingSubmissions.length > 0" class="card bg-base-100/80 backdrop-blur-sm border border-base-200/50 shadow-xl">
      <div class="card-body p-4">
        <h4 class="font-semibold text-base-content mb-4 flex items-center gap-2">
          <Icon name="list" size="sm" />
          Pending Items:
        </h4>
        <div class="space-y-3">
          <div
            v-for="submission in pendingSubmissions"
            :key="submission.id"
            class="flex items-center justify-between p-3 bg-base-200/50 rounded-lg hover:bg-base-200/70 transition-all duration-200"
          >
            <div class="flex items-center gap-3 flex-1">
              <div class="flex items-center justify-center w-8 h-8 rounded-full"
                :class="{
                  'bg-warning/20': submission.status === 'pending',
                  'bg-info/20': submission.status === 'syncing',
                  'bg-success/20': submission.status === 'synced',
                  'bg-error/20': submission.status === 'failed'
                }"
              >
                <div
                  class="w-3 h-3 rounded-full"
                  :class="{
                    'bg-warning': submission.status === 'pending',
                    'bg-info animate-pulse': submission.status === 'syncing',
                    'bg-success': submission.status === 'synced',
                    'bg-error': submission.status === 'failed'
                  }"
                ></div>
              </div>
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-1">
                  <span class="font-medium text-base-content">{{ formatSubmissionType(submission.type) }}</span>
                  <span class="badge badge-sm"
                    :class="{
                      'badge-warning': submission.status === 'pending',
                      'badge-info': submission.status === 'syncing',
                      'badge-success': submission.status === 'synced',
                      'badge-error': submission.status === 'failed'
                    }"
                  >
                    {{ submission.status }}
                  </span>
                </div>
                <div class="text-xs text-base-content/60">{{ formatTimestamp(submission.timestamp) }}</div>
                <div v-if="submission.error" class="text-xs text-error mt-1">{{ submission.error }}</div>
              </div>
            </div>
            <div class="flex gap-2">
              <button
                v-if="submission.status === 'failed'"
                @click="handleRetry(submission.id)"
                class="btn btn-xs btn-primary hover:btn-primary-focus transition-all duration-200"
                title="Retry submission"
              >
                <Icon name="refresh" size="xs" />
                Retry
              </button>
              <button
                @click="handleRemove(submission.id)"
                class="btn btn-xs btn-ghost hover:btn-error transition-all duration-200"
                title="Remove from queue"
              >
                <Icon name="x" size="xs" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePWA } from '@/composables/usePWA'
import { useOfflineStore } from '@/stores/offline'
import Icon from '@/components/common/Icon.vue'

const { isOnline } = usePWA()
const offlineStore = useOfflineStore()

const showPendingItems = ref(false)
const isDismissed = ref(false)

const {
  hasPendingSubmissions,
  pendingCount,
  failedCount,
  pendingSubmissions,
  syncInProgress,
  syncPendingSubmissions,
  retrySubmission,
  removeSubmission,
} = offlineStore

// Computed property for total items (like in ThemeTest)
const totalItems = computed(() => pendingSubmissions?.length || 0)

const handleSync = async () => {
  await syncPendingSubmissions()
}

const handleRetry = async (submissionId: string) => {
  await retrySubmission(submissionId)
}

const handleRemove = (submissionId: string) => {
  removeSubmission(submissionId)
}

const dismissAlert = () => {
  isDismissed.value = true
  console.log('Sync alert dismissed')

  // Auto-show again if new items are added or after 30 seconds
  setTimeout(() => {
    if (totalItems.value > 0) {
      isDismissed.value = false
      console.log('Sync alert auto-restored due to pending items')
    }
  }, 30000) // Show again after 30 seconds if items still exist
}

// Watch for new items and auto-show alert
const lastItemCount = ref(totalItems.value)
const checkForNewItems = () => {
  if (totalItems.value > lastItemCount.value) {
    isDismissed.value = false // Show alert when new items are added
    console.log('Sync alert shown due to new items')
  }
  lastItemCount.value = totalItems.value
}

// Check for new items every few seconds
setInterval(checkForNewItems, 2000)

const formatSubmissionType = (type: string): string => {
  switch (type) {
    case 'contact':
      return 'Contact Form'
    case 'auth':
      return 'Authentication'
    default:
      return type.charAt(0).toUpperCase() + type.slice(1)
  }
}

const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
  return date.toLocaleDateString()
}
</script>
