<template>
  <div class="toast toast-top toast-end z-50">
    <TransitionGroup
      name="toast"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="card shadow-2xl max-w-sm cursor-pointer backdrop-blur-sm border transition-all duration-300 hover:scale-105"
        :class="getToastCardClass(toast.type)"
        @click="removeToast(toast.id)"
      >
        <div class="card-body p-4">
          <div class="flex items-start gap-3">
            <div class="p-2 rounded-lg flex-shrink-0" :class="getIconBgClass(toast.type)">
              <Icon v-if="toast.icon" :name="toast.icon" size="sm" :class="getIconClass(toast.type)" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="font-medium text-sm" :class="getTextClass(toast.type)">{{ toast.title }}</div>
              <div v-if="toast.message" class="text-xs opacity-80 mt-1">{{ toast.message }}</div>
            </div>
            <button
              @click.stop="removeToast(toast.id)"
              class="btn btn-ghost btn-xs btn-circle hover:btn-error transition-all duration-200 flex-shrink-0"
            >
              <Icon name="x" size="xs" />
            </button>
          </div>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useToast } from '@/composables/useToast'
import Icon from '@/components/common/Icon.vue'

const { toasts, removeToast } = useToast()

const getToastCardClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'bg-gradient-to-br from-success/10 to-success/5 border-success/20'
    case 'error':
      return 'bg-gradient-to-br from-error/10 to-error/5 border-error/20'
    case 'warning':
      return 'bg-gradient-to-br from-warning/10 to-warning/5 border-warning/20'
    case 'info':
      return 'bg-gradient-to-br from-info/10 to-info/5 border-info/20'
    default:
      return 'bg-gradient-to-br from-info/10 to-info/5 border-info/20'
  }
}

const getIconBgClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'bg-success/20'
    case 'error':
      return 'bg-error/20'
    case 'warning':
      return 'bg-warning/20'
    case 'info':
      return 'bg-info/20'
    default:
      return 'bg-info/20'
  }
}

const getIconClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-success'
    case 'error':
      return 'text-error'
    case 'warning':
      return 'text-warning'
    case 'info':
      return 'text-info'
    default:
      return 'text-info'
  }
}

const getTextClass = (type: string) => {
  switch (type) {
    case 'success':
      return 'text-success'
    case 'error':
      return 'text-error'
    case 'warning':
      return 'text-warning'
    case 'info':
      return 'text-info'
    default:
      return 'text-info'
  }
}
</script>

<style scoped>
/* Toast animations */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* Hover effects */
.alert {
  transition: all 0.2s ease;
}

.alert:hover {
  transform: translateX(-4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
