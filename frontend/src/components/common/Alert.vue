<template>
  <div 
    class="card shadow-xl border backdrop-blur-sm transition-all duration-300"
    :class="[
      cardClasses,
      sizeClasses,
      {
        'hover:shadow-2xl hover:scale-105': interactive,
        'animate-fade-in-up': animated
      }
    ]"
    @click="handleClick"
  >
    <div class="card-body" :class="bodyClasses">
      <div class="flex items-start gap-3">
        <!-- Icon -->
        <div 
          v-if="showIcon"
          class="rounded-lg flex-shrink-0"
          :class="[iconBgClasses, iconSizeClasses]"
        >
          <Icon 
            :name="iconName" 
            :size="iconSize"
            :class="iconClasses"
          />
        </div>

        <!-- Content -->
        <div class="flex-1 min-w-0">
          <!-- Title -->
          <div 
            v-if="title"
            class="font-medium mb-1"
            :class="[textClasses, titleSizeClasses]"
          >
            {{ title }}
          </div>

          <!-- Message -->
          <div 
            v-if="message"
            class="text-base-content/70"
            :class="messageSizeClasses"
          >
            {{ message }}
          </div>

          <!-- Slot for custom content -->
          <slot />
        </div>

        <!-- Actions -->
        <div v-if="showActions" class="flex gap-2 flex-shrink-0">
          <slot name="actions">
            <button
              v-if="dismissible"
              @click.stop="handleDismiss"
              class="btn btn-ghost btn-xs btn-circle hover:btn-error transition-all duration-200"
              :title="dismissLabel"
            >
              <Icon name="x" size="xs" />
            </button>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

export interface AlertProps {
  type?: 'info' | 'success' | 'warning' | 'error'
  title?: string
  message?: string
  size?: 'sm' | 'md' | 'lg'
  icon?: string
  showIcon?: boolean
  dismissible?: boolean
  dismissLabel?: string
  interactive?: boolean
  animated?: boolean
}

const props = withDefaults(defineProps<AlertProps>(), {
  type: 'info',
  size: 'md',
  showIcon: true,
  dismissible: false,
  dismissLabel: 'Dismiss',
  interactive: false,
  animated: false
})

const emit = defineEmits<{
  dismiss: []
  click: []
}>()

// Computed classes
const cardClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-gradient-to-br from-success/10 to-success/5 border-success/20'
    case 'error':
      return 'bg-gradient-to-br from-error/10 to-error/5 border-error/20'
    case 'warning':
      return 'bg-gradient-to-br from-warning/10 to-warning/5 border-warning/20'
    case 'info':
    default:
      return 'bg-gradient-to-br from-info/10 to-info/5 border-info/20'
  }
})

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'max-w-sm'
    case 'lg':
      return 'max-w-2xl'
    case 'md':
    default:
      return 'max-w-lg'
  }
})

const bodyClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'p-3'
    case 'lg':
      return 'p-6'
    case 'md':
    default:
      return 'p-4'
  }
})

const iconBgClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-success/20'
    case 'error':
      return 'bg-error/20'
    case 'warning':
      return 'bg-warning/20'
    case 'info':
    default:
      return 'bg-info/20'
  }
})

const iconSizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'p-1'
    case 'lg':
      return 'p-3'
    case 'md':
    default:
      return 'p-2'
  }
})

const iconClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text-success'
    case 'error':
      return 'text-error'
    case 'warning':
      return 'text-warning'
    case 'info':
    default:
      return 'text-info'
  }
})

const textClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text-success'
    case 'error':
      return 'text-error'
    case 'warning':
      return 'text-warning'
    case 'info':
    default:
      return 'text-info'
  }
})

const titleSizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-sm'
    case 'lg':
      return 'text-lg'
    case 'md':
    default:
      return 'text-base'
  }
})

const messageSizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-xs'
    case 'lg':
      return 'text-base'
    case 'md':
    default:
      return 'text-sm'
  }
})

const iconName = computed(() => {
  if (props.icon) return props.icon
  
  switch (props.type) {
    case 'success':
      return 'check-circle'
    case 'error':
      return 'x-circle'
    case 'warning':
      return 'alert-triangle'
    case 'info':
    default:
      return 'info'
  }
})

const iconSize = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'xs'
    case 'lg':
      return 'md'
    case 'md':
    default:
      return 'sm'
  }
})

const showActions = computed(() => {
  return props.dismissible || !!$slots.actions
})

// Methods
const handleClick = () => {
  if (props.interactive) {
    emit('click')
  }
}

const handleDismiss = () => {
  emit('dismiss')
}
</script>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

.card {
  cursor: default;
}

.card.hover\:scale-105:hover {
  cursor: pointer;
}
</style>
