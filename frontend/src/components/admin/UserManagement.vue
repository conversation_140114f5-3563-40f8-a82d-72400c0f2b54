<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-6xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="users" size="md" class="mr-2" />
          User Management
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- User Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-primary text-primary-content rounded-lg">
          <div class="stat-title text-primary-content/80">Total Users</div>
          <div class="stat-value text-2xl">{{ users.length }}</div>
        </div>
        <div class="stat bg-success text-success-content rounded-lg">
          <div class="stat-title text-success-content/80">Active Users</div>
          <div class="stat-value text-2xl">{{ activeUsersCount }}</div>
        </div>
        <div class="stat bg-warning text-warning-content rounded-lg">
          <div class="stat-title text-warning-content/80">Pending</div>
          <div class="stat-value text-2xl">{{ pendingUsersCount }}</div>
        </div>
        <div class="stat bg-error text-error-content rounded-lg">
          <div class="stat-title text-error-content/80">Blocked</div>
          <div class="stat-value text-2xl">{{ blockedUsersCount }}</div>
        </div>
      </div>

      <!-- Actions Bar -->
      <div class="flex flex-wrap gap-2 mb-4">
        <button @click="showAddUser = true" class="btn btn-primary btn-sm">
          <Icon name="user-plus" size="sm" class="mr-2" />
          Add User
        </button>
        <button @click="exportUsers" class="btn btn-secondary btn-sm">
          <Icon name="download" size="sm" class="mr-2" />
          Export Users
        </button>
        <button @click="refreshUsers" class="btn btn-accent btn-sm">
          <Icon name="refresh" size="sm" class="mr-2" />
          Refresh
        </button>
        <div class="form-control">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="Search users..." 
            class="input input-bordered input-sm w-64"
          />
        </div>
      </div>

      <!-- Users Table -->
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>User</th>
              <th>Role</th>
              <th>Status</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in filteredUsers" :key="user.id">
              <td>
                <div class="flex items-center space-x-3">
                  <div class="avatar placeholder">
                    <div class="bg-primary text-primary-content rounded-full w-8 h-8">
                      <span class="text-xs">{{ getUserInitials(user.name) }}</span>
                    </div>
                  </div>
                  <div>
                    <div class="font-bold">{{ user.name }}</div>
                    <div class="text-sm opacity-50">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td>
                <div class="badge" :class="getRoleBadgeClass(user.role)">
                  {{ user.role }}
                </div>
              </td>
              <td>
                <div class="badge" :class="getStatusBadgeClass(user.status)">
                  {{ user.status }}
                </div>
              </td>
              <td>{{ formatDate(user.lastLogin) }}</td>
              <td>
                <div class="flex space-x-1">
                  <button @click="editUser(user)" class="btn btn-ghost btn-xs">
                    <Icon name="edit" size="xs" />
                  </button>
                  <button @click="toggleUserStatus(user)" class="btn btn-ghost btn-xs">
                    <Icon :name="user.status === 'active' ? 'pause' : 'play'" size="xs" />
                  </button>
                  <button @click="deleteUser(user)" class="btn btn-ghost btn-xs text-error">
                    <Icon name="trash" size="xs" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex justify-center mt-4">
        <div class="btn-group">
          <button class="btn btn-sm">«</button>
          <button class="btn btn-sm btn-active">1</button>
          <button class="btn btn-sm">2</button>
          <button class="btn btn-sm">3</button>
          <button class="btn btn-sm">»</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add User Modal -->
  <div v-if="showAddUser" class="modal modal-open">
    <div class="modal-box">
      <h3 class="text-lg font-bold mb-4">Add New User</h3>
      <div class="space-y-4">
        <div class="form-control">
          <label class="label">Name</label>
          <input v-model="newUser.name" type="text" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">Email</label>
          <input v-model="newUser.email" type="email" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label">Role</label>
          <select v-model="newUser.role" class="select select-bordered">
            <option value="client">Client</option>
            <option value="staff">Staff</option>
            <option value="admin">Admin</option>
          </select>
        </div>
      </div>
      <div class="modal-action">
        <button @click="addUser" class="btn btn-primary">Add User</button>
        <button @click="showAddUser = false" class="btn">Cancel</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

defineEmits(['close'])

interface User {
  id: number
  name: string
  email: string
  role: 'admin' | 'staff' | 'client'
  status: 'active' | 'pending' | 'blocked'
  lastLogin: string
}

// Mock user data
const users = ref<User[]>([
  { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'admin', status: 'active', lastLogin: '2024-01-15' },
  { id: 2, name: 'John Smith', email: '<EMAIL>', role: 'staff', status: 'active', lastLogin: '2024-01-14' },
  { id: 3, name: 'Jane Doe', email: '<EMAIL>', role: 'client', status: 'active', lastLogin: '2024-01-13' },
  { id: 4, name: 'Bob Wilson', email: '<EMAIL>', role: 'client', status: 'pending', lastLogin: '2024-01-12' },
  { id: 5, name: 'Alice Brown', email: '<EMAIL>', role: 'client', status: 'blocked', lastLogin: '2024-01-10' },
])

const searchQuery = ref('')
const showAddUser = ref(false)
const newUser = ref({ name: '', email: '', role: 'client' as const })

// Computed properties
const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  return users.value.filter(user => 
    user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const activeUsersCount = computed(() => users.value.filter(u => u.status === 'active').length)
const pendingUsersCount = computed(() => users.value.filter(u => u.status === 'pending').length)
const blockedUsersCount = computed(() => users.value.filter(u => u.status === 'blocked').length)

// Methods
const getUserInitials = (name: string) => {
  return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase()
}

const getRoleBadgeClass = (role: string) => {
  switch (role) {
    case 'admin': return 'badge-error'
    case 'staff': return 'badge-warning'
    case 'client': return 'badge-info'
    default: return 'badge-ghost'
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'active': return 'badge-success'
    case 'pending': return 'badge-warning'
    case 'blocked': return 'badge-error'
    default: return 'badge-ghost'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const editUser = (user: User) => {
  console.log('Edit user:', user)
  alert(`Edit user: ${user.name}`)
}

const toggleUserStatus = (user: User) => {
  user.status = user.status === 'active' ? 'blocked' : 'active'
  console.log('Toggled user status:', user)
}

const deleteUser = (user: User) => {
  if (confirm(`Delete user ${user.name}?`)) {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
    }
  }
}

const addUser = () => {
  if (newUser.value.name && newUser.value.email) {
    users.value.push({
      id: Date.now(),
      ...newUser.value,
      status: 'pending',
      lastLogin: new Date().toISOString().split('T')[0]
    })
    newUser.value = { name: '', email: '', role: 'client' }
    showAddUser.value = false
  }
}

const exportUsers = () => {
  console.log('Exporting users...')
  alert('User export functionality would be implemented here')
}

const refreshUsers = () => {
  console.log('Refreshing users...')
  alert('Users refreshed!')
}
</script>
