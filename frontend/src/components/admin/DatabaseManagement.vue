<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-5xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="database" size="md" class="mr-2" />
          Database Management
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- Database Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-primary text-primary-content rounded-lg">
          <div class="stat-title text-primary-content/80">Total Records</div>
          <div class="stat-value text-2xl">{{ dbStats.totalRecords.toLocaleString() }}</div>
        </div>
        <div class="stat bg-secondary text-secondary-content rounded-lg">
          <div class="stat-title text-secondary-content/80">Database Size</div>
          <div class="stat-value text-2xl">{{ dbStats.size }}</div>
        </div>
        <div class="stat bg-accent text-accent-content rounded-lg">
          <div class="stat-title text-accent-content/80">Tables</div>
          <div class="stat-value text-2xl">{{ dbStats.tables }}</div>
        </div>
        <div class="stat bg-info text-info-content rounded-lg">
          <div class="stat-title text-info-content/80">Connections</div>
          <div class="stat-value text-2xl">{{ dbStats.connections }}</div>
        </div>
      </div>

      <!-- Database Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Backup & Restore -->
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">
              <Icon name="database" size="sm" />
              Backup & Restore
            </h4>
            <div class="space-y-3">
              <button @click="createBackup" class="btn btn-primary btn-block">
                <Icon name="download" size="sm" class="mr-2" />
                Create Full Backup
              </button>
              <button @click="createIncrementalBackup" class="btn btn-secondary btn-block">
                <Icon name="save" size="sm" class="mr-2" />
                Incremental Backup
              </button>
              <button @click="restoreDatabase" class="btn btn-warning btn-block">
                <Icon name="upload" size="sm" class="mr-2" />
                Restore Database
              </button>
            </div>
            <div class="mt-4">
              <p class="text-sm text-base-content/70">
                Last backup: {{ lastBackup }}
              </p>
            </div>
          </div>
        </div>

        <!-- Maintenance -->
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">
              <Icon name="cog" size="sm" />
              Maintenance
            </h4>
            <div class="space-y-3">
              <button @click="optimizeDatabase" class="btn btn-accent btn-block">
                <Icon name="zap" size="sm" class="mr-2" />
                Optimize Database
              </button>
              <button @click="repairTables" class="btn btn-info btn-block">
                <Icon name="wrench" size="sm" class="mr-2" />
                Repair Tables
              </button>
              <button @click="analyzePerformance" class="btn btn-success btn-block">
                <Icon name="chart-bar" size="sm" class="mr-2" />
                Analyze Performance
              </button>
            </div>
            <div class="mt-4">
              <p class="text-sm text-base-content/70">
                Last optimization: {{ lastOptimization }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Database Tables -->
      <div class="card bg-base-100 border border-base-200 mb-6">
        <div class="card-body">
          <h4 class="card-title text-lg mb-4">
            <Icon name="table" size="sm" />
            Database Tables
          </h4>
          <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Table Name</th>
                  <th>Records</th>
                  <th>Size</th>
                  <th>Last Updated</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="table in tables" :key="table.name">
                  <td class="font-mono">{{ table.name }}</td>
                  <td>{{ table.records.toLocaleString() }}</td>
                  <td>{{ table.size }}</td>
                  <td>{{ formatDate(table.lastUpdated) }}</td>
                  <td>
                    <div class="flex space-x-1">
                      <button @click="viewTable(table)" class="btn btn-ghost btn-xs">
                        <Icon name="eye" size="xs" />
                      </button>
                      <button @click="optimizeTable(table)" class="btn btn-ghost btn-xs">
                        <Icon name="zap" size="xs" />
                      </button>
                      <button @click="exportTable(table)" class="btn btn-ghost btn-xs">
                        <Icon name="download" size="xs" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Query Console -->
      <div class="card bg-base-100 border border-base-200">
        <div class="card-body">
          <h4 class="card-title text-lg mb-4">
            <Icon name="terminal" size="sm" />
            SQL Query Console
          </h4>
          <div class="form-control mb-4">
            <textarea 
              v-model="sqlQuery" 
              class="textarea textarea-bordered h-32 font-mono text-sm"
              placeholder="Enter SQL query here..."
            ></textarea>
          </div>
          <div class="flex gap-2 mb-4">
            <button @click="executeQuery" class="btn btn-primary">
              <Icon name="play" size="sm" class="mr-2" />
              Execute Query
            </button>
            <button @click="clearQuery" class="btn btn-ghost">
              <Icon name="x" size="sm" class="mr-2" />
              Clear
            </button>
            <button @click="saveQuery" class="btn btn-secondary">
              <Icon name="save" size="sm" class="mr-2" />
              Save Query
            </button>
          </div>
          
          <!-- Query Results -->
          <div v-if="queryResults" class="mt-4">
            <h5 class="font-semibold mb-2">Query Results:</h5>
            <div class="bg-base-200 p-4 rounded-lg">
              <pre class="text-sm">{{ queryResults }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-action">
        <button @click="refreshStats" class="btn btn-accent">
          <Icon name="refresh" size="sm" class="mr-2" />
          Refresh Stats
        </button>
        <button @click="$emit('close')" class="btn">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Icon from '@/components/common/Icon.vue'

defineEmits(['close'])

interface Table {
  name: string
  records: number
  size: string
  lastUpdated: string
}

const dbStats = ref({
  totalRecords: 125847,
  size: '2.4 GB',
  tables: 15,
  connections: 8
})

const tables = ref<Table[]>([
  { name: 'users', records: 1247, size: '156 MB', lastUpdated: '2024-01-15T10:30:00Z' },
  { name: 'contacts', records: 5632, size: '234 MB', lastUpdated: '2024-01-15T09:45:00Z' },
  { name: 'projects', records: 892, size: '89 MB', lastUpdated: '2024-01-15T08:20:00Z' },
  { name: 'invoices', records: 3421, size: '445 MB', lastUpdated: '2024-01-14T16:30:00Z' },
  { name: 'audit_logs', records: 98456, size: '1.2 GB', lastUpdated: '2024-01-15T10:35:00Z' },
])

const sqlQuery = ref('SELECT * FROM users LIMIT 10;')
const queryResults = ref('')
const lastBackup = ref('2024-01-14 02:00:00')
const lastOptimization = ref('2024-01-13 03:30:00')

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const createBackup = () => {
  console.log('Creating full backup...')
  alert('Full database backup started. This may take several minutes.')
}

const createIncrementalBackup = () => {
  console.log('Creating incremental backup...')
  alert('Incremental backup completed successfully!')
}

const restoreDatabase = () => {
  if (confirm('This will restore the database from the latest backup. Continue?')) {
    console.log('Restoring database...')
    alert('Database restore initiated. System will be unavailable during restore.')
  }
}

const optimizeDatabase = () => {
  console.log('Optimizing database...')
  alert('Database optimization started. Performance may be affected temporarily.')
}

const repairTables = () => {
  console.log('Repairing tables...')
  alert('Table repair completed successfully!')
}

const analyzePerformance = () => {
  console.log('Analyzing performance...')
  alert('Performance analysis completed. Check logs for detailed report.')
}

const viewTable = (table: Table) => {
  console.log('Viewing table:', table.name)
  alert(`Viewing table: ${table.name}`)
}

const optimizeTable = (table: Table) => {
  console.log('Optimizing table:', table.name)
  alert(`Table ${table.name} optimized successfully!`)
}

const exportTable = (table: Table) => {
  console.log('Exporting table:', table.name)
  alert(`Exporting table: ${table.name}`)
}

const executeQuery = () => {
  if (!sqlQuery.value.trim()) {
    alert('Please enter a SQL query')
    return
  }
  
  console.log('Executing query:', sqlQuery.value)
  queryResults.value = `Query executed successfully!\nRows affected: 10\nExecution time: 0.045s`
}

const clearQuery = () => {
  sqlQuery.value = ''
  queryResults.value = ''
}

const saveQuery = () => {
  console.log('Saving query:', sqlQuery.value)
  alert('Query saved to favorites!')
}

const refreshStats = () => {
  console.log('Refreshing database stats...')
  dbStats.value.connections = Math.floor(Math.random() * 20) + 5
  alert('Database statistics refreshed!')
}
</script>
