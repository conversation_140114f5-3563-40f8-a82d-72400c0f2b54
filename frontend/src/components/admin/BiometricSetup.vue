<template>
  <div class="biometric-setup">
    <div class="card bg-base-100 shadow-lg">
      <div class="card-header">
        <h3 class="card-title flex items-center">
          <Icon name="fingerprint" size="md" class="mr-2" />
          Biometric Authentication Setup
        </h3>
        <div class="badge badge-primary">Enhanced Security</div>
      </div>
      
      <div class="card-body space-y-6">
        <!-- Capability Check -->
        <div class="alert" :class="capabilityAlertClass">
          <Icon :name="capabilityIcon" size="sm" />
          <div>
            <div class="font-semibold">{{ capabilityTitle }}</div>
            <div class="text-sm">{{ capabilityMessage }}</div>
          </div>
        </div>

        <!-- Current Status -->
        <div class="bg-base-200 rounded-lg p-4">
          <h4 class="font-semibold mb-3">Current Status</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center justify-between">
              <span class="text-sm">Biometric Support</span>
              <div class="badge" :class="capabilities.supported ? 'badge-success' : 'badge-error'">
                {{ capabilities.supported ? 'Supported' : 'Not Supported' }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm">Device Available</span>
              <div class="badge" :class="capabilities.available ? 'badge-success' : 'badge-error'">
                {{ capabilities.available ? 'Available' : 'Not Available' }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm">Credentials Registered</span>
              <div class="badge" :class="capabilities.hasCredentials ? 'badge-success' : 'badge-warning'">
                {{ capabilities.hasCredentials ? 'Registered' : 'Not Registered' }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm">Authenticators</span>
              <div class="text-sm">{{ capabilities.authenticators.join(', ') || 'None' }}</div>
            </div>
          </div>
        </div>

        <!-- Setup Actions -->
        <div v-if="capabilities.supported && capabilities.available" class="space-y-4">
          <div class="divider">Setup Actions</div>
          
          <!-- Register New Credential -->
          <div class="card bg-base-200">
            <div class="card-body">
              <h5 class="card-title text-base">Register Biometric Credential</h5>
              <p class="text-sm text-base-content/70 mb-4">
                Register your fingerprint or face for secure admin access.
              </p>
              
              <div class="flex items-center space-x-4">
                <button
                  @click="registerBiometric"
                  class="btn btn-primary"
                  :disabled="isLoading"
                  :class="{ 'loading': isLoading }"
                >
                  <Icon v-if="!isLoading" name="plus" size="sm" class="mr-2" />
                  {{ isLoading ? 'Registering...' : 'Register Biometric' }}
                </button>
                
                <div v-if="registrationStatus" class="flex items-center">
                  <Icon 
                    :name="registrationStatus === 'success' ? 'check-circle' : 'x-circle'" 
                    size="sm" 
                    :class="registrationStatus === 'success' ? 'text-success' : 'text-error'"
                    class="mr-2"
                  />
                  <span class="text-sm">
                    {{ registrationStatus === 'success' ? 'Registration successful!' : 'Registration failed' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Test Authentication -->
          <div class="card bg-base-200">
            <div class="card-body">
              <h5 class="card-title text-base">Test Authentication</h5>
              <p class="text-sm text-base-content/70 mb-4">
                Test your biometric authentication to ensure it's working correctly.
              </p>
              
              <div class="flex items-center space-x-4">
                <button
                  @click="testBiometric"
                  class="btn btn-secondary"
                  :disabled="isLoading || !capabilities.hasCredentials"
                  :class="{ 'loading': isLoading }"
                >
                  <Icon v-if="!isLoading" name="play" size="sm" class="mr-2" />
                  {{ isLoading ? 'Testing...' : 'Test Authentication' }}
                </button>
                
                <div v-if="testStatus" class="flex items-center">
                  <Icon 
                    :name="testStatus === 'success' ? 'check-circle' : 'x-circle'" 
                    size="sm" 
                    :class="testStatus === 'success' ? 'text-success' : 'text-error'"
                    class="mr-2"
                  />
                  <span class="text-sm">
                    {{ testStatus === 'success' ? 'Authentication successful!' : 'Authentication failed' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Security Settings -->
          <div class="card bg-base-200">
            <div class="card-body">
              <h5 class="card-title text-base">Security Settings</h5>
              <div class="space-y-3">
                <label class="label cursor-pointer justify-start">
                  <input
                    type="checkbox"
                    v-model="settings.requireBiometric"
                    class="checkbox checkbox-primary"
                  />
                  <span class="label-text ml-3">Require biometric for admin access</span>
                </label>
                
                <label class="label cursor-pointer justify-start">
                  <input
                    type="checkbox"
                    v-model="settings.fallbackToPin"
                    class="checkbox checkbox-primary"
                  />
                  <span class="label-text ml-3">Allow PIN fallback if biometric fails</span>
                </label>
                
                <label class="label cursor-pointer justify-start">
                  <input
                    type="checkbox"
                    v-model="settings.rememberDevice"
                    class="checkbox checkbox-primary"
                  />
                  <span class="label-text ml-3">Remember trusted devices</span>
                </label>
              </div>
              
              <div class="card-actions justify-end mt-4">
                <button @click="saveSettings" class="btn btn-primary btn-sm">
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Troubleshooting -->
        <div class="collapse collapse-arrow bg-base-200">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            Troubleshooting & FAQ
          </div>
          <div class="collapse-content text-sm space-y-2">
            <div>
              <strong>Q: Biometric authentication is not working?</strong>
              <p>A: Make sure your device supports biometric authentication and that you have set up fingerprint or face recognition in your device settings.</p>
            </div>
            <div>
              <strong>Q: Can I use multiple biometric methods?</strong>
              <p>A: Yes, you can register multiple biometric credentials for redundancy.</p>
            </div>
            <div>
              <strong>Q: What happens if biometric fails?</strong>
              <p>A: You can fall back to PIN or password authentication depending on your settings.</p>
            </div>
            <div>
              <strong>Q: Is biometric data stored on the server?</strong>
              <p>A: No, biometric data never leaves your device. Only cryptographic keys are used for authentication.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import Icon from '@/components/common/Icon.vue'

const authStore = useAuthStore()

// State
const isLoading = ref(false)
const registrationStatus = ref<'success' | 'error' | null>(null)
const testStatus = ref<'success' | 'error' | null>(null)

const capabilities = reactive({
  supported: false,
  available: false,
  authenticators: [] as string[],
  hasCredentials: false
})

const settings = reactive({
  requireBiometric: false,
  fallbackToPin: true,
  rememberDevice: false
})

// Computed properties
const capabilityAlertClass = computed(() => {
  if (!capabilities.supported) return 'alert-error'
  if (!capabilities.available) return 'alert-warning'
  if (!capabilities.hasCredentials) return 'alert-info'
  return 'alert-success'
})

const capabilityIcon = computed(() => {
  if (!capabilities.supported) return 'x-circle'
  if (!capabilities.available) return 'exclamation-triangle'
  if (!capabilities.hasCredentials) return 'info'
  return 'check-circle'
})

const capabilityTitle = computed(() => {
  if (!capabilities.supported) return 'Biometric Authentication Not Supported'
  if (!capabilities.available) return 'Biometric Device Not Available'
  if (!capabilities.hasCredentials) return 'Biometric Setup Required'
  return 'Biometric Authentication Ready'
})

const capabilityMessage = computed(() => {
  if (!capabilities.supported) {
    return 'Your browser or device does not support biometric authentication.'
  }
  if (!capabilities.available) {
    return 'No biometric devices found. Please ensure your device has fingerprint or face recognition enabled.'
  }
  if (!capabilities.hasCredentials) {
    return 'Register your biometric credentials to enable secure authentication.'
  }
  return 'Biometric authentication is configured and ready to use.'
})

// Methods
const checkCapabilities = async () => {
  try {
    const caps = await authStore.getBiometricCapabilities()
    Object.assign(capabilities, caps)
  } catch (error) {
    console.error('Failed to check biometric capabilities:', error)
  }
}

const registerBiometric = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    registrationStatus.value = null

    await authStore.setupBiometric()
    registrationStatus.value = 'success'
    
    // Refresh capabilities
    await checkCapabilities()
    
    // Clear status after 3 seconds
    setTimeout(() => {
      registrationStatus.value = null
    }, 3000)

  } catch (error: any) {
    console.error('Biometric registration failed:', error)
    registrationStatus.value = 'error'
    
    setTimeout(() => {
      registrationStatus.value = null
    }, 3000)
  } finally {
    isLoading.value = false
  }
}

const testBiometric = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    testStatus.value = null

    await authStore.loginWithBiometric()
    testStatus.value = 'success'
    
    setTimeout(() => {
      testStatus.value = null
    }, 3000)

  } catch (error: any) {
    console.error('Biometric test failed:', error)
    testStatus.value = 'error'
    
    setTimeout(() => {
      testStatus.value = null
    }, 3000)
  } finally {
    isLoading.value = false
  }
}

const saveSettings = () => {
  // In a real implementation, save settings to the server
  localStorage.setItem('admin_biometric_settings', JSON.stringify(settings))
  
  // Show success message
  const toast = document.createElement('div')
  toast.className = 'toast toast-top toast-end'
  toast.innerHTML = `
    <div class="alert alert-success">
      <span>Settings saved successfully!</span>
    </div>
  `
  document.body.appendChild(toast)
  
  setTimeout(() => {
    document.body.removeChild(toast)
  }, 3000)
}

const loadSettings = () => {
  try {
    const saved = localStorage.getItem('admin_biometric_settings')
    if (saved) {
      Object.assign(settings, JSON.parse(saved))
    }
  } catch (error) {
    console.warn('Failed to load biometric settings:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await checkCapabilities()
  loadSettings()
})
</script>

<style scoped>
.card-header {
  @apply flex items-center justify-between p-6 border-b border-base-200;
}

.card-title {
  @apply text-lg font-semibold;
}

.toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
}
</style>
