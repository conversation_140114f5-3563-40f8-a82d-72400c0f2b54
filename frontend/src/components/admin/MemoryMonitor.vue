<template>
  <div class="memory-monitor">
    <div class="card bg-base-100 shadow-lg">
      <div class="card-header">
        <h3 class="card-title flex items-center">
          <Icon name="activity" size="md" class="mr-2" />
          Memory & Performance Monitor
        </h3>
        <div class="flex items-center space-x-2">
          <button 
            @click="toggleMonitoring"
            :class="[
              'btn btn-sm',
              isMonitoring ? 'btn-error' : 'btn-primary'
            ]"
          >
            {{ isMonitoring ? 'Stop' : 'Start' }} Monitoring
          </button>
          <button @click="clearData" class="btn btn-ghost btn-sm">
            Clear Data
          </button>
        </div>
      </div>
      
      <div class="card-body">
        <!-- Memory Usage Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Heap Used</div>
            <div class="stat-value text-primary">
              {{ formatBytes(memoryStats.usedJSHeapSize) }}
            </div>
            <div class="stat-desc">
              {{ ((memoryStats.usedJSHeapSize / memoryStats.totalJSHeapSize) * 100).toFixed(2) }}% of total
            </div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Total Heap</div>
            <div class="stat-value text-secondary">
              {{ formatBytes(memoryStats.totalJSHeapSize) }}
            </div>
            <div class="stat-desc">
              Limit: {{ formatBytes(memoryStats.jsHeapSizeLimit) }}
            </div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Memory Pressure</div>
            <div class="stat-value" :class="{
              'text-success': memoryPressure < 70,
              'text-warning': memoryPressure >= 70 && memoryPressure < 90,
              'text-error': memoryPressure >= 90
            }">
              {{ memoryPressure.toFixed(2) }}%
            </div>
            <div class="stat-desc">
              {{ getPressureStatus() }}
            </div>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-3">Performance Metrics</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="metric-card">
              <div class="metric-label">FPS</div>
              <div class="metric-value">{{ currentFPS }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">DOM Nodes</div>
              <div class="metric-value">{{ domNodeCount }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Event Listeners</div>
              <div class="metric-value">{{ eventListenerCount }}</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Socket Connections</div>
              <div class="metric-value">{{ socketConnections }}</div>
            </div>
          </div>
        </div>

        <!-- Memory Chart -->
        <div class="mb-6">
          <h4 class="text-lg font-semibold mb-3">Memory Usage Over Time</h4>
          <div class="chart-container bg-base-200 rounded-lg p-4" style="height: 200px;">
            <canvas ref="chartCanvas" width="800" height="200"></canvas>
          </div>
        </div>

        <!-- Potential Issues -->
        <div v-if="potentialIssues.length > 0" class="mb-6">
          <h4 class="text-lg font-semibold mb-3 text-warning">Potential Issues Detected</h4>
          <div class="space-y-2">
            <div 
              v-for="issue in potentialIssues" 
              :key="issue.id"
              class="alert alert-warning"
            >
              <Icon name="alert-triangle" size="sm" />
              <div>
                <div class="font-semibold">{{ issue.type }}</div>
                <div class="text-sm">{{ issue.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommendations -->
        <div v-if="recommendations.length > 0">
          <h4 class="text-lg font-semibold mb-3 text-info">Recommendations</h4>
          <ul class="list-disc list-inside space-y-1">
            <li v-for="rec in recommendations" :key="rec" class="text-sm">
              {{ rec }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'

interface MemoryStats {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

interface PerformanceData {
  timestamp: number
  memory: MemoryStats
  fps: number
  domNodes: number
}

interface PotentialIssue {
  id: string
  type: string
  description: string
  severity: 'low' | 'medium' | 'high'
}

// Reactive state
const isMonitoring = ref(false)
const memoryStats = ref<MemoryStats>({
  usedJSHeapSize: 0,
  totalJSHeapSize: 0,
  jsHeapSizeLimit: 0
})

const performanceData = ref<PerformanceData[]>([])
const currentFPS = ref(0)
const domNodeCount = ref(0)
const eventListenerCount = ref(0)
const socketConnections = ref(0)
const potentialIssues = ref<PotentialIssue[]>([])
const recommendations = ref<string[]>([])

// Chart reference
const chartCanvas = ref<HTMLCanvasElement>()

// Monitoring intervals
let memoryInterval: number | null = null
let fpsInterval: number | null = null
let chartUpdateInterval: number | null = null

// FPS calculation
let lastFrameTime = 0
let frameCount = 0

// Computed properties
const memoryPressure = computed(() => {
  if (memoryStats.value.totalJSHeapSize === 0) return 0
  return (memoryStats.value.usedJSHeapSize / memoryStats.value.totalJSHeapSize) * 100
})

// Methods
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getPressureStatus = (): string => {
  const pressure = memoryPressure.value
  if (pressure < 70) return 'Normal'
  if (pressure < 90) return 'High'
  return 'Critical'
}

const updateMemoryStats = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    memoryStats.value = {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    }
    
    // Check for memory issues
    checkMemoryIssues()
  }
}

const updatePerformanceMetrics = () => {
  // Update DOM node count
  domNodeCount.value = document.querySelectorAll('*').length
  
  // Estimate event listener count (approximation)
  eventListenerCount.value = estimateEventListeners()
  
  // Get Socket.io connection count
  socketConnections.value = getSocketConnections()
  
  // Store performance data
  performanceData.value.push({
    timestamp: Date.now(),
    memory: { ...memoryStats.value },
    fps: currentFPS.value,
    domNodes: domNodeCount.value
  })
  
  // Keep only last 100 data points
  if (performanceData.value.length > 100) {
    performanceData.value = performanceData.value.slice(-100)
  }
}

const calculateFPS = () => {
  const now = performance.now()
  frameCount++
  
  if (now - lastFrameTime >= 1000) {
    currentFPS.value = Math.round((frameCount * 1000) / (now - lastFrameTime))
    frameCount = 0
    lastFrameTime = now
  }
  
  if (isMonitoring.value) {
    requestAnimationFrame(calculateFPS)
  }
}

const estimateEventListeners = (): number => {
  // This is an approximation - actual count is not directly accessible
  const elements = document.querySelectorAll('*')
  let count = 0
  
  // Check for common event attributes
  elements.forEach(el => {
    const attributes = el.getAttributeNames()
    count += attributes.filter(attr => 
      attr.startsWith('on') || 
      attr.includes('click') || 
      attr.includes('event')
    ).length
  })
  
  return count
}

const getSocketConnections = (): number => {
  // Try to get Socket.io connection count from global state
  try {
    // This would need to be adapted based on your Socket.io implementation
    return 1 // Placeholder
  } catch {
    return 0
  }
}

const checkMemoryIssues = () => {
  const issues: PotentialIssue[] = []
  
  // Check memory pressure
  if (memoryPressure.value > 90) {
    issues.push({
      id: 'high-memory',
      type: 'High Memory Usage',
      description: 'Memory usage is above 90%. Consider optimizing or clearing unused data.',
      severity: 'high'
    })
  }
  
  // Check DOM node count
  if (domNodeCount.value > 5000) {
    issues.push({
      id: 'dom-nodes',
      type: 'High DOM Node Count',
      description: `${domNodeCount.value} DOM nodes detected. Consider virtual scrolling or pagination.`,
      severity: 'medium'
    })
  }
  
  // Check FPS
  if (currentFPS.value < 30 && currentFPS.value > 0) {
    issues.push({
      id: 'low-fps',
      type: 'Low Frame Rate',
      description: `FPS is ${currentFPS.value}. Performance may be degraded.`,
      severity: 'medium'
    })
  }
  
  potentialIssues.value = issues
  
  // Generate recommendations
  generateRecommendations()
}

const generateRecommendations = () => {
  const recs: string[] = []
  
  if (memoryPressure.value > 80) {
    recs.push('Clear unused reactive data and component instances')
    recs.push('Implement virtual scrolling for large lists')
    recs.push('Use object pooling for frequently created/destroyed objects')
  }
  
  if (domNodeCount.value > 3000) {
    recs.push('Consider pagination or lazy loading for large datasets')
    recs.push('Use v-show instead of v-if for frequently toggled elements')
  }
  
  if (currentFPS.value < 45 && currentFPS.value > 0) {
    recs.push('Optimize animations and transitions')
    recs.push('Debounce expensive operations')
    recs.push('Use CSS transforms instead of changing layout properties')
  }
  
  recommendations.value = recs
}

const updateChart = () => {
  if (!chartCanvas.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return
  
  const canvas = chartCanvas.value
  const width = canvas.width
  const height = canvas.height
  
  // Clear canvas
  ctx.clearRect(0, 0, width, height)
  
  if (performanceData.value.length < 2) return
  
  // Draw memory usage line
  ctx.strokeStyle = '#3b82f6'
  ctx.lineWidth = 2
  ctx.beginPath()
  
  performanceData.value.forEach((data, index) => {
    const x = (index / (performanceData.value.length - 1)) * width
    const y = height - ((data.memory.usedJSHeapSize / data.memory.totalJSHeapSize) * height)
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.stroke()
}

const toggleMonitoring = () => {
  isMonitoring.value = !isMonitoring.value
  
  if (isMonitoring.value) {
    startMonitoring()
  } else {
    stopMonitoring()
  }
}

const startMonitoring = () => {
  // Clear any existing intervals first
  stopMonitoring()

  // Update memory stats every second
  memoryInterval = setInterval(updateMemoryStats, 1000)

  // Update performance metrics every 2 seconds
  fpsInterval = setInterval(updatePerformanceMetrics, 2000)

  // Update chart every 3 seconds
  chartUpdateInterval = setInterval(updateChart, 3000)

  // Start FPS calculation
  lastFrameTime = performance.now()
  frameCount = 0
  requestAnimationFrame(calculateFPS)

  // Initial updates
  updateMemoryStats()
  updatePerformanceMetrics()
}

const stopMonitoring = () => {
  if (memoryInterval) {
    clearInterval(memoryInterval)
    memoryInterval = null
  }

  if (fpsInterval) {
    clearInterval(fpsInterval)
    fpsInterval = null
  }

  if (chartUpdateInterval) {
    clearInterval(chartUpdateInterval)
    chartUpdateInterval = null
  }

  // Reset monitoring state
  isMonitoring.value = false

  // Clear any pending animation frames
  if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
    // Cancel any pending animation frames (FPS calculation)
    let id = requestAnimationFrame(() => {})
    cancelAnimationFrame(id)
  }
}

const clearData = () => {
  performanceData.value = []
  potentialIssues.value = []
  recommendations.value = []
  updateChart()
}

// Lifecycle
onMounted(() => {
  // Check if performance.memory is available
  if (!('memory' in performance)) {
    potentialIssues.value.push({
      id: 'no-memory-api',
      type: 'Memory API Unavailable',
      description: 'Performance.memory API is not available in this browser.',
      severity: 'low'
    })
  }
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.metric-card {
  @apply bg-base-200 rounded-lg p-3 text-center;
}

.metric-label {
  @apply text-xs text-base-content/70 font-medium;
}

.metric-value {
  @apply text-lg font-bold;
  color: hsl(var(--p));
}

.chart-container {
  position: relative;
}

.chart-container canvas {
  width: 100%;
  height: 100%;
}
</style>
