<template>
  <div class="min-h-screen bg-gradient-to-br from-base-200 via-base-300 to-base-200 flex items-center justify-center p-4">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0 bg-pattern"></div>
    </div>

    <!-- Login Card -->
    <div class="card w-full max-w-md bg-base-100 shadow-2xl border border-base-300 relative z-10">
      <div class="card-body p-8">
        <!-- Header -->
        <div class="text-center mb-8">
          <div class="flex justify-center mb-4">
            <div class="p-4 bg-primary/20 rounded-full">
              <Icon name="shield-check" size="2xl" class="text-primary" />
            </div>
          </div>
          <h2 class="text-3xl font-bold text-base-content">Admin Access</h2>
          <p class="text-base-content/60 mt-2">Secure authentication required</p>
        </div>

        <!-- Authentication Methods Tabs -->
        <div class="tabs tabs-boxed mb-6">
          <button
            @click="authMethod = 'password'"
            :class="['tab', { 'tab-active': authMethod === 'password' }]"
          >
            <Icon name="key" size="sm" class="mr-2" />
            Password
          </button>
          <button
            v-if="hasUserSession"
            @click="authMethod = 'pin'"
            :class="['tab', { 'tab-active': authMethod === 'pin' }]"
          >
            <Icon name="hashtag" size="sm" class="mr-2" />
            PIN
          </button>
          <button
            v-if="hasUserSession"
            @click="authMethod = 'biometric'"
            :class="['tab', { 'tab-active': authMethod === 'biometric' }]"
            :disabled="!biometricAvailable"
          >
            <Icon name="fingerprint" size="sm" class="mr-2" />
            Biometric
          </button>
        </div>

        <!-- Error/Success Messages -->
        <div v-if="message.text" :class="[
          'alert mb-4',
          message.type === 'error' ? 'alert-error' : 'alert-success'
        ]">
          <Icon :name="message.type === 'error' ? 'error' : 'check-circle'" size="sm" />
          <span>{{ message.text }}</span>
        </div>

        <!-- Password Authentication -->
        <form v-if="authMethod === 'password'" @submit.prevent="handlePasswordLogin" class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Admin Email</span>
            </label>
            <div class="relative">
              <input
                type="email"
                v-model="passwordForm.email"
                class="input input-bordered w-full pl-10"
                placeholder="<EMAIL>"
                :disabled="isLoading"
                required
              />
              <Icon name="mail" size="sm" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50" />
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Password</span>
            </label>
            <div class="relative">
              <input
                :type="showPassword ? 'text' : 'password'"
                v-model="passwordForm.password"
                class="input input-bordered w-full pl-10 pr-10"
                placeholder="Enter admin password"
                :disabled="isLoading"
                required
              />
              <Icon name="lock" size="sm" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/50" />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-base-content/50 hover:text-base-content"
              >
                <Icon :name="showPassword ? 'eye-slash' : 'eye'" size="sm" />
              </button>
            </div>
          </div>

          <button
            type="submit"
            class="btn btn-primary w-full"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
            <Icon v-if="!isLoading" name="login" size="sm" class="mr-2" />
            {{ isLoading ? 'Authenticating...' : 'Sign In' }}
          </button>
        </form>

        <!-- PIN Authentication -->
        <div v-if="authMethod === 'pin'" class="space-y-6">
          <div class="text-center">
            <p class="text-sm text-base-content/70 mb-4">Enter your 6-digit admin PIN</p>
            <div class="flex justify-center space-x-2 mb-6">
              <input
                v-for="(digit, index) in pinDigits"
                :key="index"
                :ref="`pinInput${index}`"
                type="password"
                maxlength="1"
                v-model="pinDigits[index]"
                @input="handlePinInput(index, $event)"
                @keydown="handlePinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :disabled="isLoading"
              />
            </div>
          </div>

          <button
            @click="handlePinLogin"
            class="btn btn-primary w-full"
            :disabled="isLoading || pinDigits.some(d => !d)"
          >
            <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
            <Icon v-if="!isLoading" name="unlock" size="sm" class="mr-2" />
            {{ isLoading ? 'Verifying...' : 'Verify PIN' }}
          </button>

          <div class="text-center">
            <button @click="clearPin" class="btn btn-ghost btn-sm">
              <Icon name="refresh" size="sm" class="mr-2" />
              Clear PIN
            </button>
          </div>
        </div>

        <!-- Biometric Authentication -->
        <div v-if="authMethod === 'biometric'" class="space-y-6 text-center">
          <div class="py-8">
            <div class="flex justify-center mb-4">
              <div class="p-6 bg-primary/10 rounded-full">
                <Icon 
                  :name="biometricIcon" 
                  size="3xl" 
                  :class="[
                    'transition-all duration-300',
                    biometricStatus === 'scanning' ? 'text-warning animate-pulse' :
                    biometricStatus === 'success' ? 'text-success' :
                    biometricStatus === 'error' ? 'text-error' : 'text-primary'
                  ]"
                />
              </div>
            </div>
            <h3 class="text-lg font-semibold mb-2">{{ biometricTitle }}</h3>
            <p class="text-sm text-base-content/70">{{ biometricMessage }}</p>
          </div>

          <button
            @click="handleBiometricAuth"
            class="btn btn-primary w-full"
            :disabled="isLoading || !biometricAvailable"
          >
            <span v-if="isLoading" class="loading loading-spinner loading-sm"></span>
            <Icon v-if="!isLoading" name="fingerprint" size="sm" class="mr-2" />
            {{ isLoading ? 'Scanning...' : 'Authenticate' }}
          </button>

          <div v-if="!biometricAvailable" class="alert alert-warning">
            <Icon name="exclamation-triangle" size="sm" />
            <span>Biometric authentication not available on this device</span>
          </div>
        </div>

        <!-- Additional Options -->
        <div class="divider text-xs">Additional Options</div>
        
        <div class="flex justify-between items-center text-sm">
          <label class="label cursor-pointer">
            <input
              type="checkbox"
              v-model="rememberDevice"
              class="checkbox checkbox-primary checkbox-sm"
              :disabled="isLoading"
            />
            <span class="label-text ml-2">Remember this device</span>
          </label>
          
          <button @click="showForgotPassword = true" class="link link-secondary text-xs">
            Forgot credentials?
          </button>
        </div>

        <!-- Security Info -->
        <div class="mt-6 p-3 bg-base-200 rounded-lg">
          <div class="flex items-center text-xs text-base-content/70">
            <Icon name="shield" size="sm" class="mr-2" />
            <span>Protected by enterprise-grade security</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Forgot Password Modal -->
    <div v-if="showForgotPassword" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="card w-96 bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title">Reset Admin Credentials</h3>
          <p class="text-sm text-base-content/70 mb-4">
            Contact your system administrator to reset your admin credentials.
          </p>
          <div class="card-actions justify-end">
            <button @click="showForgotPassword = false" class="btn btn-ghost">Close</button>
            <button @click="contactAdmin" class="btn btn-primary">Contact Admin</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Setup Wizard Modal -->
    <div v-if="showSetupWizard" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <div class="text-center mb-6">
          <Icon name="shield-check" size="lg" class="text-primary mx-auto mb-4" />
          <h3 class="text-xl font-bold">Enhance Your Security</h3>
          <p class="text-sm text-base-content/70 mt-2">
            Set up PIN and biometric authentication for faster, more secure access
          </p>

          <!-- General Error Message -->
          <div v-if="setupErrors.general" class="alert alert-error mt-4">
            <Icon name="exclamation-triangle" size="sm" />
            <span class="text-sm">{{ setupErrors.general }}</span>
          </div>
        </div>

        <!-- PIN Setup Step -->
        <div v-if="setupStep === 'pin'" class="space-y-4">
          <div class="text-center">
            <h4 class="font-semibold mb-2">Create Your Admin PIN</h4>
            <p class="text-sm text-base-content/70 mb-4">Choose a 6-digit PIN for quick access</p>

            <!-- PIN Error Message -->
            <div v-if="setupErrors.pin" class="alert alert-error mb-4">
              <Icon name="exclamation-triangle" size="sm" />
              <span class="text-sm">{{ setupErrors.pin }}</span>
            </div>

            <div class="flex justify-center space-x-2 mb-4">
              <input
                v-for="(digit, index) in pinDigits"
                :key="index"
                :data-pin-index="index"
                type="password"
                maxlength="1"
                v-model="pinDigits[index]"
                @input="handleSetupPinInput(index, $event)"
                @keydown="handlePinKeydown(index, $event)"
                class="input input-bordered w-12 h-12 text-center text-lg font-bold"
                :class="{ 'input-error': setupErrors.pin }"
                :disabled="setupLoading.pin"
              />
            </div>

            <!-- PIN Requirements -->
            <div class="text-xs text-base-content/60 mb-4">
              <p>• Use 6 digits only</p>
              <p>• Avoid simple patterns (123456, 111111)</p>
              <p>• Remember this PIN for future logins</p>
            </div>
          </div>

          <div class="flex space-x-3">
            <button
              @click="skipPinSetup"
              class="btn btn-ghost flex-1"
              :disabled="setupLoading.pin"
            >
              Skip for Now
            </button>
            <button
              @click="createAdminPin"
              class="btn btn-primary flex-1"
              :disabled="setupLoading.pin || pinDigits.some(d => !d)"
            >
              <span v-if="setupLoading.pin" class="loading loading-spinner loading-sm"></span>
              <Icon v-if="!setupLoading.pin" name="check" size="sm" class="mr-2" />
              {{ setupLoading.pin ? 'Creating PIN...' : 'Create PIN' }}
            </button>
          </div>
        </div>

        <!-- Biometric Setup Step -->
        <div v-if="setupStep === 'biometric'" class="space-y-4">
          <div class="text-center">
            <h4 class="font-semibold mb-2">Enable Biometric Authentication</h4>
            <p class="text-sm text-base-content/70 mb-4">
              Use your fingerprint or Face ID for secure access
            </p>

            <!-- Biometric Error Message -->
            <div v-if="setupErrors.biometric" class="alert alert-error mb-4">
              <Icon name="exclamation-triangle" size="sm" />
              <span class="text-sm">{{ setupErrors.biometric }}</span>
            </div>

            <div class="my-6">
              <Icon name="fingerprint" size="xl" class="text-primary mx-auto" />

              <!-- Biometric Status -->
              <div v-if="setupLoading.biometric" class="flex items-center justify-center space-x-2 mt-4">
                <span class="loading loading-spinner loading-sm"></span>
                <span class="text-sm">Setting up biometric authentication...</span>
              </div>
            </div>
          </div>

          <div v-if="!biometricAvailable" class="alert alert-warning">
            <Icon name="exclamation-triangle" size="sm" />
            <span>Biometric authentication not available on this device</span>
          </div>

          <!-- Biometric Instructions -->
          <div v-if="biometricAvailable && !setupLoading.biometric" class="text-xs text-base-content/60 text-center mb-4">
            <p>• Follow your browser's prompts</p>
            <p>• Use your device's fingerprint or Face ID</p>
            <p>• This will be saved securely for future logins</p>
          </div>

          <div class="flex space-x-3">
            <button
              @click="skipBiometricSetup"
              class="btn btn-ghost flex-1"
              :disabled="setupLoading.biometric"
            >
              Skip for Now
            </button>
            <button
              @click="setupBiometric"
              class="btn btn-primary flex-1"
              :disabled="setupLoading.biometric || !biometricAvailable"
            >
              <span v-if="setupLoading.biometric" class="loading loading-spinner loading-sm"></span>
              <Icon v-if="!setupLoading.biometric" name="fingerprint" size="sm" class="mr-2" />
              {{ setupLoading.biometric ? 'Setting up...' : 'Set Up Biometric' }}
            </button>
          </div>
        </div>

        <!-- Complete Step -->
        <div v-if="setupStep === 'complete'" class="space-y-4 text-center">
          <Icon name="check-circle" size="xl" class="text-success mx-auto" />
          <h4 class="font-semibold">Setup Complete!</h4>
          <p class="text-sm text-base-content/70">
            Your enhanced authentication is now active. Next time you can use:
          </p>

          <div class="space-y-2">
            <div v-if="setupData.pinCreated" class="flex items-center justify-center space-x-2">
              <Icon name="unlock" size="sm" class="text-primary" />
              <span class="text-sm">6-digit PIN</span>
            </div>
            <div v-if="setupData.biometricRegistered" class="flex items-center justify-center space-x-2">
              <Icon name="fingerprint" size="sm" class="text-primary" />
              <span class="text-sm">Biometric authentication</span>
            </div>
          </div>

          <button
            @click="completeSetup"
            class="btn btn-primary w-full"
          >
            Continue to Admin Panel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { authService } from '@/services/auth'
import Icon from '@/components/common/Icon.vue'

const router = useRouter()
const authStore = useAuthStore()

// Authentication method and flow state
const authMethod = ref<'password' | 'pin' | 'biometric'>('password')
const showSetupWizard = ref(false)
const isFirstLogin = ref(false)
const availableAuthMethods = ref<string[]>([])

// Loading state
const isLoading = ref(false)

// Message state
const message = reactive({
  text: '',
  type: 'error' as 'error' | 'success'
})

// Password form
const passwordForm = reactive({
  email: '<EMAIL>',
  password: ''
})

const showPassword = ref(false)

// Setup wizard state
const setupStep = ref<'pin' | 'biometric' | 'complete'>('pin')
const setupData = reactive({
  pinCreated: false,
  biometricRegistered: false,
  skipBiometric: false
})

// Loading states for setup wizard
const setupLoading = reactive({
  pin: false,
  biometric: false,
  checking: false
})

// Error states for setup wizard
const setupErrors = reactive({
  pin: '',
  biometric: '',
  general: ''
})

// Clear setup errors
const clearSetupErrors = () => {
  setupErrors.pin = ''
  setupErrors.biometric = ''
  setupErrors.general = ''
}

// PIN form
const pinDigits = ref(['', '', '', '', '', ''])
const pinInput0 = ref<HTMLInputElement>()
const pinInput1 = ref<HTMLInputElement>()
const pinInput2 = ref<HTMLInputElement>()
const pinInput3 = ref<HTMLInputElement>()
const pinInput4 = ref<HTMLInputElement>()
const pinInput5 = ref<HTMLInputElement>()

// Biometric state
const biometricAvailable = ref(false)
const biometricStatus = ref<'idle' | 'scanning' | 'success' | 'error'>('idle')

// Additional options
const rememberDevice = ref(false)
const showForgotPassword = ref(false)

// Computed properties
const hasUserSession = computed(() => {
  // Check if user is logged in via auth store or has valid session data
  return authStore.isAuthenticated ||
         localStorage.getItem('auth_token') ||
         sessionStorage.getItem('auth_token')
})
const biometricIcon = computed(() => {
  switch (biometricStatus.value) {
    case 'scanning': return 'fingerprint'
    case 'success': return 'check-circle'
    case 'error': return 'error'
    default: return 'fingerprint'
  }
})

const biometricTitle = computed(() => {
  switch (biometricStatus.value) {
    case 'scanning': return 'Scanning...'
    case 'success': return 'Authentication Successful'
    case 'error': return 'Authentication Failed'
    default: return 'Biometric Authentication'
  }
})

const biometricMessage = computed(() => {
  switch (biometricStatus.value) {
    case 'scanning': return 'Please place your finger on the sensor or look at the camera'
    case 'success': return 'Access granted'
    case 'error': return 'Please try again'
    default: return 'Use your fingerprint or face to authenticate'
  }
})

// Methods
const getPinInput = (index: number): HTMLInputElement | undefined => {
  const refs = [pinInput0, pinInput1, pinInput2, pinInput3, pinInput4, pinInput5]
  return refs[index]?.value
}

const setMessage = (text: string, type: 'error' | 'success' = 'error') => {
  message.text = text
  message.type = type
  setTimeout(() => {
    message.text = ''
  }, 5000)
}

const handlePasswordLogin = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    message.text = ''

    // Validate admin credentials
    if (!passwordForm.email.includes('admin')) {
      throw new Error('Admin email required')
    }

    await authStore.login({
      email: passwordForm.email,
      password: passwordForm.password
    })

    if (!authStore.isAdmin) {
      throw new Error('Admin privileges required')
    }

    // Check if this is first login or if enhanced auth is not set up
    const hasEnhancedAuth = await checkEnhancedAuthSetup()

    if (!hasEnhancedAuth && !localStorage.getItem('admin_enhanced_auth_setup')) {
      // First time login - show setup wizard
      isFirstLogin.value = true
      showSetupWizard.value = true
      setMessage('Welcome! Let\'s set up enhanced security for your account.', 'success')
    } else {
      // Regular login - proceed to admin panel immediately
      // Store device if remember is checked
      if (rememberDevice.value) {
        localStorage.setItem('admin_device_trusted', 'true')
      }

      // Redirect immediately without success message
      router.push('/admin')
    }

  } catch (error: any) {
    console.error('❌ Admin login failed:', error)

    // Handle specific error types
    let errorMessage = 'Authentication failed'

    if (error.message) {
      if (error.message.includes('Invalid email or password')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.'
      } else if (error.message.includes('Validation failed')) {
        errorMessage = 'Please fill in all required fields with valid information.'
      } else if (error.message.includes('Network error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.'
      } else if (error.message.includes('Session expired')) {
        errorMessage = 'Your session has expired. Please try logging in again.'
      } else if (error.message.includes('Account locked')) {
        errorMessage = 'Your account has been temporarily locked. Please contact support.'
      } else if (error.message.includes('Too many attempts')) {
        errorMessage = 'Too many login attempts. Please wait a few minutes before trying again.'
      } else {
        errorMessage = error.message
      }
    }

    setMessage(errorMessage)
  } finally {
    isLoading.value = false
  }
}

const handlePinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  if (value && /^\d$/.test(value)) {
    pinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        const nextInput = getPinInput(index + 1)
        if (nextInput && typeof nextInput.focus === 'function') {
          nextInput.focus()
        }
      })
    }
  } else {
    target.value = ''
    pinDigits.value[index] = ''
  }
}

const handlePinKeydown = (index: number, event: KeyboardEvent) => {
  if (event.key === 'Backspace' && !pinDigits.value[index] && index > 0) {
    nextTick(() => {
      const prevInput = getPinInput(index - 1)
      if (prevInput && typeof prevInput.focus === 'function') {
        prevInput.focus()
      }
    })
  }
}

const clearPin = () => {
  pinDigits.value = ['', '', '', '', '', '']
  nextTick(() => {
    const firstInput = getPinInput(0)
    if (firstInput && typeof firstInput.focus === 'function') {
      firstInput.focus()
    }
  })
}

const handlePinLogin = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    message.text = ''

    const pin = pinDigits.value.join('')

    // Call the auth store PIN login method
    await authStore.loginWithPin(pin)

    // Save preferred auth method
    localStorage.setItem('admin_preferred_auth_method', 'pin')

    // Store device if remember is checked
    if (rememberDevice.value) {
      localStorage.setItem('admin_device_trusted', 'true')
    }

    // Redirect immediately without success message
    router.push('/admin')

  } catch (error: any) {
    console.error('❌ PIN authentication failed:', error)

    // Handle specific PIN error types
    let errorMessage = 'Invalid PIN. Please try again.'

    if (error.message) {
      if (error.message.includes('Invalid PIN')) {
        errorMessage = 'Invalid PIN. Please check your PIN and try again.'
      } else if (error.message.includes('PIN locked')) {
        errorMessage = 'PIN authentication is temporarily locked due to too many failed attempts. Please wait and try again later.'
      } else if (error.message.includes('Account locked')) {
        errorMessage = 'Your account has been temporarily locked. Please contact support.'
      } else if (error.message.includes('Session expired')) {
        errorMessage = 'Your session has expired. Please refresh the page and try again.'
      } else if (error.message.includes('Network error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.'
      } else {
        errorMessage = error.message
      }
    }

    setMessage(errorMessage)
    clearPin()
  } finally {
    isLoading.value = false
  }
}

const checkBiometricAvailability = async () => {
  try {
    // Check if WebAuthn is available
    if ('credentials' in navigator && 'create' in navigator.credentials) {
      biometricAvailable.value = true
    }
  } catch (error) {
    console.warn('Biometric authentication not available:', error)
  }
}

const handleBiometricAuth = async () => {
  if (isLoading.value || !biometricAvailable.value) return

  try {
    isLoading.value = true
    biometricStatus.value = 'scanning'
    message.text = ''

    // Determine if this is a session unlock or full login
    const isSessionUnlock = authStore.isAuthenticated && authStore.user

    let result
    if (isSessionUnlock) {
      // Use biometric unlock for existing session
      result = await authStore.unlockWithBiometric()
    } else {
      // Use biometric login for new session
      result = await authStore.loginWithBiometric()
    }

    if (result.success) {
      biometricStatus.value = 'success'

      // Save preferred auth method
      localStorage.setItem('admin_preferred_auth_method', 'biometric')

      // Store device if remember is checked
      if (rememberDevice.value) {
        localStorage.setItem('admin_device_trusted', 'true')
      }

      // Redirect immediately without success message
      router.push('/admin')
    } else {
      biometricStatus.value = 'error'
      throw new Error('Biometric authentication failed')
    }

  } catch (error: any) {
    console.error('❌ Biometric authentication failed:', error)
    biometricStatus.value = 'error'

    // Determine error message based on context and error type
    const isSessionUnlock = authStore.isAuthenticated && authStore.user
    let errorMessage = isSessionUnlock ? 'Biometric unlock failed' : 'Biometric authentication failed'

    if (error.message) {
      if (error.message.includes('NotAllowedError') || error.message.includes('cancelled')) {
        errorMessage = 'Biometric authentication was cancelled. Please try again.'
      } else if (error.message.includes('NotSupportedError')) {
        errorMessage = 'Biometric authentication is not supported on this device.'
      } else if (error.message.includes('SecurityError')) {
        errorMessage = 'Security error: Please ensure you\'re using a secure connection (HTTPS).'
      } else if (error.message.includes('Network error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.'
      } else if (error.message.includes('Invalid biometric')) {
        errorMessage = 'Biometric verification failed. Please try again or use an alternative method.'
      } else if (error.message.includes('Session expired')) {
        errorMessage = 'Your session has expired. Please refresh the page and try again.'
      } else {
        errorMessage = error.message
      }
    }

    setMessage(errorMessage)
    setTimeout(() => {
      biometricStatus.value = 'idle'
    }, 3000)
  } finally {
    isLoading.value = false
  }
}

const contactAdmin = () => {
  // In a real app, this would open a support ticket or send an email
  setMessage('Admin contact request sent', 'success')
  showForgotPassword.value = false
}

// Enhanced authentication setup methods
const checkEnhancedAuthSetup = async () => {
  try {
    // Check if user has PIN or biometric auth set up
    const data = await authService.getAuthMethods()
    availableAuthMethods.value = data.methods || []
    return data.methods.length > 0
  } catch (error) {
    console.warn('Could not check enhanced auth setup:', error)
    return false
  }
}

const handleSetupPinInput = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // Clear PIN errors when user starts typing
  if (setupErrors.pin) {
    setupErrors.pin = ''
  }

  if (value && /^\d$/.test(value)) {
    pinDigits.value[index] = value

    // Move to next input
    if (index < 5) {
      nextTick(() => {
        const nextInput = document.querySelector(`input[data-pin-index="${index + 1}"]`) as HTMLInputElement
        nextInput?.focus()
      })
    }
  } else {
    target.value = ''
    pinDigits.value[index] = ''
  }
}

const createAdminPin = async () => {
  if (setupLoading.pin) return

  try {
    setupLoading.pin = true
    clearSetupErrors()

    const pin = pinDigits.value.join('')

    // Validate PIN
    if (pin.length !== 6) {
      throw new Error('PIN must be exactly 6 digits')
    }

    if (!/^\d{6}$/.test(pin)) {
      throw new Error('PIN must contain only numbers')
    }

    // Check for weak patterns
    const weakPatterns = ['123456', '654321', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999', '000000']
    if (weakPatterns.includes(pin)) {
      throw new Error('Please choose a more secure PIN. Avoid simple patterns.')
    }

    // Use the auth service to create PIN
    const result = await authService.createPin(pin, await generateDeviceFingerprint())

    if (result.success) {
      setupData.pinCreated = true
      setupStep.value = 'biometric'
      clearPin()
      setMessage('PIN created successfully!', 'success')
    } else {
      throw new Error('Failed to create PIN')
    }
  } catch (error: any) {
    setupErrors.pin = error.message || 'Failed to create PIN'
    setMessage(error.message || 'Failed to create PIN', 'error')
  } finally {
    setupLoading.pin = false
  }
}

const skipPinSetup = () => {
  clearSetupErrors()
  setupStep.value = 'biometric'
  clearPin()
}

const setupBiometric = async () => {
  if (setupLoading.biometric || !biometricAvailable.value) return

  try {
    setupLoading.biometric = true
    clearSetupErrors()

    // Check if biometric is still available
    if (!biometricAvailable.value) {
      throw new Error('Biometric authentication is not available on this device')
    }

    // Use the biometric service to register
    const credential = await authStore.setupBiometric()

    if (credential.success) {
      setupData.biometricRegistered = true
      setupStep.value = 'complete'
      setMessage('Biometric authentication set up successfully!', 'success')
    } else {
      throw new Error('Failed to set up biometric authentication')
    }
  } catch (error: any) {
    let errorMessage = 'Failed to set up biometric authentication'

    // Handle specific biometric errors
    if (error.name === 'NotAllowedError') {
      errorMessage = 'Biometric authentication was cancelled or denied'
    } else if (error.name === 'NotSupportedError') {
      errorMessage = 'Biometric authentication is not supported on this device'
    } else if (error.name === 'SecurityError') {
      errorMessage = 'Security error: Please ensure you\'re using HTTPS'
    } else if (error.message) {
      errorMessage = error.message
    }

    setupErrors.biometric = errorMessage
    setMessage(errorMessage, 'error')
  } finally {
    setupLoading.biometric = false
  }
}

const skipBiometricSetup = () => {
  clearSetupErrors()
  setupData.skipBiometric = true
  setupStep.value = 'complete'
}

const completeSetup = () => {
  // Mark setup as completed
  localStorage.setItem('admin_enhanced_auth_setup', 'true')

  // Store device if remember is checked
  if (rememberDevice.value) {
    localStorage.setItem('admin_device_trusted', 'true')
  }

  showSetupWizard.value = false

  setTimeout(() => {
    router.push('/admin')
  }, 500)
}

const generateDeviceFingerprint = async () => {
  // Generate a simple device fingerprint
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx?.fillText('Device fingerprint', 10, 10)

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')

  // Simple hash
  let hash = 0
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36)
}

// Lifecycle
onMounted(async () => {
  await checkBiometricAvailability()

  // Check if user has a valid session
  if (!hasUserSession.value) {
    // No user session - force password authentication
    authMethod.value = 'password'
    return
  }

  // Check if user has enhanced auth set up and device is trusted
  const hasEnhancedAuth = localStorage.getItem('admin_enhanced_auth_setup')
  const isTrustedDevice = localStorage.getItem('admin_device_trusted')

  if (hasEnhancedAuth && isTrustedDevice) {
    // Try to determine the best auth method
    await determineAuthMethod()
  } else {
    // Default to password for first-time or untrusted devices
    authMethod.value = 'password'
  }

  // Focus appropriate input based on auth method
  nextTick(() => {
    if (authMethod.value === 'pin') {
      const firstInput = getPinInput(0)
      if (firstInput && typeof firstInput.focus === 'function') {
        firstInput.focus()
      }
    } else if (authMethod.value === 'password') {
      const passwordInput = document.querySelector('input[type="password"]') as HTMLInputElement
      if (passwordInput && typeof passwordInput.focus === 'function') {
        passwordInput.focus()
      }
    }
  })
})

const determineAuthMethod = async () => {
  try {
    // Check what auth methods are available for this user
    const savedMethod = localStorage.getItem('admin_preferred_auth_method')

    if (savedMethod === 'biometric' && biometricAvailable.value) {
      authMethod.value = 'biometric'
    } else if (savedMethod === 'pin') {
      authMethod.value = 'pin'
    } else {
      // Auto-detect best available method
      if (biometricAvailable.value) {
        authMethod.value = 'biometric'
      } else {
        authMethod.value = 'pin'
      }
    }
  } catch (error) {
    console.warn('Could not determine auth method:', error)
    authMethod.value = 'password'
  }
}
</script>

<style scoped>
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.tabs-boxed .tab {
  @apply transition-all duration-200;
}

.tabs-boxed .tab:hover {
  background-color: hsl(var(--b3));
}

.tabs-boxed .tab-active {
  background-color: hsl(var(--p));
  color: hsl(var(--pc));
}

/* PIN input styling */
input[type="password"]::-webkit-textfield-decoration-container {
  display: none;
}

/* Biometric animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}
</style>
