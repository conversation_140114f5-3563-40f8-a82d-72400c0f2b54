<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-5xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="shield" size="md" class="mr-2" />
          Security Settings
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- Security Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-success text-success-content rounded-lg">
          <div class="stat-title text-success-content/80">Security Score</div>
          <div class="stat-value text-2xl">{{ securityScore }}%</div>
        </div>
        <div class="stat bg-warning text-warning-content rounded-lg">
          <div class="stat-title text-warning-content/80">Failed Logins</div>
          <div class="stat-value text-2xl">{{ failedLogins }}</div>
        </div>
        <div class="stat bg-info text-info-content rounded-lg">
          <div class="stat-title text-info-content/80">Active Sessions</div>
          <div class="stat-value text-2xl">{{ activeSessions }}</div>
        </div>
        <div class="stat bg-error text-error-content rounded-lg">
          <div class="stat-title text-error-content/80">Blocked IPs</div>
          <div class="stat-value text-2xl">{{ blockedIPs }}</div>
        </div>
      </div>

      <!-- Security Tabs -->
      <div class="tabs tabs-boxed mb-6">
        <a 
          v-for="tab in tabs" 
          :key="tab.id"
          @click="activeTab = tab.id"
          class="tab"
          :class="{ 'tab-active': activeTab === tab.id }"
        >
          <Icon :name="tab.icon" size="sm" class="mr-2" />
          {{ tab.name }}
        </a>
      </div>

      <!-- Authentication Settings -->
      <div v-if="activeTab === 'auth'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Password Policy</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">Minimum Password Length</label>
                <input v-model="security.minPasswordLength" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Password Expiry (days)</label>
                <input v-model="security.passwordExpiry" type="number" class="input input-bordered" />
              </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Require Uppercase</span>
                  <input v-model="security.requireUppercase" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Require Numbers</span>
                  <input v-model="security.requireNumbers" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Require Special Characters</span>
                  <input v-model="security.requireSpecialChars" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Enable 2FA</span>
                  <input v-model="security.enable2FA" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Session Management</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">Session Timeout (minutes)</label>
                <input v-model="security.sessionTimeout" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">Max Concurrent Sessions</label>
                <input v-model="security.maxSessions" type="number" class="input input-bordered" />
              </div>
            </div>
            <div class="form-control mt-4">
              <label class="label cursor-pointer">
                <span class="label-text">Force Logout on Password Change</span>
                <input v-model="security.forceLogoutOnPasswordChange" type="checkbox" class="toggle toggle-primary" />
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Biometric Authentication -->
      <div v-if="activeTab === 'biometric'" class="space-y-6">
        <BiometricSetup />
      </div>

      <!-- Access Control -->
      <div v-if="activeTab === 'access'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">IP Access Control</h4>
            <div class="form-control mb-4">
              <label class="label">Allowed IP Ranges (one per line)</label>
              <textarea v-model="security.allowedIPs" class="textarea textarea-bordered h-32" placeholder="***********/24&#10;10.0.0.0/8"></textarea>
            </div>
            <div class="form-control">
              <label class="label cursor-pointer">
                <span class="label-text">Enable IP Whitelist</span>
                <input v-model="security.enableIPWhitelist" type="checkbox" class="toggle toggle-primary" />
              </label>
            </div>
          </div>
        </div>

        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Rate Limiting</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">Login Attempts (per hour)</label>
                <input v-model="security.maxLoginAttempts" type="number" class="input input-bordered" />
              </div>
              <div class="form-control">
                <label class="label">API Requests (per minute)</label>
                <input v-model="security.apiRateLimit" type="number" class="input input-bordered" />
              </div>
            </div>
            <div class="form-control mt-4">
              <label class="label cursor-pointer">
                <span class="label-text">Auto-block Suspicious IPs</span>
                <input v-model="security.autoBlockSuspiciousIPs" type="checkbox" class="toggle toggle-warning" />
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Monitoring -->
      <div v-if="activeTab === 'monitoring'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Security Monitoring</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Log Failed Login Attempts</span>
                  <input v-model="security.logFailedLogins" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Log Successful Logins</span>
                  <input v-model="security.logSuccessfulLogins" type="checkbox" class="toggle toggle-primary" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Monitor File Changes</span>
                  <input v-model="security.monitorFileChanges" type="checkbox" class="toggle toggle-warning" />
                </label>
              </div>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">Real-time Alerts</span>
                  <input v-model="security.realtimeAlerts" type="checkbox" class="toggle toggle-error" />
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Security Events -->
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">Recent Security Events</h4>
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Event</th>
                    <th>IP Address</th>
                    <th>Severity</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="event in securityEvents" :key="event.id">
                    <td>{{ formatTime(event.time) }}</td>
                    <td>{{ event.event }}</td>
                    <td class="font-mono">{{ event.ip }}</td>
                    <td>
                      <div class="badge" :class="getSeverityBadgeClass(event.severity)">
                        {{ event.severity }}
                      </div>
                    </td>
                    <td>
                      <button @click="blockIP(event.ip)" class="btn btn-ghost btn-xs text-error">
                        Block IP
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- SSL/TLS Settings -->
      <div v-if="activeTab === 'ssl'" class="space-y-6">
        <div class="card bg-base-100 border border-base-200">
          <div class="card-body">
            <h4 class="card-title text-lg">SSL/TLS Configuration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">SSL Certificate Status</label>
                <div class="badge badge-success">Valid until 2025-01-15</div>
              </div>
              <div class="form-control">
                <label class="label">TLS Version</label>
                <select v-model="security.tlsVersion" class="select select-bordered">
                  <option value="1.2">TLS 1.2</option>
                  <option value="1.3">TLS 1.3</option>
                </select>
              </div>
            </div>
            <div class="form-control mt-4">
              <label class="label cursor-pointer">
                <span class="label-text">Force HTTPS</span>
                <input v-model="security.forceHTTPS" type="checkbox" class="toggle toggle-primary" />
              </label>
            </div>
            <div class="card-actions mt-4">
              <button @click="renewCertificate" class="btn btn-primary">
                <Icon name="shield" size="sm" class="mr-2" />
                Renew Certificate
              </button>
              <button @click="testSSL" class="btn btn-secondary">
                <Icon name="check" size="sm" class="mr-2" />
                Test SSL
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-action">
        <button @click="saveSecuritySettings" class="btn btn-primary">
          <Icon name="save" size="sm" class="mr-2" />
          Save Settings
        </button>
        <button @click="runSecurityScan" class="btn btn-warning">
          <Icon name="shield" size="sm" class="mr-2" />
          Run Security Scan
        </button>
        <button @click="$emit('close')" class="btn">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Icon from '@/components/common/Icon.vue'
import BiometricSetup from '@/components/admin/BiometricSetup.vue'

defineEmits(['close'])

const activeTab = ref('auth')
const securityScore = ref(87)
const failedLogins = ref(23)
const activeSessions = ref(45)
const blockedIPs = ref(12)

const tabs = [
  { id: 'auth', name: 'Authentication', icon: 'key' },
  { id: 'biometric', name: 'Biometric', icon: 'fingerprint' },
  { id: 'access', name: 'Access Control', icon: 'shield' },
  { id: 'monitoring', name: 'Monitoring', icon: 'eye' },
  { id: 'ssl', name: 'SSL/TLS', icon: 'lock' },
]

const security = ref({
  // Authentication
  minPasswordLength: 8,
  passwordExpiry: 90,
  requireUppercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  enable2FA: false,
  sessionTimeout: 30,
  maxSessions: 3,
  forceLogoutOnPasswordChange: true,
  
  // Access Control
  allowedIPs: '***********/24\n10.0.0.0/8',
  enableIPWhitelist: false,
  maxLoginAttempts: 5,
  apiRateLimit: 100,
  autoBlockSuspiciousIPs: true,
  
  // Monitoring
  logFailedLogins: true,
  logSuccessfulLogins: true,
  monitorFileChanges: false,
  realtimeAlerts: true,
  
  // SSL/TLS
  tlsVersion: '1.3',
  forceHTTPS: true,
})

const securityEvents = ref([
  { id: 1, time: '2024-01-15T10:30:00Z', event: 'Failed login attempt', ip: '*************', severity: 'medium' },
  { id: 2, time: '2024-01-15T10:25:00Z', event: 'Suspicious API access', ip: '************', severity: 'high' },
  { id: 3, time: '2024-01-15T10:20:00Z', event: 'Multiple failed logins', ip: '*************', severity: 'high' },
  { id: 4, time: '2024-01-15T10:15:00Z', event: 'Password changed', ip: '************', severity: 'low' },
])

const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleString()
}

const getSeverityBadgeClass = (severity: string) => {
  switch (severity) {
    case 'high': return 'badge-error'
    case 'medium': return 'badge-warning'
    case 'low': return 'badge-info'
    default: return 'badge-ghost'
  }
}

const blockIP = (ip: string) => {
  if (confirm(`Block IP address ${ip}?`)) {
    console.log('Blocking IP:', ip)
    alert(`IP ${ip} has been blocked`)
    blockedIPs.value++
  }
}

const saveSecuritySettings = () => {
  console.log('Saving security settings:', security.value)
  alert('Security settings saved successfully!')
}

const runSecurityScan = () => {
  console.log('Running security scan...')
  alert('Security scan initiated. Results will be available in 5-10 minutes.')
}

const renewCertificate = () => {
  console.log('Renewing SSL certificate...')
  alert('SSL certificate renewal initiated. This may take a few minutes.')
}

const testSSL = () => {
  console.log('Testing SSL configuration...')
  alert('SSL test completed successfully! All configurations are valid.')
}
</script>
