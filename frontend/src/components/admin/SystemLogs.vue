<template>
  <div class="modal modal-open">
    <div class="modal-box max-w-7xl">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-bold text-primary">
          <Icon name="document" size="md" class="mr-2" />
          System Logs
        </h3>
        <button @click="$emit('close')" class="btn btn-ghost btn-circle">
          <Icon name="x" size="md" />
        </button>
      </div>

      <!-- Log Filters -->
      <div class="card bg-base-100 border border-base-200 mb-6">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="form-control">
              <label class="label">Log Level</label>
              <select v-model="filters.level" class="select select-bordered select-sm">
                <option value="">All Levels</option>
                <option value="error">Error</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Category</label>
              <select v-model="filters.category" class="select select-bordered select-sm">
                <option value="">All Categories</option>
                <option value="auth">Authentication</option>
                <option value="api">API</option>
                <option value="database">Database</option>
                <option value="system">System</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Date Range</label>
              <select v-model="filters.dateRange" class="select select-bordered select-sm">
                <option value="today">Today</option>
                <option value="week">Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="all">All time</option>
              </select>
            </div>
            <div class="form-control">
              <label class="label">Search</label>
              <input 
                v-model="filters.search" 
                type="text" 
                placeholder="Search logs..." 
                class="input input-bordered input-sm"
              />
            </div>
          </div>
          <div class="flex gap-2 mt-4">
            <button @click="applyFilters" class="btn btn-primary btn-sm">
              <Icon name="filter" size="sm" class="mr-2" />
              Apply Filters
            </button>
            <button @click="clearFilters" class="btn btn-ghost btn-sm">
              <Icon name="x" size="sm" class="mr-2" />
              Clear
            </button>
            <button @click="refreshLogs" class="btn btn-accent btn-sm">
              <Icon name="refresh" size="sm" class="mr-2" />
              Refresh
            </button>
            <button @click="exportLogs" class="btn btn-secondary btn-sm">
              <Icon name="download" size="sm" class="mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      <!-- Log Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stat bg-error text-error-content rounded-lg">
          <div class="stat-title text-error-content/80">Errors</div>
          <div class="stat-value text-2xl">{{ logStats.errors }}</div>
        </div>
        <div class="stat bg-warning text-warning-content rounded-lg">
          <div class="stat-title text-warning-content/80">Warnings</div>
          <div class="stat-value text-2xl">{{ logStats.warnings }}</div>
        </div>
        <div class="stat bg-info text-info-content rounded-lg">
          <div class="stat-title text-info-content/80">Info</div>
          <div class="stat-value text-2xl">{{ logStats.info }}</div>
        </div>
        <div class="stat bg-success text-success-content rounded-lg">
          <div class="stat-title text-success-content/80">Debug</div>
          <div class="stat-value text-2xl">{{ logStats.debug }}</div>
        </div>
      </div>

      <!-- Log Entries -->
      <div class="card bg-base-100 border border-base-200">
        <div class="card-body">
          <div class="flex justify-between items-center mb-4">
            <h4 class="card-title text-lg">Log Entries</h4>
            <div class="flex gap-2">
              <button @click="toggleAutoRefresh" class="btn btn-ghost btn-sm">
                <Icon :name="autoRefresh ? 'pause' : 'play'" size="sm" class="mr-2" />
                {{ autoRefresh ? 'Pause' : 'Auto Refresh' }}
              </button>
              <button @click="clearAllLogs" class="btn btn-error btn-sm">
                <Icon name="trash" size="sm" class="mr-2" />
                Clear All
              </button>
            </div>
          </div>

          <!-- Log Table -->
          <div class="overflow-x-auto max-h-96">
            <table class="table table-zebra table-pin-rows w-full">
              <thead>
                <tr>
                  <th class="w-32">Time</th>
                  <th class="w-20">Level</th>
                  <th class="w-24">Category</th>
                  <th>Message</th>
                  <th class="w-24">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="log in filteredLogs" :key="log.id" :class="getLogRowClass(log.level)">
                  <td class="font-mono text-xs">{{ formatTime(log.timestamp) }}</td>
                  <td>
                    <div class="badge badge-sm" :class="getLevelBadgeClass(log.level)">
                      {{ log.level.toUpperCase() }}
                    </div>
                  </td>
                  <td>
                    <div class="badge badge-outline badge-sm">
                      {{ log.category }}
                    </div>
                  </td>
                  <td class="font-mono text-sm">
                    <div class="max-w-md truncate" :title="log.message">
                      {{ log.message }}
                    </div>
                    <div v-if="log.details" class="text-xs text-base-content/60 mt-1">
                      {{ log.details }}
                    </div>
                  </td>
                  <td>
                    <div class="flex space-x-1">
                      <button @click="viewLogDetails(log)" class="btn btn-ghost btn-xs">
                        <Icon name="eye" size="xs" />
                      </button>
                      <button @click="copyLog(log)" class="btn btn-ghost btn-xs">
                        <Icon name="copy" size="xs" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-base-content/70">
              Showing {{ filteredLogs.length }} of {{ logs.length }} entries
            </div>
            <div class="btn-group">
              <button class="btn btn-sm">«</button>
              <button class="btn btn-sm btn-active">1</button>
              <button class="btn btn-sm">2</button>
              <button class="btn btn-sm">3</button>
              <button class="btn btn-sm">»</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-action">
        <button @click="downloadLogs" class="btn btn-secondary">
          <Icon name="download" size="sm" class="mr-2" />
          Download Logs
        </button>
        <button @click="$emit('close')" class="btn">Close</button>
      </div>
    </div>
  </div>

  <!-- Log Details Modal -->
  <div v-if="selectedLog" class="modal modal-open">
    <div class="modal-box max-w-2xl">
      <h3 class="text-lg font-bold mb-4">Log Details</h3>
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="label">Timestamp</label>
            <div class="font-mono text-sm">{{ formatTime(selectedLog.timestamp) }}</div>
          </div>
          <div>
            <label class="label">Level</label>
            <div class="badge" :class="getLevelBadgeClass(selectedLog.level)">
              {{ selectedLog.level.toUpperCase() }}
            </div>
          </div>
          <div>
            <label class="label">Category</label>
            <div class="badge badge-outline">{{ selectedLog.category }}</div>
          </div>
          <div>
            <label class="label">Source</label>
            <div class="font-mono text-sm">{{ selectedLog.source || 'N/A' }}</div>
          </div>
        </div>
        <div>
          <label class="label">Message</label>
          <div class="bg-base-200 p-4 rounded-lg font-mono text-sm">
            {{ selectedLog.message }}
          </div>
        </div>
        <div v-if="selectedLog.stackTrace">
          <label class="label">Stack Trace</label>
          <div class="bg-base-200 p-4 rounded-lg font-mono text-xs max-h-40 overflow-y-auto">
            <pre>{{ selectedLog.stackTrace }}</pre>
          </div>
        </div>
      </div>
      <div class="modal-action">
        <button @click="copyLogDetails" class="btn btn-secondary">Copy</button>
        <button @click="selectedLog = null" class="btn">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

defineEmits(['close'])

interface LogEntry {
  id: number
  timestamp: string
  level: 'error' | 'warning' | 'info' | 'debug'
  category: string
  message: string
  details?: string
  source?: string
  stackTrace?: string
}

const logs = ref<LogEntry[]>([
  { id: 1, timestamp: '2024-01-15T10:35:23Z', level: 'error', category: 'auth', message: 'Failed login attempt for user: <EMAIL>', details: 'Invalid password provided', source: 'AuthController.login' },
  { id: 2, timestamp: '2024-01-15T10:34:15Z', level: 'warning', category: 'api', message: 'Rate limit exceeded for IP: *************', details: 'Exceeded 100 requests per minute', source: 'RateLimitMiddleware' },
  { id: 3, timestamp: '2024-01-15T10:33:45Z', level: 'info', category: 'system', message: 'Database backup completed successfully', details: 'Backup size: 2.4GB, Duration: 45 seconds', source: 'BackupService' },
  { id: 4, timestamp: '2024-01-15T10:32:12Z', level: 'debug', category: 'database', message: 'Query executed: SELECT * FROM users WHERE active = 1', details: 'Execution time: 0.045s, Rows returned: 1247', source: 'DatabaseManager' },
  { id: 5, timestamp: '2024-01-15T10:31:33Z', level: 'error', category: 'system', message: 'Disk space warning: /var/log partition at 85%', details: 'Available space: 2.1GB', source: 'SystemMonitor' },
])

const filters = ref({
  level: '',
  category: '',
  dateRange: 'today',
  search: ''
})

const selectedLog = ref<LogEntry | null>(null)
const autoRefresh = ref(false)
let refreshInterval: number | null = null

const logStats = computed(() => {
  return {
    errors: logs.value.filter(l => l.level === 'error').length,
    warnings: logs.value.filter(l => l.level === 'warning').length,
    info: logs.value.filter(l => l.level === 'info').length,
    debug: logs.value.filter(l => l.level === 'debug').length,
  }
})

const filteredLogs = computed(() => {
  let filtered = logs.value

  if (filters.value.level) {
    filtered = filtered.filter(log => log.level === filters.value.level)
  }

  if (filters.value.category) {
    filtered = filtered.filter(log => log.category === filters.value.category)
  }

  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(log => 
      log.message.toLowerCase().includes(search) ||
      log.details?.toLowerCase().includes(search)
    )
  }

  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}

const getLevelBadgeClass = (level: string) => {
  switch (level) {
    case 'error': return 'badge-error'
    case 'warning': return 'badge-warning'
    case 'info': return 'badge-info'
    case 'debug': return 'badge-success'
    default: return 'badge-ghost'
  }
}

const getLogRowClass = (level: string) => {
  switch (level) {
    case 'error': return 'bg-error/5'
    case 'warning': return 'bg-warning/5'
    default: return ''
  }
}

const applyFilters = () => {
  console.log('Applying filters:', filters.value)
}

const clearFilters = () => {
  filters.value = {
    level: '',
    category: '',
    dateRange: 'today',
    search: ''
  }
}

const refreshLogs = () => {
  console.log('Refreshing logs...')
  // Add a new mock log entry
  logs.value.unshift({
    id: Date.now(),
    timestamp: new Date().toISOString(),
    level: 'info',
    category: 'system',
    message: 'Logs refreshed manually',
    details: 'User requested log refresh',
    source: 'LogViewer'
  })
}

const exportLogs = () => {
  console.log('Exporting logs...')
  alert('Log export initiated. Download will start shortly.')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshInterval = setInterval(refreshLogs, 5000)
  } else if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

const clearAllLogs = () => {
  if (confirm('Clear all logs? This action cannot be undone.')) {
    logs.value = []
  }
}

const viewLogDetails = (log: LogEntry) => {
  selectedLog.value = log
}

const copyLog = (log: LogEntry) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.category}: ${log.message}`
  navigator.clipboard.writeText(logText)
  alert('Log entry copied to clipboard!')
}

const copyLogDetails = () => {
  if (selectedLog.value) {
    const details = JSON.stringify(selectedLog.value, null, 2)
    navigator.clipboard.writeText(details)
    alert('Log details copied to clipboard!')
  }
}

const downloadLogs = () => {
  console.log('Downloading logs...')
  alert('Log download started. File will be saved to your downloads folder.')
}

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
