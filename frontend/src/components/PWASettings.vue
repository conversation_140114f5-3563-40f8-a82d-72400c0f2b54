<template>
  <div class="pwa-settings">
    <!-- PWA Status Card -->
    <div class="card bg-base-100 shadow-xl border border-base-300/50 mb-6">
      <div class="card-body">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-2xl font-bold text-base-content">PWA Status</h3>
          <div class="badge" :class="pwaStatusBadgeClass">
            {{ pwaStatusText }}
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Installation Status -->
          <div class="stat">
            <div class="stat-figure text-primary">
              <Icon :name="isInstalled ? 'check-circle' : 'download'" size="lg" />
            </div>
            <div class="stat-title">Installation</div>
            <div class="stat-value text-lg" :class="isInstalled ? 'text-success' : 'text-warning'">
              {{ isInstalled ? 'Installed' : 'Not Installed' }}
            </div>
            <div class="stat-desc">{{ isStandalone ? 'Running as app' : 'Running in browser' }}</div>
          </div>

          <!-- Network Status -->
          <div class="stat">
            <div class="stat-figure text-secondary">
              <Icon :name="isOnline ? 'wifi' : 'wifi-off'" size="lg" />
            </div>
            <div class="stat-title">Network</div>
            <div class="stat-value text-lg" :class="isOnline ? 'text-success' : 'text-error'">
              {{ networkStatus }}
            </div>
            <div class="stat-desc">{{ isOnline ? 'Connected' : 'Offline mode' }}</div>
          </div>

          <!-- Cache Status -->
          <div class="stat">
            <div class="stat-figure text-accent">
              <Icon name="database" size="lg" />
            </div>
            <div class="stat-title">Cache</div>
            <div class="stat-value text-lg text-info">{{ formattedCacheSize }}</div>
            <div class="stat-desc">{{ cacheStats.totalPages }} pages cached</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Installation Section -->
    <div v-if="!isInstalled" class="card bg-base-100 shadow-xl border border-base-300/50 mb-6">
      <div class="card-body">
        <h3 class="text-xl font-bold text-primary mb-4">
          <Icon name="download" size="md" class="mr-2" />
          Install HLenergy App
        </h3>
        <p class="text-base-content/70 mb-6">
          Install HLenergy as a native app for faster access, offline functionality, and a better user experience.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div class="space-y-3">
            <h4 class="font-semibold text-base-content">Benefits:</h4>
            <ul class="space-y-2 text-sm text-base-content/80">
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Works offline
              </li>
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Faster loading
              </li>
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Native app experience
              </li>
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Push notifications
              </li>
            </ul>
          </div>
          
          <div class="space-y-3">
            <h4 class="font-semibold text-base-content">Requirements:</h4>
            <ul class="space-y-2 text-sm text-base-content/80">
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Modern browser
              </li>
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                HTTPS connection
              </li>
              <li class="flex items-center">
                <Icon name="check" size="sm" class="text-success mr-2" />
                Service worker support
              </li>
            </ul>
          </div>
        </div>

        <div class="card-actions justify-center">
          <button 
            @click="handleInstallPrompt" 
            class="btn btn-primary btn-lg"
            :disabled="!showInstallPrompt"
          >
            <Icon name="download" size="sm" class="mr-2" />
            Install App
          </button>
          <button 
            v-if="!showInstallPrompt" 
            @click="forceShowInstallPrompt" 
            class="btn btn-outline btn-lg"
          >
            Show Install Option
          </button>
        </div>
      </div>
    </div>

    <!-- Notifications Section -->
    <div class="card bg-base-100 shadow-xl border border-base-300/50 mb-6">
      <div class="card-body">
        <h3 class="text-xl font-bold text-secondary mb-4">
          <Icon name="bell" size="md" class="mr-2" />
          Push Notifications
        </h3>
        
        <div class="flex items-center justify-between mb-4">
          <div>
            <p class="font-medium text-base-content">Enable Notifications</p>
            <p class="text-sm text-base-content/70">
              Get notified about important updates and messages
            </p>
          </div>
          <div class="form-control">
            <label class="label cursor-pointer">
              <input 
                type="checkbox" 
                class="toggle toggle-primary" 
                :checked="isNotificationsSubscribed"
                @change="toggleNotifications"
                :disabled="!notificationPermission.supported || isNotificationsSubscribing"
              />
            </label>
          </div>
        </div>

        <div v-if="notificationPermission.state === 'denied'" class="alert alert-warning">
          <Icon name="warning" size="sm" />
          <span>Notifications are blocked. Please enable them in your browser settings.</span>
        </div>

        <div v-if="!notificationPermission.supported" class="alert alert-info">
          <Icon name="info" size="sm" />
          <span>Push notifications are not supported in this browser.</span>
        </div>

        <div v-if="isNotificationsSubscribed" class="mt-4">
          <button @click="testNotification" class="btn btn-outline btn-sm">
            <Icon name="bell" size="sm" class="mr-2" />
            Test Notification
          </button>
        </div>
      </div>
    </div>

    <!-- Cache Management Section -->
    <div class="card bg-base-100 shadow-xl border border-base-300/50 mb-6">
      <div class="card-body">
        <h3 class="text-xl font-bold text-accent mb-4">
          <Icon name="database" size="md" class="mr-2" />
          Offline Cache
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div class="space-y-3">
            <div class="stat bg-base-200 rounded-lg p-4">
              <div class="stat-title">Cache Size</div>
              <div class="stat-value text-lg">{{ formattedCacheSize }}</div>
              <div class="stat-desc">{{ cacheStats.totalPages }} pages stored</div>
            </div>
          </div>
          
          <div class="space-y-3">
            <div class="stat bg-base-200 rounded-lg p-4">
              <div class="stat-title">Last Updated</div>
              <div class="stat-value text-lg">{{ lastUpdatedFormatted }}</div>
              <div class="stat-desc">Cache efficiency: {{ cacheEfficiency }}%</div>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap gap-3">
          <button @click="preloadEssentialPages" class="btn btn-primary btn-sm" :disabled="isLoading">
            <Icon name="download" size="sm" class="mr-2" />
            <span v-if="!isLoading">Preload Essential Pages</span>
            <span v-else>Loading...</span>
          </button>
          
          <button @click="preloadUserPages" class="btn btn-secondary btn-sm" :disabled="isLoading">
            <Icon name="user" size="sm" class="mr-2" />
            Preload User Pages
          </button>
          
          <button @click="refreshCacheStats" class="btn btn-outline btn-sm">
            <Icon name="refresh" size="sm" class="mr-2" />
            Refresh Stats
          </button>
          
          <button @click="clearCache" class="btn btn-error btn-outline btn-sm">
            <Icon name="trash" size="sm" class="mr-2" />
            Clear Cache
          </button>
        </div>
      </div>
    </div>

    <!-- Update Section -->
    <div v-if="showUpdatePrompt && needRefresh" class="card bg-base-100 shadow-xl border border-warning/50 mb-6">
      <div class="card-body relative">
        <!-- Close Button -->
        <button
          @click="dismissUpdatePrompt"
          class="btn btn-sm btn-circle btn-ghost absolute top-4 right-4 z-10"
          title="Dismiss update prompt"
        >
          <Icon name="x" size="sm" />
        </button>

        <div class="pr-12"> <!-- Add padding to avoid overlap with close button -->
          <h3 class="text-xl font-bold text-warning mb-4">
            <Icon name="refresh" size="md" class="mr-2" />
            Update Available
          </h3>
          <p class="text-base-content/70 mb-6">
            A new version of HLenergy is available. Update now to get the latest features and improvements.
          </p>
          <div class="card-actions justify-start">
            <button @click="handleUpdateNow" class="btn btn-warning" :disabled="isUpdating">
              <Icon v-if="!isUpdating" name="download" size="sm" class="mr-2" />
              <span v-if="!isUpdating">Update Now</span>
              <span v-else class="loading loading-spinner loading-sm mr-2"></span>
              <span v-if="isUpdating">Updating...</span>
            </button>
            <button @click="dismissUpdatePrompt" class="btn btn-outline btn-warning">
              Later
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePWA } from '@/composables/usePWA'
import { usePushNotifications } from '@/composables/usePushNotifications'
import { useOfflineCache } from '@/composables/useOfflineCache'
import Icon from '@/components/common/Icon.vue'

// Local state for update prompt
const showUpdatePrompt = ref(true)
const isUpdating = ref(false)

// Composables
const {
  showInstallPrompt,
  isInstalled,
  isStandalone,
  handleInstallPrompt,
  forceShowInstallPrompt,
  needRefresh,
  updateServiceWorker,
  isOnline,
  networkStatus
} = usePWA()

const {
  permission: notificationPermission,
  isSubscribed: isNotificationsSubscribed,
  isSubscribing: isNotificationsSubscribing,
  subscribe: subscribeToNotifications,
  unsubscribe: unsubscribeFromNotifications,
  showNotification
} = usePushNotifications()

const {
  cacheStats,
  isLoading,
  formattedCacheSize,
  cacheEfficiency,
  lastUpdatedFormatted,
  refreshCacheStats,
  clearCache,
  preloadEssentialPages,
  preloadUserPages
} = useOfflineCache()

// Computed
const pwaStatusBadgeClass = computed(() => {
  if (isInstalled.value && isOnline.value) return 'badge-success'
  if (isInstalled.value) return 'badge-warning'
  return 'badge-info'
})

const pwaStatusText = computed(() => {
  if (isInstalled.value && isOnline.value) return 'Fully Functional'
  if (isInstalled.value) return 'Offline Mode'
  if (isOnline.value) return 'Browser Mode'
  return 'Limited'
})

// Methods
const toggleNotifications = async () => {
  try {
    if (isNotificationsSubscribed.value) {
      await unsubscribeFromNotifications()
    } else {
      await subscribeToNotifications()
    }
  } catch (error) {
    console.error('Failed to toggle notifications:', error)
  }
}

const testNotification = async () => {
  await showNotification('Test Notification', {
    body: 'This is a test notification from HLenergy!',
    icon: '/icon-192.svg',
    tag: 'test-notification'
  })
}

// Update prompt methods
const dismissUpdatePrompt = () => {
  showUpdatePrompt.value = false

  // Show reminder after 1 hour
  setTimeout(() => {
    if (needRefresh.value) {
      showUpdatePrompt.value = true
    }
  }, 60 * 60 * 1000) // 1 hour
}

const handleUpdateNow = async () => {
  try {
    isUpdating.value = true
    await updateServiceWorker()

    // Show success message
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  } catch (error) {
    console.error('Update failed:', error)
    alert('Update failed. Please refresh the page manually.')
  } finally {
    isUpdating.value = false
  }
}
</script>

<style scoped>
.pwa-settings {
  max-width: 4xl;
  margin: 0 auto;
}

.stat {
  background-color: transparent;
}

.toggle:checked {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
}
</style>
