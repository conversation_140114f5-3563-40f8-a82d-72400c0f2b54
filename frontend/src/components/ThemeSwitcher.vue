<template>
  <div class="theme-switcher">
    <!-- Simple Theme Toggle Button -->
    <button
      @click="toggleTheme"
      class="btn btn-ghost btn-circle"
      :title="themeTooltip"
    >
      <Icon
        :name="currentThemeIcon"
        size="lg"
        class="transition-all duration-300"
        :class="{ 'rotate-180': isTransitioning }"
      />
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Icon from '@/components/common/Icon.vue'

// Theme state - only light and dark modes for user selection
const currentTheme = ref<'light' | 'dark'>('light')
const systemPrefersDark = ref(false)
const isTransitioning = ref(false)

// Computed
const currentThemeIcon = computed(() => {
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

const themeTooltip = computed(() => {
  return currentTheme.value === 'dark' ? 'Switch to Light Theme' : 'Switch to Dark Theme'
})

const effectiveTheme = computed(() => {
  return currentTheme.value === 'dark' ? 'hlenergy-dark' : 'hlenergy-light'
})

// Methods
const toggleTheme = () => {
  isTransitioning.value = true

  // Toggle between light and dark
  currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'

  applyTheme()
  saveThemePreference()

  // Reset transition state
  setTimeout(() => {
    isTransitioning.value = false
  }, 300)
}

const applyTheme = () => {
  const theme = effectiveTheme.value
  document.documentElement.setAttribute('data-theme', theme)

  // Add smooth transition class
  document.documentElement.classList.add('theme-transition')

  // Remove transition class after animation
  setTimeout(() => {
    document.documentElement.classList.remove('theme-transition')
  }, 300)

  console.log(`Applied theme: ${theme} (mode: ${currentTheme.value})`)
}

const saveThemePreference = () => {
  localStorage.setItem('hlenergy-theme', currentTheme.value)
}

const loadThemePreference = () => {
  const saved = localStorage.getItem('hlenergy-theme') as 'light' | 'dark'
  if (saved && ['light', 'dark'].includes(saved)) {
    currentTheme.value = saved
  } else {
    // Use system preference to determine initial theme
    currentTheme.value = systemPrefersDark.value ? 'dark' : 'light'
  }
}

const checkSystemTheme = () => {
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemPrefersDark.value = mediaQuery.matches

    // Listen for changes (for future use, but don't auto-switch themes)
    mediaQuery.addEventListener('change', (e) => {
      systemPrefersDark.value = e.matches
    })
  }
}

const initializeTheme = () => {
  checkSystemTheme()
  loadThemePreference()
  applyTheme()
}

// Lifecycle
onMounted(() => {
  initializeTheme()
})

// Expose theme functions for external use
defineExpose({
  toggleTheme,
  currentTheme,
  effectiveTheme
})
</script>

<style scoped>
.theme-switcher {
  position: relative;
}

/* Theme transition animation */
:global(html.theme-transition) {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

:global(html.theme-transition *) {
  transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}

/* Button hover effect */
.btn:hover {
  transform: scale(1.05);
}

/* Icon rotation animation */
.rotate-180 {
  transform: rotate(180deg);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease-in-out;
}
</style>
