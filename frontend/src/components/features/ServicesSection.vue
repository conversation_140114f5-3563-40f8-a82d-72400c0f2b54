<template>
  <section class="py-24 bg-gradient-to-br from-base-100 to-base-200/50">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-20">
        <div class="flex justify-center mb-6">
          <div class="p-4 bg-primary/20 rounded-full">
            <Icon name="lightbulb" size="2xl" class="text-primary" />
          </div>
        </div>
        <h2 class="text-4xl md:text-6xl font-bold mb-6 text-base-content">{{ t('services.title') }}</h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto mb-8 leading-relaxed">{{ t('services.subtitle') }}</p>
        <div class="w-32 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"></div>
      </div>

      <!-- Services Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 lg:gap-12 mb-20">
        <!-- Energy Audit -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center shadow-2xl border border-blue-500/30 group-hover:scale-110 group-hover:shadow-3xl transition-all duration-300">
                <Icon name="clipboard" size="xl" class="text-white drop-shadow-lg" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-blue-600 transition-colors duration-300">{{ t('services.energy_audit') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.energy_audit_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-blue-600 to-blue-700 mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Strategic Consultation -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-2xl flex items-center justify-center shadow-2xl border border-emerald-500/30 group-hover:scale-110 group-hover:shadow-3xl transition-all duration-300">
                <Icon name="lightbulb" size="xl" class="text-white drop-shadow-lg" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-emerald-600 transition-colors duration-300">{{ t('services.consultation') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.consultation_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-emerald-600 to-emerald-700 mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Implementation Support -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-accent/90 to-accent rounded-2xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                <Icon name="cog" size="xl" class="text-white" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-accent transition-colors duration-300">{{ t('services.implementation') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.implementation_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-accent to-accent-focus mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Monitoring -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-info/90 to-info rounded-2xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                <Icon name="chart-bar" size="xl" class="text-white" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-info transition-colors duration-300">{{ t('services.monitoring') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.monitoring_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-info to-info-focus mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Regulatory Compliance -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-success/90 to-success rounded-2xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                <Icon name="shield" size="xl" class="text-white" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-success transition-colors duration-300">{{ t('services.compliance') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.compliance_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-success to-success-focus mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Energy Financing -->
        <div class="group">
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-300/50 hover:shadow-3xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body text-center p-10">
              <div class="mx-auto mb-8 w-24 h-24 bg-gradient-to-br from-warning/90 to-warning rounded-2xl flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300">
                <Icon name="currency" size="xl" class="text-white" />
              </div>
              <h3 class="text-2xl font-bold mb-6 text-base-content group-hover:text-warning transition-colors duration-300">{{ t('services.financing') }}</h3>
              <p class="text-base-content/70 leading-relaxed text-lg mb-6">{{ t('services.financing_desc') }}</p>
              <div class="mt-auto">
                <div class="w-16 h-1 bg-gradient-to-r from-warning to-warning-focus mx-auto rounded-full group-hover:w-24 transition-all duration-300"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="text-center">
        <div class="inline-block p-1 bg-gradient-to-r from-primary to-secondary rounded-2xl shadow-2xl">
          <RouterLink
            to="/services"
            class="btn btn-lg bg-base-100 text-primary hover:bg-primary hover:text-primary-content px-12 py-6 text-xl font-bold shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 border-0 rounded-xl"
          >
            Explore All Services
            <Icon name="arrow-right" size="lg" class="ml-3 group-hover:translate-x-1 transition-transform duration-300" />
          </RouterLink>
        </div>
        <p class="text-base-content/60 mt-6 text-lg">Discover how we can transform your energy efficiency</p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()
</script>

<style scoped>
/* Enhanced shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-8px) scale(1.02);
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity, color;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced card hover effects */
.card {
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.group:hover .card::before {
  left: 100%;
}

/* Icon container animations */
.group:hover .w-24 {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Gradient line animations */
.group:hover .w-16 {
  animation: expand 0.3s ease-out forwards;
}

@keyframes expand {
  from {
    width: 4rem;
  }
  to {
    width: 6rem;
  }
}
</style>
