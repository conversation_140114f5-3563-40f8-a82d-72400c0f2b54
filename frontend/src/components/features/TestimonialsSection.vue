<template>
  <section class="py-20 bg-gradient-to-br from-primary/5 to-secondary/5">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="flex justify-center mb-6">
          <div class="p-4 bg-accent/20 rounded-full">
            <Icon name="star" size="2xl" class="text-accent" />
          </div>
        </div>
        <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ t('testimonials.title') }}</h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto mb-8 leading-relaxed">{{ t('testimonials.subtitle') }}</p>
        <div class="w-32 h-1 bg-gradient-to-r from-accent to-warning mx-auto rounded-full"></div>
      </div>

      <!-- Testimonials Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
        <!-- Testimonial 1 - B2B -->
        <div class="group">
          <div class="card bg-base-100/90 backdrop-blur-sm shadow-xl border border-base-300/50 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body p-8">
              <!-- Quote Icon -->
              <div class="flex justify-center mb-6">
                <div class="p-3 bg-primary/20 rounded-full">
                  <Icon name="chat" size="lg" class="text-primary" />
                </div>
              </div>
              
              <!-- Stars -->
              <div class="flex justify-center mb-4">
                <div class="flex space-x-1">
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                </div>
              </div>

              <!-- Testimonial Text -->
              <blockquote class="text-base-content/80 text-center mb-6 leading-relaxed italic">
                "{{ t('testimonials.client1_text') }}"
              </blockquote>

              <!-- Client Info -->
              <div class="text-center">
                <div class="font-semibold text-base-content">{{ t('testimonials.client1_name') }}</div>
                <div class="text-sm text-primary font-medium">{{ t('testimonials.client1_company') }}</div>
                <div class="badge badge-primary badge-sm mt-2">B2B Client</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Testimonial 2 - B2C -->
        <div class="group">
          <div class="card bg-base-100/90 backdrop-blur-sm shadow-xl border border-base-300/50 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body p-8">
              <!-- Quote Icon -->
              <div class="flex justify-center mb-6">
                <div class="p-3 bg-secondary/20 rounded-full">
                  <Icon name="chat" size="lg" class="text-secondary" />
                </div>
              </div>
              
              <!-- Stars -->
              <div class="flex justify-center mb-4">
                <div class="flex space-x-1">
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                </div>
              </div>

              <!-- Testimonial Text -->
              <blockquote class="text-base-content/80 text-center mb-6 leading-relaxed italic">
                "{{ t('testimonials.client2_text') }}"
              </blockquote>

              <!-- Client Info -->
              <div class="text-center">
                <div class="font-semibold text-base-content">{{ t('testimonials.client2_name') }}</div>
                <div class="text-sm text-secondary font-medium">{{ t('testimonials.client2_company') }}</div>
                <div class="badge badge-secondary badge-sm mt-2">B2C Client</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Testimonial 3 - B2B -->
        <div class="group md:col-span-2 lg:col-span-1">
          <div class="card bg-base-100/90 backdrop-blur-sm shadow-xl border border-base-300/50 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
            <div class="card-body p-8">
              <!-- Quote Icon -->
              <div class="flex justify-center mb-6">
                <div class="p-3 bg-accent/20 rounded-full">
                  <Icon name="chat" size="lg" class="text-accent" />
                </div>
              </div>
              
              <!-- Stars -->
              <div class="flex justify-center mb-4">
                <div class="flex space-x-1">
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                  <Icon name="star" size="sm" class="text-warning" variant="solid" />
                </div>
              </div>

              <!-- Testimonial Text -->
              <blockquote class="text-base-content/80 text-center mb-6 leading-relaxed italic">
                "{{ t('testimonials.client3_text') }}"
              </blockquote>

              <!-- Client Info -->
              <div class="text-center">
                <div class="font-semibold text-base-content">{{ t('testimonials.client3_name') }}</div>
                <div class="text-sm text-accent font-medium">{{ t('testimonials.client3_company') }}</div>
                <div class="badge badge-accent badge-sm mt-2">B2B Client</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Trust Indicators -->
      <div class="mt-16 text-center">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div class="text-center">
            <div class="text-3xl font-bold text-primary mb-2">500+</div>
            <div class="text-sm text-base-content/70">Happy Clients</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-secondary mb-2">98%</div>
            <div class="text-sm text-base-content/70">Satisfaction Rate</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-accent mb-2">15+</div>
            <div class="text-sm text-base-content/70">Years Experience</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-warning mb-2">30%</div>
            <div class="text-sm text-base-content/70">Average Savings</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()
</script>

<style scoped>
/* Enhanced shadow effects */
.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Enhanced hover animations */
.group:hover .card {
  transform: translateY(-8px) scale(1.02);
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: transform, box-shadow, background-color, border-color, opacity, color;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Quote styling */
blockquote {
  position: relative;
}

blockquote::before {
  content: '"';
  font-size: 4rem;
  position: absolute;
  top: -1rem;
  left: -1rem;
  color: hsl(var(--p) / 0.1);
  font-family: serif;
}
</style>
