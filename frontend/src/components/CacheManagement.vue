<template>
  <div class="cache-management px-6 py-6">
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h2 class="card-title">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
          </svg>
          Offline Cache Management
        </h2>
        
        <!-- Cache Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Cached Pages</div>
            <div class="stat-value text-primary">{{ cacheStats.totalPages }}</div>
            <div class="stat-desc">Available offline</div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Cache Size</div>
            <div class="stat-value text-secondary">{{ formattedCacheSize }}</div>
            <div class="stat-desc">Storage used</div>
          </div>
          
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Efficiency</div>
            <div class="stat-value text-accent">{{ cacheEfficiency.toFixed(0) }}%</div>
            <div class="stat-desc">Coverage score</div>
          </div>
        </div>

        <!-- Cache Status -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-semibold">Cache Status</h3>
            <div class="badge" :class="isInitialized ? 'badge-success' : 'badge-warning'">
              {{ isInitialized ? 'Active' : 'Initializing' }}
            </div>
          </div>
          
          <div class="bg-base-200 px-4 py-4 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm">Last Updated:</span>
              <span class="text-sm font-medium">{{ lastUpdatedFormatted }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm">Cache Version:</span>
              <span class="text-sm font-medium">{{ cacheStats.cacheVersion }}</span>
            </div>
          </div>
        </div>

        <!-- Cache Actions -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">Cache Actions</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <button 
              @click="preloadEssentialPages" 
              :disabled="isLoading"
              class="btn btn-primary btn-sm"
            >
              <span v-if="!isLoading">📄 Preload Essential</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
            
            <button 
              @click="preloadUserPages" 
              :disabled="isLoading"
              class="btn btn-secondary btn-sm"
            >
              <span v-if="!isLoading">👤 Preload User Pages</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
            
            <button 
              @click="refreshCacheStats" 
              :disabled="isLoading"
              class="btn btn-accent btn-sm"
            >
              <span v-if="!isLoading">🔄 Refresh Stats</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
            
            <button 
              @click="clearCache" 
              :disabled="isLoading"
              class="btn btn-error btn-sm"
            >
              <span v-if="!isLoading">🗑️ Clear Cache</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
          </div>
        </div>

        <!-- Cached Pages List -->
        <div class="mb-6">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-semibold">Cached Pages</h3>
            <button @click="loadCachedPages" class="btn btn-ghost btn-sm">
              {{ showCachedPages ? 'Hide' : 'Show' }} List
            </button>
          </div>
          
          <div v-if="showCachedPages" class="bg-base-200 px-4 py-4 rounded-lg max-h-60 overflow-y-auto">
            <div v-if="cachedPages.length === 0" class="text-center text-base-content/70 py-4">
              No cached pages found
            </div>
            <div v-else class="space-y-2">
              <div 
                v-for="page in cachedPages" 
                :key="page"
                class="flex items-center justify-between p-2 bg-base-100 rounded"
              >
                <span class="text-sm font-mono">{{ page }}</span>
                <div class="flex items-center space-x-2">
                  <div class="badge badge-success badge-xs">Cached</div>
                  <button @click="checkPageStatus(page)" class="btn btn-ghost btn-xs">
                    Check
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Manual Page Caching -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">Manual Page Caching</h3>
          <div class="flex space-x-2">
            <input 
              v-model="manualPageUrl"
              type="text" 
              placeholder="Enter page URL (e.g., /about)"
              class="input input-bordered flex-1"
            />
            <button 
              @click="cacheManualPage" 
              :disabled="!manualPageUrl || isLoading"
              class="btn btn-primary"
            >
              Cache Page
            </button>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="alert alert-error mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
          <button @click="error = null" class="btn btn-sm btn-ghost">
            ✕
          </button>
        </div>

        <!-- Success Messages -->
        <div v-if="successMessage" class="alert alert-success mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ successMessage }}</span>
          <button @click="successMessage = null" class="btn btn-sm btn-ghost">
            ✕
          </button>
        </div>

        <!-- Offline Navigation Test -->
        <div v-if="!isOnline" class="bg-warning/10 border border-warning/20 rounded-lg px-4 py-4 mb-6">
          <h4 class="font-semibold text-warning mb-2">🔄 Offline Mode Active</h4>
          <p class="text-sm mb-3">You're currently offline. Test cached page navigation:</p>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <RouterLink to="/" class="btn btn-sm btn-outline">Home</RouterLink>
            <RouterLink to="/about" class="btn btn-sm btn-outline">About</RouterLink>
            <RouterLink to="/services" class="btn btn-sm btn-outline">Services</RouterLink>
            <RouterLink to="/contact" class="btn btn-sm btn-outline">Contact</RouterLink>
          </div>
        </div>

        <!-- Cache Tips -->
        <div class="bg-info/10 border border-info/20 rounded-lg px-4 py-4">
          <h4 class="font-semibold text-info mb-2">💡 Cache Tips</h4>
          <ul class="text-sm space-y-1 text-base-content/80">
            <li>• Essential pages are automatically cached for offline access</li>
            <li>• Pages you visit are cached automatically</li>
            <li>• Preload user pages to cache your frequently visited pages</li>
            <li>• Clear cache if you experience issues or want to free up space</li>
            <li>• Cache efficiency shows how well your offline experience is covered</li>
            <li>• <strong>Offline navigation:</strong> Only cached pages work when offline</li>
            <li>• <strong>Smart caching:</strong> Already cached pages are skipped automatically</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useOfflineCache } from '@/composables/useOfflineCache'
import { usePWA } from '@/composables/usePWA'

const {
  cacheStats,
  isInitialized,
  isLoading,
  error,
  formattedCacheSize,
  cacheEfficiency,
  lastUpdatedFormatted,
  refreshCacheStats,
  clearCache,
  preloadEssentialPages,
  preloadUserPages,
  preloadPages,
  getCachedPages,
  checkPageCached
} = useOfflineCache()

const { isOnline } = usePWA()

// Local state
const showCachedPages = ref(false)
const cachedPages = ref<string[]>([])
const manualPageUrl = ref('')
const successMessage = ref<string | null>(null)

// Methods
const loadCachedPages = async () => {
  if (!showCachedPages.value) {
    try {
      cachedPages.value = await getCachedPages()
      showCachedPages.value = true
    } catch (err) {
      console.error('Failed to load cached pages:', err)
    }
  } else {
    showCachedPages.value = false
  }
}

const checkPageStatus = async (url: string) => {
  try {
    const isCached = await checkPageCached(url)
    if (isCached) {
      successMessage.value = `✅ ${url} is cached and available offline`
    } else {
      successMessage.value = `❌ ${url} is not cached`
    }
    
    // Clear message after 3 seconds
    setTimeout(() => {
      successMessage.value = null
    }, 3000)
  } catch (err) {
    console.error('Failed to check page status:', err)
  }
}

const cacheManualPage = async () => {
  if (!manualPageUrl.value) return
  
  try {
    await preloadPages([manualPageUrl.value])
    successMessage.value = `✅ Page ${manualPageUrl.value} cached successfully`
    manualPageUrl.value = ''
    
    // Refresh cached pages list if it's shown
    if (showCachedPages.value) {
      cachedPages.value = await getCachedPages()
    }
    
    // Clear message after 3 seconds
    setTimeout(() => {
      successMessage.value = null
    }, 3000)
  } catch (err) {
    console.error('Failed to cache manual page:', err)
  }
}

onMounted(() => {
  // Auto-refresh stats every 30 seconds
  const interval = setInterval(refreshCacheStats, 30000)
  
  // Cleanup on unmount
  return () => {
    clearInterval(interval)
  }
})
</script>

<style scoped>
.cache-management {
  max-width: 1200px;
  margin: 0 auto;
}

.stat {
  @apply px-4 py-4;
}

.alert {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
