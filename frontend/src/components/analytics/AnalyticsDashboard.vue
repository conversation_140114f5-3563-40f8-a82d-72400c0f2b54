<template>
  <div class="analytics-dashboard p-6 space-y-6">
    <!-- Dashboard Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Analytics Dashboard</h1>
        <p class="text-base-content/70 mt-1">
          Real-time insights and business intelligence for HLenergy
        </p>
      </div>
      
      <!-- Time Range Selector -->
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <div class="form-control">
          <select 
            v-model="selectedTimeRange" 
            @change="handleTimeRangeChange"
            class="select select-bordered"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
        </div>
        
        <button 
          @click="refreshData" 
          :disabled="isLoading"
          class="btn btn-primary"
          :class="{ 'loading': isLoading }"
        >
          <span v-if="!isLoading">Refresh</span>
        </button>
      </div>
    </div>

    <!-- Real-time Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-figure text-primary">
          <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
        </div>
        <div class="stat-title">Live Users</div>
        <div class="stat-value text-primary">{{ realTimeUsers }}</div>
        <div class="stat-desc">Currently browsing</div>
      </div>
      
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-title">Page Views Today</div>
        <div class="stat-value">{{ realTimePageViews }}</div>
        <div class="stat-desc">↗︎ Real-time updates</div>
      </div>
      
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-title">Conversions Today</div>
        <div class="stat-value text-success">{{ realTimeConversions }}</div>
        <div class="stat-desc">Contact forms submitted</div>
      </div>
    </div>

    <!-- Key Performance Indicators -->
    <div v-if="dashboardSummary" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
      <!-- Total Users -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-lg">Total Users</h3>
          <div class="flex items-end space-x-2">
            <span class="text-3xl font-bold">{{ dashboardSummary.users.total.toLocaleString() }}</span>
            <span 
              class="text-sm font-medium"
              :class="dashboardSummary.users.growth >= 0 ? 'text-success' : 'text-error'"
            >
              {{ dashboardSummary.users.growth >= 0 ? '+' : '' }}{{ dashboardSummary.users.growth }}%
            </span>
          </div>
          <div class="text-sm text-base-content/70">
            {{ dashboardSummary.users.active }} active users
          </div>
        </div>
      </div>

      <!-- Total Leads -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-lg">Total Leads</h3>
          <div class="flex items-end space-x-2">
            <span class="text-3xl font-bold text-primary">{{ dashboardSummary.leads.total }}</span>
            <span 
              class="text-sm font-medium"
              :class="dashboardSummary.leads.growth >= 0 ? 'text-success' : 'text-error'"
            >
              {{ dashboardSummary.leads.growth >= 0 ? '+' : '' }}{{ dashboardSummary.leads.growth }}%
            </span>
          </div>
          <div class="text-sm text-base-content/70">
            {{ dashboardSummary.leads.qualified }} qualified leads
          </div>
        </div>
      </div>

      <!-- Conversion Rate -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-lg">Conversion Rate</h3>
          <div class="flex items-end space-x-2">
            <span class="text-3xl font-bold text-success">{{ dashboardSummary.leads.conversionRate.toFixed(1) }}%</span>
          </div>
          <div class="text-sm text-base-content/70">
            Visitor to lead conversion
          </div>
        </div>
      </div>

      <!-- Bounce Rate -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title text-lg">Bounce Rate</h3>
          <div class="flex items-end space-x-2">
            <span class="text-3xl font-bold">{{ dashboardSummary.engagement.bounceRate.toFixed(1) }}%</span>
          </div>
          <div class="text-sm text-base-content/70">
            Avg session: {{ Math.round(dashboardSummary.engagement.avgSessionDuration / 60) }}min
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Lead Sources Chart -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title">Lead Sources</h3>
          <div class="space-y-3">
            <div 
              v-for="source in leadSourcesData.slice(0, 5)" 
              :key="source.source"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div class="w-4 h-4 bg-primary rounded"></div>
                <span class="font-medium">{{ source.source }}</span>
              </div>
              <div class="text-right">
                <div class="font-bold">{{ source.count }}</div>
                <div class="text-sm text-success">{{ source.conversionRate.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Service Interest -->
      <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <h3 class="card-title">Service Interest</h3>
          <div class="space-y-3">
            <div 
              v-for="service in serviceInterestData.slice(0, 5)" 
              :key="service.service"
              class="flex items-center justify-between"
            >
              <span class="font-medium">{{ service.service }}</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-base-200 rounded-full h-2">
                  <div 
                    class="bg-secondary h-2 rounded-full"
                    :style="{ width: `${(service.inquiries / Math.max(...serviceInterestData.map(s => s.inquiries))) * 100}%` }"
                  ></div>
                </div>
                <span class="font-bold">{{ service.inquiries }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conversion Funnel -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h3 class="card-title">Conversion Funnel</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
          <div
            v-for="(step, index) in conversionFunnel"
            :key="step.step"
            class="text-center relative"
          >
            <div
              class="w-full h-20 bg-primary rounded-lg flex items-center justify-center text-white font-bold text-lg"
              :style="{ opacity: 1 - (index * 0.2) }"
            >
              {{ step.users }}
            </div>
            <div class="mt-2 font-medium">{{ step.step }}</div>
            <div class="text-sm text-base-content/70">
              {{ step.conversionRate.toFixed(1) }}% conversion
            </div>
            <div v-if="index < conversionFunnel.length - 1" class="hidden md:flex absolute top-10 -right-4 items-center justify-center w-8 h-8 text-primary text-xl">
              →
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Pages -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h3 class="card-title">Top Performing Pages</h3>
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>Page</th>
                <th>Views</th>
                <th>Performance</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="page in topPerformingPages" :key="page.page">
                <td class="font-medium">{{ page.page }}</td>
                <td>{{ page.views.toLocaleString() }}</td>
                <td>
                  <div class="w-full bg-base-200 rounded-full h-2">
                    <div 
                      class="bg-primary h-2 rounded-full"
                      :style="{ width: `${(page.views / Math.max(...topPerformingPages.map(p => p.views))) * 100}%` }"
                    ></div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Last Updated -->
    <div v-if="lastUpdated" class="text-center text-sm text-base-content/70">
      Last updated: {{ lastUpdated.toLocaleString() }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAnalyticsStore } from '@/stores/analytics'

const analyticsStore = useAnalyticsStore()

const {
  selectedTimeRange,
  isLoading,
  lastUpdated,
  realTimeUsers,
  realTimePageViews,
  realTimeConversions,
  dashboardSummary,
  leadSourcesData,
  serviceInterestData,
  conversionFunnel,
  topPerformingPages,
  setTimeRange,
  refreshAllData,
  startRealTimeUpdates,
  stopRealTimeUpdates
} = analyticsStore

let realTimeInterval: number

const handleTimeRangeChange = () => {
  setTimeRange(selectedTimeRange.value)
}

const refreshData = () => {
  refreshAllData()
}

onMounted(async () => {
  await refreshAllData()
  realTimeInterval = startRealTimeUpdates()
})

onUnmounted(() => {
  if (realTimeInterval) {
    stopRealTimeUpdates(realTimeInterval)
  }
})
</script>

<style scoped>
.analytics-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat {
  @apply p-6;
}

.card {
  @apply border border-base-300;
}
</style>
