<template>
  <div class="project-list p-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Projects</h1>
        <p class="text-base-content/70 mt-1">Manage energy consultation projects</p>
      </div>
      
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <RouterLink to="/crm/projects/new" class="btn btn-primary">
          + New Project
        </RouterLink>
        <button @click="exportProjects" class="btn btn-outline">
          Export
        </button>
      </div>
    </div>

    <!-- Project Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="card bg-gradient-to-r from-primary to-primary-focus text-primary-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Total Projects</h3>
          <div class="text-3xl font-bold">{{ totalProjects }}</div>
          <div class="text-sm opacity-80">All time</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-secondary to-secondary-focus text-secondary-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Active Projects</h3>
          <div class="text-3xl font-bold">{{ activeProjects.length }}</div>
          <div class="text-sm opacity-80">In progress</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-warning to-warning-focus text-warning-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Overdue</h3>
          <div class="text-3xl font-bold">{{ overdueProjects.length }}</div>
          <div class="text-sm opacity-80">Need attention</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-success to-success-focus text-success-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Completed</h3>
          <div class="text-3xl font-bold">{{ completedProjects.length }}</div>
          <div class="text-sm opacity-80">This month</div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <!-- Search -->
          <div class="form-control">
            <input 
              v-model="searchQuery"
              @input="handleSearch"
              type="text" 
              placeholder="Search projects..." 
              class="input input-bordered"
            />
          </div>
          
          <!-- Status Filter -->
          <div class="form-control">
            <select v-model="filters.status" @change="applyFilters" class="select select-bordered">
              <option value="">All Statuses</option>
              <option value="planning">Planning</option>
              <option value="in_progress">In Progress</option>
              <option value="on_hold">On Hold</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <!-- Type Filter -->
          <div class="form-control">
            <select v-model="filters.type" @change="applyFilters" class="select select-bordered">
              <option value="">All Types</option>
              <option value="energy_audit">Energy Audit</option>
              <option value="consultation">Consultation</option>
              <option value="implementation">Implementation</option>
              <option value="monitoring">Monitoring</option>
              <option value="compliance">Compliance</option>
            </select>
          </div>
          
          <!-- Priority Filter -->
          <div class="form-control">
            <select v-model="filters.priority" @change="applyFilters" class="select select-bordered">
              <option value="">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>
          
          <!-- Assigned To Filter -->
          <div class="form-control">
            <select v-model="filters.assignedTo" @change="applyFilters" class="select select-bordered">
              <option value="">All Assignees</option>
              <option value="1">John Doe</option>
              <option value="2">Jane Smith</option>
              <!-- Add more users dynamically -->
            </select>
          </div>
          
          <!-- Clear Filters -->
          <div class="form-control">
            <button @click="clearFilters" class="btn btn-ghost">
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div v-if="isLoading" v-for="i in 6" :key="i" class="card bg-base-100 shadow-lg">
        <div class="card-body">
          <div class="animate-pulse">
            <div class="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-base-300 rounded w-1/2 mb-4"></div>
            <div class="h-2 bg-base-300 rounded w-full mb-2"></div>
            <div class="h-2 bg-base-300 rounded w-2/3"></div>
          </div>
        </div>
      </div>

      <div v-else-if="projects.length === 0" class="col-span-full">
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body text-center py-12">
            <h3 class="text-xl font-semibold text-base-content/70 mb-2">No projects found</h3>
            <p class="text-base-content/50 mb-4">Create your first project to get started</p>
            <RouterLink to="/crm/projects/new" class="btn btn-primary">
              + Create Project
            </RouterLink>
          </div>
        </div>
      </div>

      <div v-else v-for="project in projects" :key="project.id" class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
        <div class="card-body">
          <!-- Project Header -->
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <h3 class="card-title text-lg">{{ project.title }}</h3>
              <p class="text-sm text-base-content/70">{{ project.projectNumber }}</p>
            </div>
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                ⋮
              </div>
              <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow">
                <li><RouterLink :to="`/crm/projects/${project.id}`">View Details</RouterLink></li>
                <li><RouterLink :to="`/crm/projects/${project.id}/edit`">Edit Project</RouterLink></li>
                <li><a @click="logCommunication(project)">Log Communication</a></li>
                <li><a @click="uploadDocument(project)">Upload Document</a></li>
                <li class="divider"></li>
                <li><a @click="deleteProject(project)" class="text-error">Delete Project</a></li>
              </ul>
            </div>
          </div>

          <!-- Project Info -->
          <div class="space-y-3">
            <!-- Customer -->
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium">Customer:</span>
              <RouterLink 
                v-if="project.customer" 
                :to="`/crm/customers/${project.customer.id}`"
                class="text-sm text-primary hover:underline"
              >
                {{ project.customer.firstName }} {{ project.customer.lastName }}
              </RouterLink>
              <span v-else class="text-sm text-base-content/70">Not assigned</span>
            </div>

            <!-- Status & Priority -->
            <div class="flex items-center space-x-2">
              <div class="badge" :class="getStatusBadgeClass(project.status)">
                {{ project.status }}
              </div>
              <div class="badge" :class="getPriorityBadgeClass(project.priority)">
                {{ project.priority }}
              </div>
              <div class="badge badge-outline">
                {{ project.type }}
              </div>
            </div>

            <!-- Progress -->
            <div>
              <div class="flex items-center justify-between text-sm mb-1">
                <span>Progress</span>
                <span>{{ project.completionPercentage }}%</span>
              </div>
              <div class="w-full bg-base-200 rounded-full h-2">
                <div 
                  class="bg-primary h-2 rounded-full transition-all"
                  :style="{ width: `${project.completionPercentage}%` }"
                ></div>
              </div>
            </div>

            <!-- Timeline -->
            <div class="text-sm text-base-content/70">
              <div v-if="project.startDate && project.endDate">
                {{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}
              </div>
              <div v-else-if="project.startDate">
                Started: {{ formatDate(project.startDate) }}
              </div>
              <div v-else>
                Created: {{ formatDate(project.createdAt) }}
              </div>
            </div>

            <!-- Budget -->
            <div v-if="project.budget" class="text-sm">
              <span class="font-medium">Budget:</span>
              <span class="text-success ml-1">${{ project.budget.toLocaleString() }}</span>
            </div>
          </div>

          <!-- Project Actions -->
          <div class="card-actions justify-end mt-4">
            <RouterLink :to="`/crm/projects/${project.id}`" class="btn btn-sm btn-primary">
              View Details
            </RouterLink>
          </div>

          <!-- Overdue Warning -->
          <div v-if="isOverdue(project)" class="alert alert-warning mt-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-sm">Project is overdue!</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="projects.length > 0" class="flex items-center justify-between mt-8">
      <div class="text-sm text-base-content/70">
        Showing {{ ((pagination.page - 1) * pagination.limit) + 1 }} to 
        {{ Math.min(pagination.page * pagination.limit, pagination.total) }} of 
        {{ pagination.total }} projects
      </div>
      
      <div class="join">
        <button 
          class="join-item btn btn-sm"
          :disabled="pagination.page <= 1"
          @click="changePage(pagination.page - 1)"
        >
          Previous
        </button>
        
        <button 
          v-for="page in visiblePages" 
          :key="page"
          class="join-item btn btn-sm"
          :class="{ 'btn-active': page === pagination.page }"
          @click="changePage(page)"
        >
          {{ page }}
        </button>
        
        <button 
          class="join-item btn btn-sm"
          :disabled="pagination.page >= pagination.pages"
          @click="changePage(pagination.page + 1)"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCRMStore } from '@/stores/crm'
import type { Project } from '@/services/crm'

const router = useRouter()
const crmStore = useCRMStore()

const {
  projects,
  isLoading,
  projectPagination: pagination,
  projectFilters,
  totalProjects,
  activeProjects,
  overdueProjects,
  fetchProjects,
  setProjectFilters,
  clearFilters: storeClearFilters,
  updateProject,
  deleteProject: storeDeleteProject
} = crmStore

// Local state
const searchQuery = ref('')
const filters = ref({
  status: '',
  type: '',
  priority: '',
  assignedTo: null as number | null
})

// Computed
const completedProjects = computed(() => 
  projects.filter(p => p.status === 'completed')
)

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, pagination.page - 2)
  const end = Math.min(pagination.pages, pagination.page + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const handleSearch = () => {
  setProjectFilters({ search: searchQuery.value })
  fetchProjects()
}

const applyFilters = () => {
  setProjectFilters(filters.value)
  fetchProjects()
}

const clearFilters = () => {
  searchQuery.value = ''
  filters.value = {
    status: '',
    type: '',
    priority: '',
    assignedTo: null
  }
  storeClearFilters()
  fetchProjects()
}

const changePage = (page: number) => {
  pagination.page = page
  fetchProjects()
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    planning: 'badge-info',
    in_progress: 'badge-warning',
    on_hold: 'badge-ghost',
    completed: 'badge-success',
    cancelled: 'badge-error',
    archived: 'badge-neutral'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    low: 'badge-ghost',
    medium: 'badge-info',
    high: 'badge-warning',
    urgent: 'badge-error'
  }
  return classes[priority as keyof typeof classes] || 'badge-ghost'
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const isOverdue = (project: Project) => {
  if (!project.endDate || project.status === 'completed') return false
  return new Date(project.endDate) < new Date()
}

const logCommunication = (project: Project) => {
  router.push(`/crm/communications/new?projectId=${project.id}`)
}

const uploadDocument = (project: Project) => {
  router.push(`/crm/documents/upload?projectId=${project.id}`)
}

const deleteProject = async (project: Project) => {
  if (confirm(`Are you sure you want to delete project "${project.title}"?`)) {
    try {
      await storeDeleteProject(project.id)
      // Show success message
    } catch (error) {
      console.error('Failed to delete project:', error)
    }
  }
}

const exportProjects = () => {
  // Implement export functionality
  console.log('Export projects')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.project-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
</style>
