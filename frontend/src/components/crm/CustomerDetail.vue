<template>
  <div class="customer-detail p-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center min-h-screen">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Customer Detail Content -->
    <div v-else-if="currentCustomer" class="space-y-6">
      <!-- Header -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-center space-x-4">
          <RouterLink to="/crm/customers" class="btn btn-ghost btn-sm">
            ← Back to Customers
          </RouterLink>
          <div class="divider divider-horizontal"></div>
          <div class="flex items-center space-x-4">
            <div class="avatar placeholder">
              <div class="bg-primary text-primary-content rounded-full w-16">
                <span class="text-xl">{{ getInitials(currentCustomer.firstName, currentCustomer.lastName) }}</span>
              </div>
            </div>
            <div>
              <h1 class="text-3xl font-bold">{{ currentCustomer.firstName }} {{ currentCustomer.lastName }}</h1>
              <p class="text-base-content/70">{{ currentCustomer.email }}</p>
              <div class="flex items-center space-x-2 mt-1">
                <div class="badge" :class="getStatusBadgeClass(currentCustomer.status)">
                  {{ currentCustomer.status }}
                </div>
                <div class="badge" :class="getPriorityBadgeClass(currentCustomer.priority)">
                  {{ currentCustomer.priority }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-4 mt-4 lg:mt-0">
          <RouterLink :to="`/crm/customers/${currentCustomer.id}/edit`" class="btn btn-primary">
            Edit Customer
          </RouterLink>
          <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-outline">
              Actions ▼
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
              <li><a @click="createProject">Create Project</a></li>
              <li><a @click="logCommunication">Log Communication</a></li>
              <li><a @click="uploadDocument">Upload Document</a></li>
              <li v-if="currentCustomer.status === 'lead'">
                <a @click="convertToCustomer" class="text-success">Convert to Customer</a>
              </li>
              <li class="divider"></li>
              <li><a @click="deleteCustomer" class="text-error">Delete Customer</a></li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Customer Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Lead Score -->
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Lead Score</h3>
            <div class="flex items-center space-x-4">
              <div class="radial-progress text-primary" :style="`--value:${currentCustomer.leadScore}`">
                {{ currentCustomer.leadScore }}%
              </div>
              <div class="text-sm text-base-content/70">
                {{ getScoreDescription(currentCustomer.leadScore) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Projects -->
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Projects</h3>
            <div class="text-3xl font-bold text-secondary">{{ customerProjects.length }}</div>
            <div class="text-sm text-base-content/70">
              {{ activeProjects.length }} active
            </div>
          </div>
        </div>

        <!-- Communications -->
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Communications</h3>
            <div class="text-3xl font-bold text-accent">{{ customerCommunications.length }}</div>
            <div class="text-sm text-base-content/70">
              Last: {{ formatRelativeTime(currentCustomer.lastContactDate) }}
            </div>
          </div>
        </div>

        <!-- Estimated Value -->
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <h3 class="card-title text-lg">Est. Value</h3>
            <div class="text-3xl font-bold text-success">
              ${{ (currentCustomer.estimatedValue || 0).toLocaleString() }}
            </div>
            <div class="text-sm text-base-content/70">
              Potential revenue
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Customer Information -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Basic Information -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Customer Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Full Name</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.firstName }} {{ currentCustomer.lastName }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Email</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.email }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Phone</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.phone || 'N/A' }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Lead Source</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.leadSource || 'N/A' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Company Information -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Company Information</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Company Name</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.companyName || 'N/A' }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Job Title</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.jobTitle || 'N/A' }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Industry</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.industry || 'N/A' }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Company Size</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.companySize || 'N/A' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Energy Profile -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Energy Profile</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Current Provider</span>
                  </label>
                  <div class="text-base-content">{{ currentCustomer.currentEnergyProvider || 'N/A' }}</div>
                </div>
                <div>
                  <label class="label">
                    <span class="label-text font-medium">Monthly Budget</span>
                  </label>
                  <div class="text-base-content">
                    {{ currentCustomer.monthlyEnergyBudget ? `$${currentCustomer.monthlyEnergyBudget.toLocaleString()}` : 'N/A' }}
                  </div>
                </div>
              </div>
              <div v-if="currentCustomer.energyNeeds" class="mt-4">
                <label class="label">
                  <span class="label-text font-medium">Energy Needs</span>
                </label>
                <div class="bg-base-200 p-4 rounded-lg">
                  <pre class="text-sm">{{ JSON.stringify(currentCustomer.energyNeeds, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Projects -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <div class="flex items-center justify-between mb-4">
                <h3 class="card-title">Recent Projects</h3>
                <button @click="createProject" class="btn btn-sm btn-primary">
                  + New Project
                </button>
              </div>
              
              <div v-if="customerProjects.length === 0" class="text-center py-8 text-base-content/70">
                No projects yet
              </div>
              
              <div v-else class="space-y-4">
                <div v-for="project in customerProjects.slice(0, 3)" :key="project.id" class="border border-base-300 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-medium">{{ project.title }}</h4>
                      <p class="text-sm text-base-content/70">{{ project.description }}</p>
                    </div>
                    <div class="text-right">
                      <div class="badge" :class="getProjectStatusBadgeClass(project.status)">
                        {{ project.status }}
                      </div>
                      <div class="text-sm text-base-content/70 mt-1">
                        {{ project.completionPercentage }}% complete
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center justify-between mt-3">
                    <div class="text-sm text-base-content/70">
                      {{ formatDate(project.createdAt) }}
                    </div>
                    <RouterLink :to="`/crm/projects/${project.id}`" class="btn btn-xs btn-ghost">
                      View Details
                    </RouterLink>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Quick Actions -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Quick Actions</h3>
              <div class="space-y-2">
                <button @click="logCommunication" class="btn btn-block btn-sm btn-primary">
                  📞 Log Communication
                </button>
                <button @click="scheduleFollowUp" class="btn btn-block btn-sm btn-secondary">
                  📅 Schedule Follow-up
                </button>
                <button @click="createProject" class="btn btn-block btn-sm btn-accent">
                  📋 Create Project
                </button>
                <button @click="uploadDocument" class="btn btn-block btn-sm btn-info">
                  📁 Upload Document
                </button>
              </div>
            </div>
          </div>

          <!-- Customer Tags -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Tags</h3>
              <div class="flex flex-wrap gap-2">
                <div v-for="tag in currentCustomer.tags" :key="tag" class="badge badge-outline">
                  {{ tag }}
                </div>
                <button class="badge badge-ghost">+ Add Tag</button>
              </div>
            </div>
          </div>

          <!-- Assignment -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Assignment</h3>
              <div v-if="currentCustomer.assignedUser" class="flex items-center space-x-3">
                <div class="avatar placeholder">
                  <div class="bg-primary text-primary-content rounded-full w-10">
                    <span class="text-sm">{{ getInitials(currentCustomer.assignedUser.name, '') }}</span>
                  </div>
                </div>
                <div>
                  <div class="font-medium">{{ currentCustomer.assignedUser.name }}</div>
                  <div class="text-sm text-base-content/70">{{ currentCustomer.assignedUser.email }}</div>
                </div>
              </div>
              <div v-else class="text-base-content/70">
                Not assigned
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title mb-4">Notes</h3>
              <div v-if="currentCustomer.notes" class="text-sm">
                {{ currentCustomer.notes }}
              </div>
              <div v-else class="text-base-content/70 text-sm">
                No notes yet
              </div>
              <button class="btn btn-sm btn-ghost mt-2">Edit Notes</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-error mb-2">Customer Not Found</h2>
        <p class="text-base-content/70 mb-4">The customer you're looking for doesn't exist.</p>
        <RouterLink to="/crm/customers" class="btn btn-primary">
          Back to Customers
        </RouterLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useCRMStore } from '@/stores/crm'

const route = useRoute()
const router = useRouter()
const crmStore = useCRMStore()

const {
  currentCustomer,
  isLoading,
  fetchCustomer,
  convertToCustomer: storeConvertToCustomer,
  deleteCustomer: storeDeleteCustomer
} = crmStore

const customerId = computed(() => parseInt(route.params.id as string))

// Mock data for projects and communications (in production, these would come from the store)
const customerProjects = computed(() => currentCustomer.value?.projects || [])
const customerCommunications = computed(() => currentCustomer.value?.communications || [])
const activeProjects = computed(() => customerProjects.value.filter(p => p.status === 'in_progress' || p.status === 'planning'))

const getInitials = (firstName: string, lastName: string) => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    lead: 'badge-info',
    prospect: 'badge-warning',
    customer: 'badge-success',
    inactive: 'badge-ghost'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    low: 'badge-ghost',
    medium: 'badge-info',
    high: 'badge-warning',
    urgent: 'badge-error'
  }
  return classes[priority as keyof typeof classes] || 'badge-ghost'
}

const getProjectStatusBadgeClass = (status: string) => {
  const classes = {
    planning: 'badge-info',
    in_progress: 'badge-warning',
    on_hold: 'badge-ghost',
    completed: 'badge-success',
    cancelled: 'badge-error'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getScoreDescription = (score: number) => {
  if (score >= 80) return 'Hot Lead'
  if (score >= 60) return 'Warm Lead'
  if (score >= 40) return 'Cold Lead'
  return 'Low Quality'
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeTime = (dateString?: string) => {
  if (!dateString) return 'Never'
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  return date.toLocaleDateString()
}

const createProject = () => {
  router.push(`/crm/projects/new?customerId=${customerId.value}`)
}

const logCommunication = () => {
  router.push(`/crm/communications/new?customerId=${customerId.value}`)
}

const scheduleFollowUp = () => {
  router.push(`/crm/communications/new?customerId=${customerId.value}&type=reminder`)
}

const uploadDocument = () => {
  router.push(`/crm/documents/upload?customerId=${customerId.value}`)
}

const convertToCustomer = async () => {
  if (currentCustomer.value) {
    try {
      await storeConvertToCustomer(currentCustomer.value.id)
      // Show success message
    } catch (error) {
      console.error('Failed to convert customer:', error)
    }
  }
}

const deleteCustomer = async () => {
  if (currentCustomer.value && confirm(`Are you sure you want to delete ${currentCustomer.value.firstName} ${currentCustomer.value.lastName}?`)) {
    try {
      await storeDeleteCustomer(currentCustomer.value.id)
      router.push('/crm/customers')
    } catch (error) {
      console.error('Failed to delete customer:', error)
    }
  }
}

onMounted(() => {
  if (customerId.value) {
    fetchCustomer(customerId.value)
  }
})
</script>

<style scoped>
.customer-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
</style>
