<template>
  <div class="customer-list p-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Customers</h1>
        <p class="text-base-content/70 mt-1">Manage your customer relationships</p>
      </div>
      
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <RouterLink to="/crm/customers/new" class="btn btn-primary">
          + New Customer
        </RouterLink>
        <button @click="exportCustomers" class="btn btn-outline">
          Export
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <!-- Search -->
          <div class="form-control">
            <input 
              v-model="searchQuery"
              @input="handleSearch"
              type="text" 
              placeholder="Search customers..." 
              class="input input-bordered"
            />
          </div>
          
          <!-- Status Filter -->
          <div class="form-control">
            <select v-model="filters.status" @change="applyFilters" class="select select-bordered">
              <option value="">All Statuses</option>
              <option value="lead">Lead</option>
              <option value="prospect">Prospect</option>
              <option value="customer">Customer</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          
          <!-- Priority Filter -->
          <div class="form-control">
            <select v-model="filters.priority" @change="applyFilters" class="select select-bordered">
              <option value="">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>
          
          <!-- Lead Source Filter -->
          <div class="form-control">
            <select v-model="filters.leadSource" @change="applyFilters" class="select select-bordered">
              <option value="">All Sources</option>
              <option value="website">Website</option>
              <option value="referral">Referral</option>
              <option value="social_media">Social Media</option>
              <option value="email_campaign">Email Campaign</option>
              <option value="cold_outreach">Cold Outreach</option>
            </select>
          </div>
          
          <!-- Clear Filters -->
          <div class="form-control">
            <button @click="clearFilters" class="btn btn-ghost">
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Table -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>
                  <label>
                    <input 
                      type="checkbox" 
                      class="checkbox"
                      @change="toggleSelectAll"
                      :checked="selectedCustomers.length === customers.length && customers.length > 0"
                    />
                  </label>
                </th>
                <th @click="sort('firstName')" class="cursor-pointer hover:bg-base-200">
                  Name
                  <span v-if="sortBy === 'firstName'">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
                </th>
                <th @click="sort('companyName')" class="cursor-pointer hover:bg-base-200">
                  Company
                  <span v-if="sortBy === 'companyName'">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
                </th>
                <th @click="sort('status')" class="cursor-pointer hover:bg-base-200">
                  Status
                  <span v-if="sortBy === 'status'">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
                </th>
                <th @click="sort('leadScore')" class="cursor-pointer hover:bg-base-200">
                  Score
                  <span v-if="sortBy === 'leadScore'">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
                </th>
                <th @click="sort('createdAt')" class="cursor-pointer hover:bg-base-200">
                  Created
                  <span v-if="sortBy === 'createdAt'">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
                </th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="isLoading">
                <td colspan="7" class="text-center py-8">
                  <span class="loading loading-spinner loading-lg"></span>
                </td>
              </tr>
              <tr v-else-if="customers.length === 0">
                <td colspan="7" class="text-center py-8 text-base-content/70">
                  No customers found
                </td>
              </tr>
              <tr v-else v-for="customer in customers" :key="customer.id" class="hover">
                <td>
                  <label>
                    <input 
                      type="checkbox" 
                      class="checkbox"
                      :value="customer.id"
                      v-model="selectedCustomers"
                    />
                  </label>
                </td>
                <td>
                  <div class="flex items-center space-x-3">
                    <div class="avatar placeholder">
                      <div class="bg-primary text-primary-content rounded-full w-10">
                        <span class="text-sm">{{ getInitials(customer.firstName, customer.lastName) }}</span>
                      </div>
                    </div>
                    <div>
                      <div class="font-bold">{{ customer.firstName }} {{ customer.lastName }}</div>
                      <div class="text-sm opacity-70">{{ customer.email }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <div>
                    <div class="font-medium">{{ customer.companyName || 'N/A' }}</div>
                    <div class="text-sm opacity-70">{{ customer.jobTitle || '' }}</div>
                  </div>
                </td>
                <td>
                  <div class="badge" :class="getStatusBadgeClass(customer.status)">
                    {{ customer.status }}
                  </div>
                  <div v-if="customer.priority === 'high' || customer.priority === 'urgent'" class="badge badge-warning badge-sm mt-1">
                    {{ customer.priority }}
                  </div>
                </td>
                <td>
                  <div class="flex items-center space-x-2">
                    <div class="w-16 bg-base-200 rounded-full h-2">
                      <div 
                        class="h-2 rounded-full"
                        :class="getScoreColor(customer.leadScore)"
                        :style="{ width: `${customer.leadScore}%` }"
                      ></div>
                    </div>
                    <span class="text-sm font-medium">{{ customer.leadScore }}</span>
                  </div>
                </td>
                <td>
                  <div class="text-sm">{{ formatDate(customer.createdAt) }}</div>
                  <div class="text-xs opacity-70">{{ formatRelativeTime(customer.createdAt) }}</div>
                </td>
                <td>
                  <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                      ⋮
                    </div>
                    <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                      <li>
                        <RouterLink :to="`/crm/customers/${customer.id}`">
                          View Details
                        </RouterLink>
                      </li>
                      <li>
                        <RouterLink :to="`/crm/customers/${customer.id}/edit`">
                          Edit Customer
                        </RouterLink>
                      </li>
                      <li>
                        <a @click="createProject(customer)">Create Project</a>
                      </li>
                      <li>
                        <a @click="logCommunication(customer)">Log Communication</a>
                      </li>
                      <li v-if="customer.status === 'lead'">
                        <a @click="convertCustomer(customer)" class="text-success">
                          Convert to Customer
                        </a>
                      </li>
                      <li class="divider"></li>
                      <li>
                        <a @click="deleteCustomer(customer)" class="text-error">
                          Delete Customer
                        </a>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between mt-6">
          <div class="text-sm text-base-content/70">
            Showing {{ ((pagination.page - 1) * pagination.limit) + 1 }} to 
            {{ Math.min(pagination.page * pagination.limit, pagination.total) }} of 
            {{ pagination.total }} customers
          </div>
          
          <div class="join">
            <button 
              class="join-item btn btn-sm"
              :disabled="pagination.page <= 1"
              @click="changePage(pagination.page - 1)"
            >
              Previous
            </button>
            
            <button 
              v-for="page in visiblePages" 
              :key="page"
              class="join-item btn btn-sm"
              :class="{ 'btn-active': page === pagination.page }"
              @click="changePage(page)"
            >
              {{ page }}
            </button>
            
            <button 
              class="join-item btn btn-sm"
              :disabled="pagination.page >= pagination.pages"
              @click="changePage(pagination.page + 1)"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bulk Actions -->
    <div v-if="selectedCustomers.length > 0" class="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <div class="card bg-base-100 shadow-xl border">
        <div class="card-body py-4">
          <div class="flex items-center space-x-4">
            <span class="font-medium">{{ selectedCustomers.length }} selected</span>
            <button @click="bulkAssign" class="btn btn-sm btn-primary">
              Assign
            </button>
            <button @click="bulkUpdateStatus" class="btn btn-sm btn-secondary">
              Update Status
            </button>
            <button @click="bulkDelete" class="btn btn-sm btn-error">
              Delete
            </button>
            <button @click="clearSelection" class="btn btn-sm btn-ghost">
              Clear
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useCRMStore } from '@/stores/crm'
import type { Customer } from '@/services/crm'

const router = useRouter()
const crmStore = useCRMStore()

const {
  customers,
  isLoading,
  customerPagination: pagination,
  customerFilters,
  fetchCustomers,
  setCustomerFilters,
  clearFilters: storeClearFilters,
  deleteCustomer: storeDeleteCustomer,
  convertToCustomer
} = crmStore

// Local state
const searchQuery = ref('')
const selectedCustomers = ref<number[]>([])
const sortBy = ref('createdAt')
const sortOrder = ref<'asc' | 'desc'>('desc')

const filters = ref({
  status: '',
  priority: '',
  leadSource: ''
})

// Computed
const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, pagination.page - 2)
  const end = Math.min(pagination.pages, pagination.page + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const handleSearch = () => {
  setCustomerFilters({ search: searchQuery.value })
  fetchCustomers()
}

const applyFilters = () => {
  setCustomerFilters(filters.value)
  fetchCustomers()
}

const clearFilters = () => {
  searchQuery.value = ''
  filters.value = {
    status: '',
    priority: '',
    leadSource: ''
  }
  storeClearFilters()
  fetchCustomers()
}

const sort = (field: string) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = field
    sortOrder.value = 'asc'
  }
  
  setCustomerFilters({ sortBy: sortBy.value, sortOrder: sortOrder.value })
  fetchCustomers()
}

const changePage = (page: number) => {
  pagination.page = page
  fetchCustomers()
}

const toggleSelectAll = () => {
  if (selectedCustomers.value.length === customers.length) {
    selectedCustomers.value = []
  } else {
    selectedCustomers.value = customers.map(c => c.id)
  }
}

const clearSelection = () => {
  selectedCustomers.value = []
}

const getInitials = (firstName: string, lastName: string) => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    lead: 'badge-info',
    prospect: 'badge-warning',
    customer: 'badge-success',
    inactive: 'badge-ghost'
  }
  return classes[status as keyof typeof classes] || 'badge-ghost'
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'bg-success'
  if (score >= 60) return 'bg-warning'
  if (score >= 40) return 'bg-info'
  return 'bg-error'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return 'Today'
  if (diffDays === 1) return 'Yesterday'
  if (diffDays < 7) return `${diffDays} days ago`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
  return `${Math.floor(diffDays / 30)} months ago`
}

const createProject = (customer: Customer) => {
  router.push(`/crm/projects/new?customerId=${customer.id}`)
}

const logCommunication = (customer: Customer) => {
  router.push(`/crm/communications/new?customerId=${customer.id}`)
}

const convertCustomer = async (customer: Customer) => {
  try {
    await convertToCustomer(customer.id)
    // Show success message
  } catch (error) {
    console.error('Failed to convert customer:', error)
  }
}

const deleteCustomer = async (customer: Customer) => {
  if (confirm(`Are you sure you want to delete ${customer.firstName} ${customer.lastName}?`)) {
    try {
      await storeDeleteCustomer(customer.id)
      // Show success message
    } catch (error) {
      console.error('Failed to delete customer:', error)
    }
  }
}

const exportCustomers = () => {
  // Implement export functionality
  console.log('Export customers')
}

const bulkAssign = () => {
  // Implement bulk assign
  console.log('Bulk assign:', selectedCustomers.value)
}

const bulkUpdateStatus = () => {
  // Implement bulk status update
  console.log('Bulk update status:', selectedCustomers.value)
}

const bulkDelete = () => {
  if (confirm(`Are you sure you want to delete ${selectedCustomers.value.length} customers?`)) {
    // Implement bulk delete
    console.log('Bulk delete:', selectedCustomers.value)
  }
}

onMounted(() => {
  fetchCustomers()
})
</script>

<style scoped>
.customer-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
</style>
