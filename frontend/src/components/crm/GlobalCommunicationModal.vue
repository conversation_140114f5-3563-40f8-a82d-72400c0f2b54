<template>
  <CommunicationModal 
    @success="onSuccess"
  />
</template>

<script setup lang="ts">
import { useCommunicationModal } from '@/composables/useCommunicationModal'
import { useCRMStore } from '@/stores/crm'
import CommunicationModal from './CommunicationModal.vue'

const { closeModal } = useCommunicationModal()
const crmStore = useCRMStore()

const onSuccess = (communication: any) => {
  // Refresh communications list
  crmStore.fetchCommunications({ limit: 10 })
  
  // Show success notification
  // You can replace this with a proper toast notification system
  const event = new CustomEvent('show-notification', {
    detail: {
      type: 'success',
      message: 'Communication logged successfully!',
      duration: 3000
    }
  })
  window.dispatchEvent(event)
}
</script>
