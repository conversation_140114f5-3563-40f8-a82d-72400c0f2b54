<template>
  <div class="communication-hub p-6">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-base-content">Communication Hub</h1>
        <p class="text-base-content opacity-70 mt-1">Track all customer interactions and follow-ups</p>
      </div>
      
      <div class="flex items-center space-x-4 mt-4 lg:mt-0">
        <RouterLink to="/crm/communications/new" class="btn btn-primary">
          + Log Communication
        </RouterLink>
        <button @click="markAllAsRead" class="btn btn-outline">
          Mark All Read
        </button>
      </div>
    </div>

    <!-- Communication Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="card bg-gradient-to-r from-primary to-primary-focus text-primary-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Total Communications</h3>
          <div class="text-3xl font-bold">{{ communications.length }}</div>
          <div class="text-sm opacity-80">All time</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-warning to-warning-focus text-warning-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Unread</h3>
          <div class="text-3xl font-bold">{{ unreadCommunications.length }}</div>
          <div class="text-sm opacity-80">Need attention</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-info to-info-focus text-info-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">Follow-ups</h3>
          <div class="text-3xl font-bold">{{ followUpCommunications.length }}</div>
          <div class="text-sm opacity-80">Scheduled</div>
        </div>
      </div>
      
      <div class="card bg-gradient-to-r from-success to-success-focus text-success-content">
        <div class="card-body">
          <h3 class="text-lg font-semibold opacity-90">This Week</h3>
          <div class="text-3xl font-bold">{{ thisWeekCommunications.length }}</div>
          <div class="text-sm opacity-80">Communications</div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card bg-base-100 shadow-lg mb-6">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <!-- Search -->
          <div class="form-control">
            <input 
              v-model="searchQuery"
              @input="handleSearch"
              type="text" 
              placeholder="Search communications..." 
              class="input input-bordered"
            />
          </div>
          
          <!-- Type Filter -->
          <div class="form-control">
            <select v-model="filters.type" @change="applyFilters" class="select select-bordered">
              <option value="">All Types</option>
              <option value="email">Email</option>
              <option value="phone_call">Phone Call</option>
              <option value="meeting">Meeting</option>
              <option value="video_call">Video Call</option>
              <option value="note">Note</option>
              <option value="task">Task</option>
            </select>
          </div>
          
          <!-- Status Filter -->
          <div class="form-control">
            <select v-model="filters.status" @change="applyFilters" class="select select-bordered">
              <option value="">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no_response">No Response</option>
            </select>
          </div>
          
          <!-- Customer Filter -->
          <div class="form-control">
            <select v-model="filters.customerId" @change="applyFilters" class="select select-bordered">
              <option value="">All Customers</option>
              <!-- Add customers dynamically -->
            </select>
          </div>
          
          <!-- Read Status -->
          <div class="form-control">
            <select v-model="filters.isRead" @change="applyFilters" class="select select-bordered">
              <option value="">All</option>
              <option value="false">Unread</option>
              <option value="true">Read</option>
            </select>
          </div>
          
          <!-- Clear Filters -->
          <div class="form-control">
            <button @click="clearFilters" class="btn btn-ghost">
              Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Communication Timeline -->
    <div class="card bg-base-100 shadow-lg">
      <div class="card-body">
        <h3 class="card-title mb-6">Communication Timeline</h3>
        
        <div v-if="isLoading" class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-start space-x-4 p-4 bg-base-200 rounded-lg">
              <div class="w-12 h-12 bg-base-300 rounded-full"></div>
              <div class="flex-1">
                <div class="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                <div class="h-3 bg-base-300 rounded w-1/2 mb-2"></div>
                <div class="h-2 bg-base-300 rounded w-full"></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="communications.length === 0" class="text-center py-12">
          <h3 class="text-xl font-semibold text-base-content/70 mb-2">No communications found</h3>
          <p class="text-base-content/50 mb-4">Start logging customer interactions</p>
          <RouterLink to="/crm/communications/new" class="btn btn-primary">
            + Log Communication
          </RouterLink>
        </div>

        <div v-else class="space-y-4">
          <div 
            v-for="comm in communications" 
            :key="comm.id" 
            class="communication-item p-4 rounded-lg border transition-all hover:shadow-md"
            :class="[
              comm.isRead ? 'bg-base-50 border-base-200' : 'bg-warning/5 border-warning/20',
              comm.followUpRequired ? 'border-l-4 border-l-warning' : ''
            ]"
          >
            <div class="flex items-start space-x-4">
              <!-- Communication Icon -->
              <div class="avatar placeholder">
                <div 
                  class="rounded-full w-12 h-12 text-white"
                  :class="getTypeIconClass(comm.type)"
                >
                  <span class="text-lg">{{ getTypeIcon(comm.type) }}</span>
                </div>
              </div>

              <!-- Communication Content -->
              <div class="flex-1">
                <!-- Header -->
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <h4 class="font-semibold text-lg">
                      {{ comm.subject || `${comm.type.replace('_', ' ')} with ${comm.customer?.firstName} ${comm.customer?.lastName}` }}
                    </h4>
                    <div v-if="!comm.isRead" class="badge badge-warning badge-sm">
                      New
                    </div>
                    <div v-if="comm.followUpRequired" class="badge badge-info badge-sm">
                      Follow-up
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm text-base-content opacity-70">
                      {{ formatRelativeTime(comm.occurredAt || comm.createdAt) }}
                    </span>
                    <div class="dropdown dropdown-end">
                      <div tabindex="0" role="button" class="btn btn-ghost btn-sm">
                        ⋮
                      </div>
                      <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow">
                        <li><a @click="markAsRead(comm)">Mark as Read</a></li>
                        <li><a @click="editCommunication(comm)">Edit</a></li>
                        <li><a @click="scheduleFollowUp(comm)">Schedule Follow-up</a></li>
                        <li class="divider"></li>
                        <li><a @click="deleteCommunication(comm)" class="text-error">Delete</a></li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Customer & Project Info -->
                <div class="flex items-center space-x-4 mb-3 text-sm text-base-content/70">
                  <div v-if="comm.customer" class="flex items-center space-x-1">
                    <span>👤</span>
                    <RouterLink 
                      :to="`/crm/customers/${comm.customer.id}`"
                      class="text-primary hover:underline"
                    >
                      {{ comm.customer.firstName }} {{ comm.customer.lastName }}
                    </RouterLink>
                  </div>
                  <div v-if="comm.project" class="flex items-center space-x-1">
                    <span>📋</span>
                    <RouterLink 
                      :to="`/crm/projects/${comm.project.id}`"
                      class="text-primary hover:underline"
                    >
                      {{ comm.project.title }}
                    </RouterLink>
                  </div>
                  <div v-if="comm.user" class="flex items-center space-x-1">
                    <span>👨‍💼</span>
                    <span>{{ comm.user.name }}</span>
                  </div>
                </div>

                <!-- Communication Content -->
                <div v-if="comm.content || comm.summary" class="mb-3">
                  <p class="text-base-content">
                    {{ comm.summary || comm.content?.substring(0, 200) + (comm.content?.length > 200 ? '...' : '') }}
                  </p>
                </div>

                <!-- Communication Details -->
                <div class="flex items-center space-x-4 text-sm">
                  <!-- Type & Direction -->
                  <div class="flex items-center space-x-2">
                    <div class="badge badge-outline badge-sm">
                      {{ comm.type.replace('_', ' ') }}
                    </div>
                    <div class="badge badge-ghost badge-sm">
                      {{ comm.direction }}
                    </div>
                  </div>

                  <!-- Duration -->
                  <div v-if="comm.duration" class="flex items-center space-x-1">
                    <span>⏱️</span>
                    <span>{{ formatDuration(comm.duration) }}</span>
                  </div>

                  <!-- Outcome -->
                  <div v-if="comm.outcome" class="flex items-center space-x-1">
                    <div class="badge badge-sm" :class="getOutcomeBadgeClass(comm.outcome)">
                      {{ comm.outcome.replace('_', ' ') }}
                    </div>
                  </div>

                  <!-- Follow-up Date -->
                  <div v-if="comm.followUpDate" class="flex items-center space-x-1">
                    <span>📅</span>
                    <span class="text-warning">Follow-up: {{ formatDate(comm.followUpDate) }}</span>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2 mt-4">
                  <button 
                    v-if="!comm.isRead"
                    @click="markAsRead(comm)" 
                    class="btn btn-sm btn-primary"
                  >
                    Mark as Read
                  </button>
                  <button 
                    v-if="comm.followUpRequired && !comm.followUpDate"
                    @click="scheduleFollowUp(comm)" 
                    class="btn btn-sm btn-warning"
                  >
                    Schedule Follow-up
                  </button>
                  <RouterLink 
                    v-if="comm.customer"
                    :to="`/crm/communications/new?customerId=${comm.customer.id}&replyTo=${comm.id}`" 
                    class="btn btn-sm btn-ghost"
                  >
                    Reply
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="communications.length > 0 && communications.length < totalCommunications" class="text-center mt-6">
          <button @click="loadMore" class="btn btn-outline" :disabled="isLoading">
            <span v-if="!isLoading">Load More</span>
            <span v-else class="loading loading-spinner loading-sm"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCRMStore } from '@/stores/crm'
import type { Communication } from '@/services/crm'

const router = useRouter()
const crmStore = useCRMStore()

const {
  communications,
  isLoading,
  unreadCommunications,
  fetchCommunications,
  createCommunication,
  updateCommunication,
  deleteCommunication: storeDeleteCommunication
} = crmStore

// Local state
const searchQuery = ref('')
const totalCommunications = ref(0)
const filters = ref({
  type: '',
  status: '',
  customerId: null as number | null,
  isRead: ''
})

// Computed
const followUpCommunications = computed(() => 
  communications.filter(c => c.followUpRequired && !c.followUpDate)
)

const thisWeekCommunications = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  return communications.filter(c => new Date(c.createdAt) >= oneWeekAgo)
})

// Methods
const handleSearch = () => {
  // Implement search
  console.log('Search:', searchQuery.value)
}

const applyFilters = () => {
  // Implement filters
  console.log('Apply filters:', filters.value)
}

const clearFilters = () => {
  searchQuery.value = ''
  filters.value = {
    type: '',
    status: '',
    customerId: null,
    isRead: ''
  }
}

const getTypeIcon = (type: string) => {
  const icons = {
    email: '📧',
    phone_call: '📞',
    meeting: '🤝',
    video_call: '📹',
    text_message: '💬',
    in_person: '👥',
    note: '📝',
    task: '✅',
    reminder: '⏰'
  }
  return icons[type as keyof typeof icons] || '💬'
}

const getTypeIconClass = (type: string) => {
  const classes = {
    email: 'bg-blue-500',
    phone_call: 'bg-green-500',
    meeting: 'bg-purple-500',
    video_call: 'bg-indigo-500',
    text_message: 'bg-cyan-500',
    in_person: 'bg-orange-500',
    note: 'bg-gray-500',
    task: 'bg-emerald-500',
    reminder: 'bg-yellow-500'
  }
  return classes[type as keyof typeof classes] || 'bg-gray-500'
}

const getOutcomeBadgeClass = (outcome: string) => {
  const classes = {
    successful: 'badge-success',
    follow_up_needed: 'badge-warning',
    not_interested: 'badge-error',
    callback_requested: 'badge-info',
    meeting_scheduled: 'badge-primary',
    proposal_requested: 'badge-secondary'
  }
  return classes[outcome as keyof typeof classes] || 'badge-ghost'
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)
  
  if (diffMins < 1) return 'Just now'
  if (diffMins < 60) return `${diffMins}m ago`
  if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
  if (diffMins < 10080) return `${Math.floor(diffMins / 1440)}d ago`
  return date.toLocaleDateString()
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}

const formatDuration = (minutes: number) => {
  if (minutes < 60) return `${minutes}m`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const markAsRead = async (comm: Communication) => {
  try {
    await updateCommunication(comm.id, { isRead: true })
    comm.isRead = true
  } catch (error) {
    console.error('Failed to mark as read:', error)
  }
}

const markAllAsRead = async () => {
  // Implement mark all as read
  console.log('Mark all as read')
}

const editCommunication = (comm: Communication) => {
  router.push(`/crm/communications/${comm.id}/edit`)
}

const scheduleFollowUp = (comm: Communication) => {
  router.push(`/crm/communications/new?customerId=${comm.customerId}&followUpFor=${comm.id}`)
}

const deleteCommunication = async (comm: Communication) => {
  if (confirm('Are you sure you want to delete this communication?')) {
    try {
      await storeDeleteCommunication(comm.id)
    } catch (error) {
      console.error('Failed to delete communication:', error)
    }
  }
}

const loadMore = () => {
  // Implement load more
  console.log('Load more communications')
}

onMounted(() => {
  fetchCommunications()
})
</script>

<style scoped>
.communication-hub {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.communication-item {
  transition: all 0.2s ease;
}

.communication-item:hover {
  transform: translateY(-1px);
}
</style>
