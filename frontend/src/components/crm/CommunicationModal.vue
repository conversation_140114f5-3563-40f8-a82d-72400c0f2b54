<template>
  <div v-if="isModalOpen" class="modal modal-open">
    <div class="modal-box max-w-4xl glass-effect bg-base-100/90 backdrop-blur-md border border-white/20">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-2xl font-bold text-base-content">Log Communication</h3>
          <p class="text-base-content/70 mt-1">Record customer interactions and communications</p>
        </div>
        <button @click="closeModal" class="btn btn-ghost btn-sm">
          <Icon name="x" size="sm" />
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isInitializing" class="flex items-center justify-center py-12">
        <div class="text-center space-y-4">
          <div class="loading loading-spinner loading-lg text-primary"></div>
          <p class="text-base-content/70">Loading communication form...</p>
        </div>
      </div>

      <!-- Communication Form -->
      <div v-else>
        <form @submit.prevent="submitCommunication" class="space-y-6">
          <!-- Customer Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Customer *</span>
              </label>
              <select 
                v-model="form.customerId" 
                class="select select-bordered w-full"
                required
                :disabled="isInitializing"
              >
                <option value="">{{ isInitializing ? 'Loading customers...' : 'Select a customer' }}</option>
                <option 
                  v-for="customer in (customers || [])" 
                  :key="customer.id" 
                  :value="customer.id"
                >
                  {{ customer.firstName }} {{ customer.lastName }} - {{ customer.email }}
                </option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Project (Optional)</span>
              </label>
              <select 
                v-model="form.projectId" 
                class="select select-bordered w-full"
                :disabled="!form.customerId"
              >
                <option value="">No project</option>
                <option 
                  v-for="project in filteredProjects" 
                  :key="project.id" 
                  :value="project.id"
                >
                  {{ project.title }}
                </option>
              </select>
            </div>
          </div>

          <!-- Communication Details -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Type *</span>
              </label>
              <select v-model="form.type" class="select select-bordered w-full" required>
                <option value="">Select type</option>
                <option value="email">Email</option>
                <option value="phone">Phone Call</option>
                <option value="meeting">Meeting</option>
                <option value="note">Note</option>
                <option value="proposal">Proposal</option>
                <option value="contract">Contract</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Direction</span>
              </label>
              <select v-model="form.direction" class="select select-bordered w-full">
                <option value="outbound">Outbound</option>
                <option value="inbound">Inbound</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Priority</span>
              </label>
              <select v-model="form.priority" class="select select-bordered w-full">
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <!-- Subject and Content -->
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Subject</span>
            </label>
            <input 
              v-model="form.subject" 
              type="text" 
              placeholder="Communication subject or title"
              class="input input-bordered w-full"
            />
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">Content *</span>
            </label>
            <textarea 
              v-model="form.content" 
              placeholder="Describe the communication details, outcomes, and any follow-up actions needed..."
              class="textarea textarea-bordered w-full h-32"
              required
            ></textarea>
          </div>

          <!-- Status and Timing -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Status</span>
              </label>
              <select v-model="form.status" class="select select-bordered w-full">
                <option value="unread">Unread</option>
                <option value="read">Read</option>
                <option value="replied">Replied</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div class="form-control">
              <label class="label">
                <span class="label-text font-medium">Communication Date</span>
              </label>
              <input 
                v-model="form.scheduledAt" 
                type="datetime-local" 
                class="input input-bordered w-full"
              />
            </div>
          </div>

          <!-- Modal Actions -->
          <div class="modal-action">
            <button type="button" @click="closeModal" class="btn btn-ghost">
              Cancel
            </button>
            <button 
              type="submit" 
              class="btn btn-primary"
              :disabled="isSubmitting"
            >
              <span v-if="!isSubmitting">Log Communication</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useCRMStore } from '@/stores/crm'
import Icon from '@/components/common/Icon.vue'

// Props (keeping for backward compatibility)
interface Props {
  isOpen?: boolean
  preselectedCustomerId?: number | null
  preselectedProjectId?: number | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  preselectedCustomerId: null,
  preselectedProjectId: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: [communication: any]
}>()

// Use composable for global modal state
import { useCommunicationModal } from '@/composables/useCommunicationModal'
const {
  isOpen: globalIsOpen,
  preselectedCustomerId: globalCustomerId,
  preselectedProjectId: globalProjectId,
  closeModal: closeGlobalModal
} = useCommunicationModal()

// Use either props or global state
const isModalOpen = computed(() => props.isOpen || globalIsOpen.value)
const selectedCustomerId = computed(() => props.preselectedCustomerId || globalCustomerId.value)
const selectedProjectId = computed(() => props.preselectedProjectId || globalProjectId.value)

const crmStore = useCRMStore()

const {
  customers,
  projects,
  fetchCustomers,
  fetchProjects,
  createCommunication
} = crmStore

// Form state
const isSubmitting = ref(false)
const isInitializing = ref(true)
const form = ref({
  customerId: '',
  projectId: '',
  type: '',
  direction: 'outbound',
  priority: 'medium',
  subject: '',
  content: '',
  status: 'read',
  scheduledAt: new Date().toISOString().slice(0, 16)
})

// Computed properties
const filteredProjects = computed(() => {
  if (!form.value.customerId) return []
  return (projects.value || []).filter(project => project.customerId == form.value.customerId)
})

// Methods
const closeModal = () => {
  emit('close')
  closeGlobalModal()
  resetForm()
}

const resetForm = () => {
  form.value = {
    customerId: selectedCustomerId.value?.toString() || '',
    projectId: selectedProjectId.value?.toString() || '',
    type: '',
    direction: 'outbound',
    priority: 'medium',
    subject: '',
    content: '',
    status: 'read',
    scheduledAt: new Date().toISOString().slice(0, 16)
  }
}

const submitCommunication = async () => {
  try {
    isSubmitting.value = true
    
    const communicationData = {
      ...form.value,
      customerId: parseInt(form.value.customerId),
      projectId: form.value.projectId ? parseInt(form.value.projectId) : null,
      scheduledAt: form.value.scheduledAt ? new Date(form.value.scheduledAt).toISOString() : null
    }
    
    const communication = await createCommunication(communicationData)
    
    emit('success', communication)
    closeModal()
    
  } catch (error) {
    console.error('Error logging communication:', error)
    alert('Failed to log communication. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}

const initializeData = async () => {
  try {
    isInitializing.value = true
    await Promise.all([
      fetchCustomers({ limit: 100 }).catch(err => {
        console.warn('Failed to fetch customers:', err)
        return { items: [], pagination: {} }
      }),
      fetchProjects({ limit: 100 }).catch(err => {
        console.warn('Failed to fetch projects:', err)
        return { items: [], pagination: {} }
      })
    ])
  } catch (error) {
    console.error('Error initializing communication modal:', error)
  } finally {
    isInitializing.value = false
  }
}

// Watch for modal open to initialize data
watch(() => isModalOpen.value, (newValue) => {
  if (newValue) {
    initializeData()
    resetForm()
  }
}, { immediate: true })

// Watch for preselected values
watch(() => [selectedCustomerId.value, selectedProjectId.value], () => {
  if (isModalOpen.value) {
    resetForm()
  }
})
</script>

<style scoped>
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}
</style>
