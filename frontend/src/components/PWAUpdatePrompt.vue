<template>
  <div v-if="needRefresh || offlineReady" class="pwa-update-prompt">
    <!-- Update Available Notification - Mobile Optimized -->
    <div v-if="needRefresh && showUpdatePrompt" class="fixed top-4 left-4 right-4 md:left-1/2 md:right-auto md:transform md:-translate-x-1/2 md:max-w-md z-50">
      <div class="glass-effect shadow-2xl backdrop-blur-md border transition-all duration-300 hover:scale-[1.02] rounded-lg p-4 relative update-prompt-bg">
        <!-- Close Button -->
        <button @click="dismissUpdate" class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2 z-10 update-close-btn">
          ✕
        </button>

        <!-- Content with proper spacing -->
        <div class="pr-8"> <!-- Add padding to avoid overlap with close button -->
          <div class="flex items-start space-x-3 mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-5 w-5 md:h-6 md:w-6 mt-0.5 update-icon" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <div class="flex-1 min-w-0">
              <h3 class="font-semibold text-sm md:text-base">Update Available!</h3>
              <p class="text-xs md:text-sm opacity-80 break-words">Version {{ newVersion }} is ready</p>
              <div v-if="latestChangelog" class="text-xs opacity-70 mt-1 break-words">
                {{ getUpdateSummary() }}
              </div>
            </div>
          </div>

          <!-- Action Buttons - Mobile Optimized -->
          <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:justify-end">
            <button @click="dismissUpdate" class="btn btn-sm btn-ghost order-2 sm:order-1 update-later-btn">
              Later
            </button>
            <button @click="updateApp" class="btn btn-sm btn-primary order-1 sm:order-2 update-now-btn" :disabled="isUpdating">
              <span v-if="!isUpdating">Update Now</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Offline Ready Notification - Mobile Optimized, No WhatsApp Overlap -->
    <div v-if="offlineReady && !needRefresh && showOfflineReady" class="fixed bottom-4 left-4 right-20 md:left-auto md:right-4 md:max-w-sm z-50">
      <div class="glass-effect shadow-2xl backdrop-blur-md border border-white/20 transition-all duration-300 hover:scale-[1.02] bg-gradient-to-br from-success/80 to-success/60 text-white rounded-lg p-4 relative">
        <!-- Close Button -->
        <button @click="dismissOfflineReady" class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2 z-10 text-white hover:bg-white/20" aria-label="Dismiss offline ready notification">
          ✕
        </button>

        <!-- Content with proper spacing -->
        <div class="pr-8"> <!-- Add padding to avoid overlap with close button -->
          <div class="flex items-center space-x-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-5 w-5 md:h-6 md:w-6 text-white" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div class="flex-1 min-w-0">
              <h3 class="font-semibold text-sm md:text-base text-white">App Ready!</h3>
              <p class="text-xs md:text-sm opacity-80 break-words text-white">HLenergy works offline now</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Update Progress Modal - Mobile Optimized -->
    <div v-if="isUpdating" class="modal modal-open">
      <div class="modal-box glass-effect bg-base-100/90 backdrop-blur-md border border-white/20 max-w-sm md:max-w-lg mx-4">
        <h3 class="font-bold text-base md:text-lg">Updating HLenergy</h3>
        <div class="py-4">
          <div class="flex items-center space-x-3">
            <span class="loading loading-spinner loading-sm md:loading-md"></span>
            <span class="text-sm md:text-base">{{ updateProgress }}</span>
          </div>
          <progress class="progress progress-primary w-full mt-4" :value="progressValue" max="100"></progress>
        </div>
        <p class="text-xs md:text-sm text-base-content/70">
          Please wait while we update the app to the latest version...
        </p>
      </div>
    </div>

    <!-- Update Success Modal - Mobile Optimized -->
    <div v-if="updateComplete" class="modal modal-open">
      <div class="modal-box glass-effect bg-base-100/90 backdrop-blur-md border border-white/20 max-w-sm md:max-w-lg mx-4">
        <h3 class="font-bold text-base md:text-lg text-success">✅ Update Complete!</h3>
        <div class="py-4">
          <p class="text-sm md:text-base">HLenergy has been updated to the latest version.</p>
          <p class="text-xs md:text-sm text-base-content/70 mt-2">
            The page will reload automatically to apply the changes.
          </p>
        </div>
        <div class="modal-action">
          <button @click="reloadApp" class="btn btn-primary btn-sm md:btn-md w-full md:w-auto">
            Reload Now
          </button>
        </div>
      </div>
    </div>

    <!-- Force Update Banner (for critical updates) - Mobile Optimized -->
    <div v-if="forceUpdate" class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div class="card glass-effect bg-base-100/90 backdrop-blur-md border border-white/20 shadow-xl w-full max-w-sm md:max-w-md">
        <div class="card-body text-center p-4 md:p-6">
          <h2 class="card-title justify-center text-warning text-sm md:text-base flex-col md:flex-row gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>Critical Update Required</span>
          </h2>
          <p class="py-4 text-xs md:text-sm">
            A critical security update is available. The app must be updated to continue.
          </p>
          <div class="card-actions justify-center w-full">
            <button @click="updateApp" class="btn btn-warning btn-sm md:btn-md w-full md:w-auto" :disabled="isUpdating">
              <span v-if="!isUpdating">Update Now</span>
              <span v-else class="loading loading-spinner loading-sm"></span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { usePWA } from '@/composables/usePWA'
import { VERSION_INFO, getLatestChangelog } from '@/config/version'

const {
  needRefresh,
  offlineReady,
  updateServiceWorker
} = usePWA()

// Local state
const isUpdating = ref(false)
const updateComplete = ref(false)
const updateProgress = ref('Preparing update...')
const progressValue = ref(0)
const forceUpdate = ref(false)
const showOfflineReady = ref(true)
const showUpdatePrompt = ref(true)

// Version information
const currentVersion = computed(() => VERSION_INFO.version)
const latestChangelog = computed(() => getLatestChangelog())
const newVersion = computed(() => {
  // In a real app, this would come from the service worker or API
  // For now, we'll increment the patch version as an example
  const parts = VERSION_INFO.version.split('.')
  parts[2] = (parseInt(parts[2]) + 1).toString()
  return parts.join('.')
})

// Computed
const shouldShowUpdate = computed(() => needRefresh.value && showUpdatePrompt.value)
const shouldShowOfflineReady = computed(() => offlineReady.value && showOfflineReady.value && !needRefresh.value)

// Methods
const updateApp = async () => {
  try {
    isUpdating.value = true
    updateProgress.value = 'Downloading update...'
    progressValue.value = 25

    // Simulate update progress
    await new Promise(resolve => setTimeout(resolve, 1000))
    updateProgress.value = 'Installing update...'
    progressValue.value = 50

    await new Promise(resolve => setTimeout(resolve, 1000))
    updateProgress.value = 'Finalizing...'
    progressValue.value = 75

    // Actually update the service worker
    await updateServiceWorker()
    
    progressValue.value = 100
    updateProgress.value = 'Update complete!'
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    isUpdating.value = false
    updateComplete.value = true
    
    // Auto-reload after 3 seconds
    setTimeout(() => {
      reloadApp()
    }, 3000)
    
  } catch (error) {
    console.error('Update failed:', error)
    isUpdating.value = false
    updateProgress.value = 'Update failed. Please try again.'
    
    // Show error notification
    showErrorNotification('Update failed. Please refresh the page manually.')
  }
}

const dismissUpdate = () => {
  showUpdatePrompt.value = false
  
  // Show reminder after 5 minutes
  setTimeout(() => {
    if (needRefresh.value) {
      showUpdatePrompt.value = true
    }
  }, 5 * 60 * 1000)
}

const dismissOfflineReady = () => {
  showOfflineReady.value = false
}

// Auto-dismiss offline ready notification after 8 seconds
let offlineReadyTimer: number | null = null

watch(offlineReady, (newValue: boolean) => {
  if (newValue && !needRefresh.value) {
    // Clear any existing timer
    if (offlineReadyTimer) {
      clearTimeout(offlineReadyTimer)
    }

    // Auto-dismiss after 8 seconds
    offlineReadyTimer = window.setTimeout(() => {
      if (showOfflineReady.value) {
        dismissOfflineReady()
      }
    }, 8000)
  }
})

// Clean up timer on unmount
onUnmounted(() => {
  if (offlineReadyTimer) {
    clearTimeout(offlineReadyTimer)
  }
})

const reloadApp = () => {
  window.location.reload()
}

const showErrorNotification = (message: string) => {
  // You can integrate with a toast/notification system here
  console.error(message)
  alert(message) // Fallback for now
}

const getUpdateSummary = () => {
  if (!latestChangelog.value) return ''

  const changes = latestChangelog.value.changes
  const totalChanges = Object.values(changes).reduce((sum: number, arr: any) => sum + (arr?.length || 0), 0)

  if (totalChanges === 0) return 'Bug fixes and improvements'

  const parts = []
  if (changes.added?.length) parts.push(`${changes.added.length} new feature${changes.added.length > 1 ? 's' : ''}`)
  if (changes.fixed?.length) parts.push(`${changes.fixed.length} bug fix${changes.fixed.length > 1 ? 'es' : ''}`)
  if (changes.changed?.length) parts.push(`${changes.changed.length} improvement${changes.changed.length > 1 ? 's' : ''}`)

  return parts.join(', ')
}

// Check for critical updates
const checkForCriticalUpdate = () => {
  // This would typically check a version endpoint
  // For now, we'll simulate based on certain conditions
  const lastUpdateCheck = localStorage.getItem('lastUpdateCheck')
  const now = Date.now()
  
  if (lastUpdateCheck) {
    const timeSinceLastCheck = now - parseInt(lastUpdateCheck)
    const oneWeek = 7 * 24 * 60 * 60 * 1000
    
    // Force update if it's been more than a week
    if (timeSinceLastCheck > oneWeek && needRefresh.value) {
      forceUpdate.value = true
    }
  }
  
  localStorage.setItem('lastUpdateCheck', now.toString())
}

// Auto-check for updates periodically
let updateCheckInterval: number

onMounted(() => {
  checkForCriticalUpdate()
  
  // Check for updates every 30 minutes
  updateCheckInterval = window.setInterval(() => {
    checkForCriticalUpdate()
  }, 30 * 60 * 1000)
  
  // Listen for online events to check for updates
  window.addEventListener('online', checkForCriticalUpdate)
})

onUnmounted(() => {
  if (updateCheckInterval) {
    clearInterval(updateCheckInterval)
  }
  window.removeEventListener('online', checkForCriticalUpdate)
})
</script>

<style scoped>
.pwa-update-prompt {
  /* Ensure proper z-index layering */
  z-index: 1000;
}

/* Proper glass effect */
.glass-effect {
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.glass-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-effect:hover::after {
  opacity: 1;
}

.glass-effect .card-body,
.glass-effect > div {
  position: relative;
  z-index: 3;
}

/* Enhanced hover effects */
.glass-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

/* Update prompt background - Light theme */
.update-prompt-bg {
  background: linear-gradient(135deg, #eaaa34/90, #5cad64/90);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Update prompt background - Dark theme */
[data-theme="hlenergy-dark"] .update-prompt-bg {
  background: linear-gradient(135deg, #02342b/80, #12816c/80);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

/* Update prompt styling - Light theme */
.update-prompt-bg {
  background: linear-gradient(135deg, #eaaa34/95, #5cad64/95);
  color: #02342b;
  border-color: rgba(2, 52, 43, 0.3);
}

.update-icon {
  color: #02342b;
}

.update-close-btn {
  color: #02342b;
}

.update-close-btn:hover {
  background-color: rgba(2, 52, 43, 0.1);
}

.update-later-btn {
  color: #02342b;
  border-color: rgba(2, 52, 43, 0.4);
}

.update-later-btn:hover {
  background-color: rgba(2, 52, 43, 0.1);
}

.update-now-btn {
  background-color: #5cad64;
  color: white;
  border-color: #5cad64;
}

.update-now-btn:hover {
  background-color: #389868;
}

/* Update prompt styling - Dark theme */
[data-theme="hlenergy-dark"] .update-prompt-bg {
  background: linear-gradient(135deg, #02342b/80, #12816c/80);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="hlenergy-dark"] .update-icon {
  color: white;
}

[data-theme="hlenergy-dark"] .update-close-btn {
  color: white;
}

[data-theme="hlenergy-dark"] .update-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme="hlenergy-dark"] .update-later-btn {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="hlenergy-dark"] .update-later-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme="hlenergy-dark"] .update-now-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="hlenergy-dark"] .update-now-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Dark theme adjustments */
[data-theme="hlenergy-dark"] .glass-effect::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 100%
  );
}

[data-theme="hlenergy-dark"] .glass-effect:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.3) inset;
}

/* Smooth animations */
.alert {
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Bottom notification animation */
.fixed.bottom-4.right-4 .alert {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Modal animations */
.modal {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Progress bar animation */
.progress {
  transition: all 0.3s ease;
}

/* Force update overlay */
.fixed.inset-0 {
  backdrop-filter: blur(4px);
}
</style>
