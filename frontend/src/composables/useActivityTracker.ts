import { onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

export function useActivityTracker() {
  const authStore = useAuthStore()

  // Activity events to track
  const activityEvents = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]

  // Throttle activity updates to avoid excessive calls
  let lastUpdate = 0
  const THROTTLE_MS = 30000 // Update every 30 seconds max

  const handleActivity = () => {
    const now = Date.now()
    if (now - lastUpdate > THROTTLE_MS) {
      lastUpdate = now
      authStore.updateActivity()
    }
  }

  const startTracking = () => {
    // Only track if user is authenticated and not locked
    if (authStore.isAuthenticated && !authStore.isLocked) {
      activityEvents.forEach(event => {
        document.addEventListener(event, handleActivity, { passive: true })
      })
    }
  }

  const stopTracking = () => {
    activityEvents.forEach(event => {
      document.removeEventListener(event, handleActivity)
    })
  }

  // Auto-start tracking on mount
  onMounted(() => {
    startTracking()
  })

  // Clean up on unmount
  onUnmounted(() => {
    stopTracking()
  })

  return {
    startTracking,
    stopTracking
  }
}
