import { ref } from 'vue'

// Global state for communication modal
const isOpen = ref(false)
const preselectedCustomerId = ref<number | null>(null)
const preselectedProjectId = ref<number | null>(null)

export function useCommunicationModal() {
  const openModal = (customerId?: number, projectId?: number) => {
    preselectedCustomerId.value = customerId || null
    preselectedProjectId.value = projectId || null
    isOpen.value = true
  }

  const closeModal = () => {
    isOpen.value = false
    preselectedCustomerId.value = null
    preselectedProjectId.value = null
  }

  return {
    isOpen,
    preselectedCustomerId,
    preselectedProjectId,
    openModal,
    closeModal
  }
}
