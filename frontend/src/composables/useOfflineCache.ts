import { ref, computed, onMounted } from 'vue'
import { offlineCacheService, type CacheStats } from '@/services/offlineCache'

export function useOfflineCache() {
  // State
  const cacheStats = ref<CacheStats>({
    totalPages: 0,
    totalSize: 0,
    lastUpdated: new Date(),
    cacheVersion: '1.0.0'
  })
  
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const formattedCacheSize = computed(() => {
    const bytes = cacheStats.value.totalSize
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  })

  const cacheEfficiency = computed(() => {
    const total = cacheStats.value.totalPages
    if (total === 0) return 0
    
    // Estimate efficiency based on essential pages cached
    const essentialPages = 9 // Number of essential pages
    return Math.min((total / essentialPages) * 100, 100)
  })

  const lastUpdatedFormatted = computed(() => {
    return cacheStats.value.lastUpdated.toLocaleString()
  })

  // Methods
  const initializeCache = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      await offlineCacheService.initialize()
      await refreshCacheStats()
      
      isInitialized.value = true
      console.log('Offline cache initialized successfully')
    } catch (err: any) {
      error.value = err.message || 'Failed to initialize offline cache'
      console.error('Failed to initialize offline cache:', err)
    } finally {
      isLoading.value = false
    }
  }

  const refreshCacheStats = async () => {
    try {
      const stats = await offlineCacheService.getCacheStats()
      cacheStats.value = stats
    } catch (err: any) {
      console.error('Failed to refresh cache stats:', err)
    }
  }

  const clearCache = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      await offlineCacheService.clearCache()
      await refreshCacheStats()
      
      console.log('Cache cleared successfully')
    } catch (err: any) {
      error.value = err.message || 'Failed to clear cache'
      console.error('Failed to clear cache:', err)
    } finally {
      isLoading.value = false
    }
  }

  const preloadPages = async (urls: string[]) => {
    try {
      isLoading.value = true
      error.value = null
      
      await offlineCacheService.preloadPages(urls)
      await refreshCacheStats()
      
      console.log('Pages preloaded successfully')
    } catch (err: any) {
      error.value = err.message || 'Failed to preload pages'
      console.error('Failed to preload pages:', err)
    } finally {
      isLoading.value = false
    }
  }

  const checkPageCached = async (url: string): Promise<boolean> => {
    try {
      return await offlineCacheService.isPageCached(url)
    } catch (err: any) {
      console.error('Failed to check if page is cached:', err)
      return false
    }
  }

  const getCachedPages = async (): Promise<string[]> => {
    try {
      return await offlineCacheService.getCachedPages()
    } catch (err: any) {
      console.error('Failed to get cached pages:', err)
      return []
    }
  }

  const preloadEssentialPages = async () => {
    const essentialPages = [
      '/',
      '/about',
      '/services',
      '/contact',
      '/dashboard',
      '/crm/dashboard',
      '/crm/customers',
      '/crm/projects',
      '/crm/communications'
    ]

    console.log('Starting essential pages preload...')
    await preloadPages(essentialPages)
    console.log('Essential pages preload completed')
  }

  const preloadUserPages = async () => {
    // Get pages from browser history
    const userPages: string[] = []
    
    // Add current page
    userPages.push(window.location.pathname)
    
    // Add commonly visited pages (you could track this in localStorage)
    const visitedPages = JSON.parse(localStorage.getItem('visitedPages') || '[]')
    userPages.push(...visitedPages.slice(0, 10)) // Top 10 visited pages
    
    // Remove duplicates
    const uniquePages = [...new Set(userPages)]
    
    await preloadPages(uniquePages)
  }

  const trackPageVisit = (url: string) => {
    try {
      const visitedPages = JSON.parse(localStorage.getItem('visitedPages') || '[]')
      
      // Remove if already exists
      const filtered = visitedPages.filter((page: string) => page !== url)
      
      // Add to beginning
      filtered.unshift(url)
      
      // Keep only top 20
      const limited = filtered.slice(0, 20)
      
      localStorage.setItem('visitedPages', JSON.stringify(limited))
    } catch (error) {
      console.error('Failed to track page visit:', error)
    }
  }

  // Auto-initialize on mount
  onMounted(() => {
    initializeCache()
    
    // Track current page visit
    trackPageVisit(window.location.pathname)
    
    // Set up periodic cache stats refresh
    const interval = setInterval(refreshCacheStats, 30000) // Every 30 seconds
    
    // Cleanup on unmount
    return () => {
      clearInterval(interval)
    }
  })

  return {
    // State
    cacheStats,
    isInitialized,
    isLoading,
    error,
    
    // Computed
    formattedCacheSize,
    cacheEfficiency,
    lastUpdatedFormatted,
    
    // Methods
    initializeCache,
    refreshCacheStats,
    clearCache,
    preloadPages,
    preloadEssentialPages,
    preloadUserPages,
    checkPageCached,
    getCachedPages,
    trackPageVisit
  }
}

// Global cache management
export const globalCacheManager = {
  async warmupCache() {
    console.log('Warming up cache...')
    await offlineCacheService.initialize()
    
    // Preload essential pages
    const essentialPages = [
      '/',
      '/about',
      '/services',
      '/contact'
    ]
    
    await offlineCacheService.preloadPages(essentialPages)
    console.log('Cache warmup complete')
  },
  
  async getCacheInfo() {
    return await offlineCacheService.getCacheStats()
  }
}
