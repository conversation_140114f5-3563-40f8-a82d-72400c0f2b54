import { ref, reactive } from 'vue'

export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  icon?: string
}

interface ToastState {
  toasts: Toast[]
}

const state = reactive<ToastState>({
  toasts: []
})

let toastIdCounter = 0

export function useToast() {
  const generateId = () => `toast-${++toastIdCounter}-${Date.now()}`

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = generateId()
    const duration = toast.duration ?? 4000
    
    const newToast: Toast = {
      id,
      ...toast
    }

    state.toasts.push(newToast)

    // Auto-remove toast after duration
    if (duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, duration)
    }

    return id
  }

  const removeToast = (id: string) => {
    const index = state.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      state.toasts.splice(index, 1)
    }
  }

  const clearAllToasts = () => {
    state.toasts.splice(0)
  }

  // Convenience methods
  const showSuccess = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'success',
      title,
      message,
      duration,
      icon: 'check-circle'
    })
  }

  const showError = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'error',
      title,
      message,
      duration: duration ?? 6000, // Errors stay longer
      icon: 'exclamation-triangle'
    })
  }

  const showWarning = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: 'exclamation-triangle'
    })
  }

  const showInfo = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'info',
      title,
      message,
      duration,
      icon: 'info'
    })
  }

  return {
    // State
    toasts: state.toasts,
    
    // Methods
    addToast,
    removeToast,
    clearAllToasts,
    
    // Convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
