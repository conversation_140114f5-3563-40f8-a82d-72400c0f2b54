import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { analyticsService, type UserEvent } from '@/services/analytics'

export function useAnalytics() {
  const route = useRoute()
  const pageStartTime = ref(Date.now())
  const scrollDepthTracked = ref(new Set<number>())

  // Auto-track page views
  const trackPageView = () => {
    const page = route.path
    const title = document.title
    const referrer = document.referrer
    
    analyticsService.trackPageView(page, title, referrer)
    pageStartTime.value = Date.now()
    scrollDepthTracked.value.clear()
  }

  // Track page duration when leaving
  const trackPageDuration = () => {
    const duration = Date.now() - pageStartTime.value
    analyticsService.trackEvent('page_duration', 'page_view', {
      duration,
      page: route.path
    })
  }

  // Track scroll depth
  const trackScrollDepth = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight
    
    const scrollPercent = Math.round((scrollTop + windowHeight) / documentHeight * 100)
    
    // Track at 25%, 50%, 75%, 100% intervals
    const milestones = [25, 50, 75, 100]
    for (const milestone of milestones) {
      if (scrollPercent >= milestone && !scrollDepthTracked.value.has(milestone)) {
        scrollDepthTracked.value.add(milestone)
        analyticsService.trackScrollDepth(milestone)
      }
    }
  }

  // Track clicks for heatmap
  const trackClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    const rect = target.getBoundingClientRect()
    
    analyticsService.trackClick(
      event.clientX,
      event.clientY,
      target.tagName.toLowerCase(),
      target.textContent?.slice(0, 100) || undefined
    )
  }

  // Setup event listeners
  const setupEventListeners = () => {
    window.addEventListener('scroll', trackScrollDepth, { passive: true })
    document.addEventListener('click', trackClick, { passive: true })
    window.addEventListener('beforeunload', trackPageDuration)
  }

  // Cleanup event listeners
  const cleanupEventListeners = () => {
    window.removeEventListener('scroll', trackScrollDepth)
    document.removeEventListener('click', trackClick)
    window.removeEventListener('beforeunload', trackPageDuration)
  }

  // Initialize analytics
  onMounted(() => {
    trackPageView()
    setupEventListeners()
  })

  onUnmounted(() => {
    trackPageDuration()
    cleanupEventListeners()
  })

  return {
    // Core tracking methods
    trackEvent: analyticsService.trackEvent.bind(analyticsService),
    trackConversion: analyticsService.trackConversion.bind(analyticsService),
    trackFormInteraction: analyticsService.trackFormInteraction.bind(analyticsService),
    
    // Manual tracking
    trackPageView,
    trackClick: (x: number, y: number, element?: string, text?: string) => {
      analyticsService.trackClick(x, y, element, text)
    },
    
    // User identification
    setUserId: analyticsService.setUserId.bind(analyticsService),
    
    // Data retrieval
    getAnalyticsMetrics: analyticsService.getAnalyticsMetrics.bind(analyticsService),
    getBusinessMetrics: analyticsService.getBusinessMetrics.bind(analyticsService),
    getHeatmapData: analyticsService.getHeatmapData.bind(analyticsService),
    getConversionFunnel: analyticsService.getConversionFunnel.bind(analyticsService),
    getUserJourney: analyticsService.getUserJourney.bind(analyticsService),
    
    // A/B Testing
    getABTestVariant: analyticsService.getABTestVariant.bind(analyticsService),
    trackABTestConversion: analyticsService.trackABTestConversion.bind(analyticsService),
  }
}

// Specific composables for common use cases
export function useFormAnalytics(formName: string) {
  const { trackFormInteraction, trackConversion } = useAnalytics()
  
  const trackFormStart = () => {
    trackFormInteraction(formName, 'start')
  }
  
  const trackFormComplete = (conversionType?: 'contact_form' | 'registration' | 'login') => {
    trackFormInteraction(formName, 'complete')
    if (conversionType) {
      trackConversion(conversionType)
    }
  }
  
  const trackFormAbandon = (fieldName?: string) => {
    trackFormInteraction(formName, 'abandon', fieldName)
  }
  
  const trackFieldInteraction = (fieldName: string) => {
    trackFormInteraction(formName, 'start', fieldName)
  }
  
  return {
    trackFormStart,
    trackFormComplete,
    trackFormAbandon,
    trackFieldInteraction
  }
}

export function useBusinessAnalytics() {
  const { trackEvent, trackConversion } = useAnalytics()
  
  const trackServiceInquiry = (service: string, source?: string) => {
    trackEvent('service_inquiry', 'business_event', {
      service,
      source
    })
  }
  
  const trackLeadGeneration = (source: string, quality?: 'high' | 'medium' | 'low') => {
    trackConversion('contact_form', undefined, {
      source,
      quality
    })
  }
  
  const trackUserEngagement = (action: string, value?: number) => {
    trackEvent('engagement', 'user_action', {
      action,
      value
    })
  }
  
  const trackFeatureUsage = (feature: string, context?: string) => {
    trackEvent('feature_usage', 'user_action', {
      feature,
      context
    })
  }
  
  return {
    trackServiceInquiry,
    trackLeadGeneration,
    trackUserEngagement,
    trackFeatureUsage
  }
}
