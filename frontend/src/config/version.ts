// Version configuration for HLenergy webapp
export interface VersionInfo {
  version: string
  buildDate: string
  buildNumber: string
  gitCommit?: string
  environment: 'development' | 'staging' | 'production'
  features: string[]
  changelog: ChangelogEntry[]
}

export interface ChangelogEntry {
  version: string
  date: string
  changes: {
    added?: string[]
    changed?: string[]
    fixed?: string[]
    removed?: string[]
  }
}

// Declare build-time variables
declare const __BUILD_DATE__: string
declare const __GIT_COMMIT__: string
declare const __BUILD_NUMBER__: string

// Current version information
export const VERSION_INFO: VersionInfo = {
  version: '2.1.2',
  buildDate: typeof __BUILD_DATE__ !== 'undefined' ? __BUILD_DATE__ : new Date().toISOString(),
  buildNumber: typeof __BUILD_NUMBER__ !== 'undefined' ? __BUILD_NUMBER__ : '001',
  gitCommit: typeof __GIT_COMMIT__ !== 'undefined' ? __GIT_COMMIT__ : 'local',
  environment: (import.meta.env.MODE as any) || 'development',
  features: [
    'Multilingual Support (EN, ES, PT)',
    'Progressive Web App (PWA)',
    'Dark/Light Theme System',
    'Responsive Design',
    'SEO Optimization',
    'WhatsApp Integration',
    'Scroll-aware Navigation',
    'Offline Support',
    'Service Worker Caching'
  ],
  changelog: [
    {
      "version": "2.1.2",
      "date": "2025-06-06",
      "changes": {
        "added": [],
        "changed": [
          "Added PWA versioning"
        ],
        "fixed": [],
        "removed": []
      }
    },
    {
      "version": "2.1.1",
      "date": "2025-06-06",
      "changes": {
        "added": [
          "Added versioning script"
        ],
        "changed": [],
        "fixed": [],
        "removed": []
      }
    },
    {
      version: '2.1.0',
      date: '2024-01-15',
      changes: {
        added: [
          'Portuguese language support',
          'WhatsApp contact button',
          'Scroll-aware navigation bar',
          'Enhanced SEO with VueUse Head',
          'Webapp versioning system',
          'Mobile-responsive button text'
        ],
        changed: [
          'Badge opacity for better visual hierarchy',
          'Improved mobile navigation layout',
          'Enhanced multilingual SEO metadata'
        ],
        fixed: [
          'Services button visibility in light theme',
          'Mobile button text overflow',
          'Badge background consistency'
        ]
      }
    },
    {
      version: '2.0.0',
      date: '2024-01-10',
      changes: {
        added: [
          'Spanish language support',
          'Enhanced theme system with HLenergy branding',
          'Progressive Web App capabilities',
          'Service worker for offline support',
          'Advanced caching system',
          'Network status monitoring'
        ],
        changed: [
          'Complete UI redesign with DaisyUI',
          'Improved responsive design',
          'Enhanced accessibility features'
        ],
        fixed: [
          'Theme persistence across sessions',
          'Mobile navigation improvements',
          'Performance optimizations'
        ]
      }
    },
    {
      version: '1.5.0',
      date: '2024-01-05',
      changes: {
        added: [
          'Dark/Light theme switcher',
          'Enhanced logo integration',
          'Improved favicon system',
          'Better mobile experience'
        ],
        changed: [
          'Updated color palette',
          'Refined typography',
          'Improved button styling'
        ],
        fixed: [
          'Cross-browser compatibility issues',
          'Mobile layout bugs',
          'Theme switching glitches'
        ]
      }
    },
    {
      version: '1.0.0',
      date: '2024-01-01',
      changes: {
        added: [
          'Initial HLenergy website launch',
          'Core navigation structure',
          'Hero section with company branding',
          'Services showcase',
          'About section with B2B/B2C focus',
          'Contact form integration',
          'Basic responsive design'
        ]
      }
    }
  ]
}

// Utility functions
export const getCurrentVersion = (): string => VERSION_INFO.version

export const getBuildInfo = (): string => {
  return `v${VERSION_INFO.version} (Build ${VERSION_INFO.buildNumber})`
}

export const getFullVersionInfo = (): string => {
  const buildDate = new Date(VERSION_INFO.buildDate).toLocaleDateString()
  return `HLenergy v${VERSION_INFO.version} | Build ${VERSION_INFO.buildNumber} | ${buildDate} | ${VERSION_INFO.environment}`
}

export const getLatestChangelog = (): ChangelogEntry | undefined => {
  return VERSION_INFO.changelog[0]
}

export const isProduction = (): boolean => {
  return VERSION_INFO.environment === 'production'
}

export const isDevelopment = (): boolean => {
  return VERSION_INFO.environment === 'development'
}

// Feature flags based on version
export const FEATURE_FLAGS = {
  PWA_ENABLED: true,
  MULTILINGUAL: true,
  WHATSAPP_INTEGRATION: true,
  ADVANCED_SEO: true,
  SCROLL_NAVBAR: true,
  THEME_SYSTEM: true,
  OFFLINE_SUPPORT: true,
  VERSION_DISPLAY: true
} as const

export type FeatureFlag = keyof typeof FEATURE_FLAGS
