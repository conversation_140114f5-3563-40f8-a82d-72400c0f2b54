// Service Worker - Version 2.1.2 - 1749226277562
// This file is auto-generated to force PWA updates

const CACHE_NAME = 'hlenergy-v2-1-2'
const CACHE_VERSION = '1749226277562'

self.addEventListener('install', (event) => {
  console.log('Service Worker installing - Version 2.1.2')
  self.skipWaiting()
})

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating - Version 2.1.2')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// This timestamp forces the browser to recognize this as a new version
// Timestamp: 1749226277562
