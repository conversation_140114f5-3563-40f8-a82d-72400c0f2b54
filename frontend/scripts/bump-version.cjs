#!/usr/bin/env node

/**
 * HLenergy Version Bump Script
 * 
 * Usage:
 *   npm run version:patch    # 1.0.0 -> 1.0.1
 *   npm run version:minor    # 1.0.0 -> 1.1.0
 *   npm run version:major    # 1.0.0 -> 2.0.0
 *   npm run version:custom 2.5.0  # Set specific version
 * 
 * Features:
 * - Semantic versioning (major.minor.patch)
 * - Interactive changelog entry
 * - Git tag creation
 * - Package.json updates
 * - Version config file updates
 * - Validation and rollback on errors
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const readline = require('readline')

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// Utility functions
const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

const error = (message) => {
  log(`❌ Error: ${message}`, 'red')
  process.exit(1)
}

const success = (message) => {
  log(`✅ ${message}`, 'green')
}

const info = (message) => {
  log(`ℹ️  ${message}`, 'blue')
}

const warning = (message) => {
  log(`⚠️  ${message}`, 'yellow')
}

// File paths
const packageJsonPath = path.join(__dirname, '../package.json')
const versionConfigPath = path.join(__dirname, '../src/config/version.ts')

// Read current version from package.json
const getCurrentVersion = () => {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    return packageJson.version || '1.0.0'
  } catch (err) {
    error(`Failed to read package.json: ${err.message}`)
  }
}

// Parse semantic version
const parseVersion = (version) => {
  const match = version.match(/^(\d+)\.(\d+)\.(\d+)$/)
  if (!match) {
    error(`Invalid version format: ${version}. Expected format: major.minor.patch`)
  }
  return {
    major: parseInt(match[1]),
    minor: parseInt(match[2]),
    patch: parseInt(match[3])
  }
}

// Generate new version based on bump type
const bumpVersion = (currentVersion, bumpType, customVersion = null) => {
  if (bumpType === 'custom') {
    if (!customVersion) {
      error('Custom version not provided')
    }
    parseVersion(customVersion) // Validate format
    return customVersion
  }

  const { major, minor, patch } = parseVersion(currentVersion)
  
  switch (bumpType) {
    case 'patch':
      return `${major}.${minor}.${patch + 1}`
    case 'minor':
      return `${major}.${minor + 1}.0`
    case 'major':
      return `${major + 1}.0.0`
    default:
      error(`Invalid bump type: ${bumpType}. Use: patch, minor, major, or custom`)
  }
}

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Promisify readline question
const question = (query) => {
  return new Promise((resolve) => {
    rl.question(query, resolve)
  })
}

// Get changelog entry from user
const getChangelogEntry = async (version) => {
  log(`\n📝 Creating changelog entry for version ${version}`, 'cyan')
  log('Enter changes for each category (press Enter for empty, type "done" when finished):\n', 'yellow')

  const changes = {
    added: [],
    changed: [],
    fixed: [],
    removed: []
  }

  const categories = [
    { key: 'added', label: '✨ Added (new features)', color: 'green' },
    { key: 'changed', label: '🔄 Changed (modifications)', color: 'blue' },
    { key: 'fixed', label: '🐛 Fixed (bug fixes)', color: 'yellow' },
    { key: 'removed', label: '🗑️  Removed (deprecated features)', color: 'red' }
  ]

  for (const category of categories) {
    log(`\n${category.label}:`, category.color)
    
    while (true) {
      const entry = await question('  - ')
      
      if (entry.toLowerCase() === 'done' || entry === '') {
        break
      }
      
      changes[category.key].push(entry)
    }
  }

  return changes
}

// Update package.json version
const updatePackageJson = (newVersion) => {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    packageJson.version = newVersion
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n')
    success(`Updated package.json to version ${newVersion}`)
  } catch (err) {
    error(`Failed to update package.json: ${err.message}`)
  }
}

// Update version config file
const updateVersionConfig = (newVersion, changes) => {
  try {
    let content = fs.readFileSync(versionConfigPath, 'utf8')

    // Update version number
    content = content.replace(
      /version: '[^']*'/,
      `version: '${newVersion}'`
    )

    // Create new changelog entry
    const newEntry = {
      version: newVersion,
      date: new Date().toISOString().split('T')[0],
      changes: changes
    }

    // Find the changelog array and insert new entry at the beginning
    const changelogMatch = content.match(/(changelog: \[)([\s\S]*?)(\n  \])/m)
    if (changelogMatch) {
      const indent = '    '
      const entryString = JSON.stringify(newEntry, null, 2)
        .split('\n')
        .map((line, index) => index === 0 ? `${indent}${line}` : `${indent}${line}`)
        .join('\n')

      const existingEntries = changelogMatch[2].trim() ? `,${changelogMatch[2]}` : ''
      const newChangelog = `${changelogMatch[1]}\n${entryString}${existingEntries}${changelogMatch[3]}`

      content = content.replace(changelogMatch[0], newChangelog)
    }

    fs.writeFileSync(versionConfigPath, content)
    success(`Updated version config to ${newVersion}`)
  } catch (err) {
    error(`Failed to update version config: ${err.message}`)
  }
}

// Update PWA manifest in vite.config.ts
const updatePWAManifest = (newVersion) => {
  try {
    const viteConfigPath = path.join(__dirname, '..', 'vite.config.ts')

    if (!fs.existsSync(viteConfigPath)) {
      warning('vite.config.ts not found, skipping PWA manifest update')
      return
    }

    let content = fs.readFileSync(viteConfigPath, 'utf8')

    // Update manifest version if it exists (more flexible regex)
    const manifestVersionRegex = /(version:\s*process\.env\.npm_package_version\s*\|\|\s*')[^']*(')/
    if (manifestVersionRegex.test(content)) {
      content = content.replace(manifestVersionRegex, `$1${newVersion}$2`)
      fs.writeFileSync(viteConfigPath, content)
      success(`Updated PWA manifest fallback version to ${newVersion}`)
    } else {
      // Try to find the version field in manifest
      const versionFieldRegex = /(version:\s*')[^']*(')/
      if (versionFieldRegex.test(content)) {
        content = content.replace(versionFieldRegex, `$1${newVersion}$2`)
        fs.writeFileSync(viteConfigPath, content)
        success(`Updated PWA manifest version to ${newVersion}`)
      } else {
        // Add version to manifest if it doesn't exist
        const manifestRegex = /(manifest:\s*{[\s\S]*?)(name:)/
        if (manifestRegex.test(content)) {
          content = content.replace(manifestRegex, `$1version: '${newVersion}',\n        $2`)
          fs.writeFileSync(viteConfigPath, content)
          success(`Added PWA manifest version ${newVersion}`)
        } else {
          warning('Could not find PWA manifest in vite.config.ts')
        }
      }
    }
  } catch (err) {
    warning(`Failed to update PWA manifest: ${err.message}`)
  }
}

// Force PWA update by updating service worker
const forcePWAUpdate = (newVersion) => {
  try {
    const swPath = path.join(__dirname, '..', 'public', 'sw.js')
    const timestamp = Date.now()

    // Create or update a simple service worker file to force update
    const swContent = `// Service Worker - Version ${newVersion} - ${timestamp}
// This file is auto-generated to force PWA updates

const CACHE_NAME = 'hlenergy-v${newVersion.replace(/\./g, '-')}'
const CACHE_VERSION = '${timestamp}'

self.addEventListener('install', (event) => {
  console.log('Service Worker installing - Version ${newVersion}')
  self.skipWaiting()
})

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating - Version ${newVersion}')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// This timestamp forces the browser to recognize this as a new version
// Timestamp: ${timestamp}
`

    fs.writeFileSync(swPath, swContent)
    success(`Updated service worker for PWA version ${newVersion}`)

    // Also update the build timestamp in vite config
    const viteConfigPath = path.join(__dirname, '..', 'vite.config.ts')
    if (fs.existsSync(viteConfigPath)) {
      let viteContent = fs.readFileSync(viteConfigPath, 'utf8')

      // Update build timestamp to force PWA update
      const buildDateRegex = /__BUILD_DATE__:\s*JSON\.stringify\([^)]+\)/
      if (buildDateRegex.test(viteContent)) {
        viteContent = viteContent.replace(
          buildDateRegex,
          `__BUILD_DATE__: JSON.stringify('${new Date().toISOString()}')`
        )
        fs.writeFileSync(viteConfigPath, viteContent)
        success('Updated build timestamp to force PWA update')
      }
    }

  } catch (err) {
    warning(`Failed to force PWA update: ${err.message}`)
  }
}

// Git operations
const gitOperations = (version) => {
  try {
    // Check if git repo exists and is clean
    execSync('git status --porcelain', { stdio: 'pipe' })
    const status = execSync('git status --porcelain', { encoding: 'utf8' })
    
    if (status.trim()) {
      warning('Working directory has uncommitted changes')
      log('Uncommitted files:')
      log(status)
    }

    // Add files
    execSync('git add package.json src/config/version.ts')
    success('Added files to git staging')

    // Commit
    execSync(`git commit -m "chore: bump version to ${version}"`)
    success(`Created commit for version ${version}`)

    // Create tag
    execSync(`git tag -a v${version} -m "Release version ${version}"`)
    success(`Created git tag v${version}`)

    info('To push changes and tags, run:')
    log(`  git push origin main`, 'cyan')
    log(`  git push origin v${version}`, 'cyan')

  } catch (err) {
    error(`Git operations failed: ${err.message}`)
  }
}

// Main function
const main = async () => {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2)
    const bumpType = args[0]
    const customVersion = args[1]

    if (!bumpType || !['patch', 'minor', 'major', 'custom'].includes(bumpType)) {
      error('Usage: node bump-version.js <patch|minor|major|custom> [version]')
    }

    // Get current version
    const currentVersion = getCurrentVersion()
    const newVersion = bumpVersion(currentVersion, bumpType, customVersion)

    // Display version change
    log('\n🚀 HLenergy Version Bump', 'bright')
    log('═'.repeat(50), 'cyan')
    log(`Current version: ${currentVersion}`, 'yellow')
    log(`New version:     ${newVersion}`, 'green')
    log('═'.repeat(50), 'cyan')

    // Confirm with user
    const confirm = await question(`\nProceed with version bump to ${newVersion}? (y/N): `)
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      log('Version bump cancelled', 'yellow')
      rl.close()
      return
    }

    // Get changelog entry
    const changes = await getChangelogEntry(newVersion)
    
    // Show summary
    log('\n📋 Summary:', 'bright')
    log(`Version: ${currentVersion} → ${newVersion}`, 'cyan')
    
    const totalChanges = Object.values(changes).reduce((sum, arr) => sum + arr.length, 0)
    if (totalChanges > 0) {
      log(`Changelog entries: ${totalChanges}`, 'cyan')
      Object.entries(changes).forEach(([key, items]) => {
        if (items.length > 0) {
          log(`  ${key}: ${items.length} items`, 'blue')
        }
      })
    } else {
      warning('No changelog entries added')
    }

    const finalConfirm = await question('\nProceed with file updates and git operations? (y/N): ')
    if (finalConfirm.toLowerCase() !== 'y' && finalConfirm.toLowerCase() !== 'yes') {
      log('Version bump cancelled', 'yellow')
      rl.close()
      return
    }

    // Update files
    info('\nUpdating files...')
    updatePackageJson(newVersion)
    updateVersionConfig(newVersion, changes)

    // PWA-specific updates
    info('\nUpdating PWA configuration...')
    updatePWAManifest(newVersion)
    forcePWAUpdate(newVersion)

    // Git operations
    info('\nPerforming git operations...')
    gitOperations(newVersion)

    // Success message
    log('\n🎉 Version bump completed successfully!', 'bright')
    log(`HLenergy is now at version ${newVersion}`, 'green')
    
    rl.close()

  } catch (err) {
    error(`Version bump failed: ${err.message}`)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\n\nVersion bump cancelled by user', 'yellow')
  rl.close()
  process.exit(0)
})

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  getCurrentVersion,
  bumpVersion,
  parseVersion
}
