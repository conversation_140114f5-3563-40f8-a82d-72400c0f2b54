#!/usr/bin/env node

/**
 * HLenergy Version Check Script
 * 
 * Checks version consistency across different files and provides
 * version information for debugging and deployment purposes.
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
}

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// File paths
const packageJsonPath = path.join(__dirname, '../package.json')
const versionConfigPath = path.join(__dirname, '../src/config/version.ts')

// Get version from package.json
const getPackageVersion = () => {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    return packageJson.version || 'unknown'
  } catch (err) {
    return `error: ${err.message}`
  }
}

// Get version from version config
const getConfigVersion = () => {
  try {
    const content = fs.readFileSync(versionConfigPath, 'utf8')
    const match = content.match(/version: '([^']*)'/)
    return match ? match[1] : 'not found'
  } catch (err) {
    return `error: ${err.message}`
  }
}

// Get git information
const getGitInfo = () => {
  try {
    const commit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim()
    const shortCommit = commit.substring(0, 7)
    const branch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim()
    const isDirty = execSync('git status --porcelain', { encoding: 'utf8' }).trim() !== ''
    
    return {
      commit,
      shortCommit,
      branch,
      isDirty
    }
  } catch (err) {
    return {
      commit: 'unknown',
      shortCommit: 'unknown',
      branch: 'unknown',
      isDirty: false,
      error: err.message
    }
  }
}

// Get build information
const getBuildInfo = () => {
  const buildDate = new Date().toISOString()
  const buildNumber = process.env.BUILD_NUMBER || Date.now().toString()
  const nodeVersion = process.version
  const platform = process.platform
  
  return {
    buildDate,
    buildNumber,
    nodeVersion,
    platform
  }
}

// Check version consistency
const checkVersionConsistency = (packageVersion, configVersion) => {
  if (packageVersion === configVersion) {
    return { consistent: true, message: 'Versions are consistent' }
  } else {
    return { 
      consistent: false, 
      message: `Version mismatch: package.json (${packageVersion}) vs config (${configVersion})` 
    }
  }
}

// Main function
const main = () => {
  log('\n🔍 HLenergy Version Check', 'bright')
  log('═'.repeat(60), 'cyan')

  // Get versions
  const packageVersion = getPackageVersion()
  const configVersion = getConfigVersion()
  const gitInfo = getGitInfo()
  const buildInfo = getBuildInfo()

  // Display version information
  log('\n📦 Version Information:', 'blue')
  log(`  Package.json:    ${packageVersion}`, packageVersion.includes('error') ? 'red' : 'green')
  log(`  Version Config:  ${configVersion}`, configVersion.includes('error') ? 'red' : 'green')

  // Check consistency
  const consistency = checkVersionConsistency(packageVersion, configVersion)
  log(`  Consistency:     ${consistency.message}`, consistency.consistent ? 'green' : 'red')

  // Git information
  log('\n🌿 Git Information:', 'blue')
  if (gitInfo.error) {
    log(`  Error: ${gitInfo.error}`, 'red')
  } else {
    log(`  Branch:          ${gitInfo.branch}`, 'cyan')
    log(`  Commit:          ${gitInfo.shortCommit} (${gitInfo.commit})`, 'cyan')
    log(`  Working Dir:     ${gitInfo.isDirty ? 'dirty' : 'clean'}`, gitInfo.isDirty ? 'yellow' : 'green')
  }

  // Build information
  log('\n🔨 Build Information:', 'blue')
  log(`  Build Date:      ${buildInfo.buildDate}`, 'cyan')
  log(`  Build Number:    ${buildInfo.buildNumber}`, 'cyan')
  log(`  Node Version:    ${buildInfo.nodeVersion}`, 'cyan')
  log(`  Platform:        ${buildInfo.platform}`, 'cyan')

  // Environment
  log('\n🌍 Environment:', 'blue')
  log(`  NODE_ENV:        ${process.env.NODE_ENV || 'not set'}`, 'cyan')
  log(`  BUILD_NUMBER:    ${process.env.BUILD_NUMBER || 'not set'}`, 'cyan')
  log(`  CI:              ${process.env.CI || 'not set'}`, 'cyan')

  // Summary
  log('\n📋 Summary:', 'blue')
  if (consistency.consistent && !packageVersion.includes('error') && !configVersion.includes('error')) {
    log(`  Status:          Ready for deployment ✅`, 'green')
    log(`  Version:         ${packageVersion}`, 'green')
  } else {
    log(`  Status:          Issues detected ❌`, 'red')
    if (!consistency.consistent) {
      log(`  Action:          Run version bump script to fix consistency`, 'yellow')
    }
  }

  log('═'.repeat(60), 'cyan')
  log('')

  // Exit with appropriate code
  process.exit(consistency.consistent && !packageVersion.includes('error') && !configVersion.includes('error') ? 0 : 1)
}

// Handle command line arguments
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  log('\nHLenergy Version Check Script', 'bright')
  log('\nUsage:')
  log('  npm run version:check     # Check version consistency and information')
  log('  node scripts/check-version.js')
  log('\nOptions:')
  log('  --help, -h               Show this help message')
  log('')
  process.exit(0)
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  getPackageVersion,
  getConfigVersion,
  getGitInfo,
  getBuildInfo,
  checkVersionConsistency
}
