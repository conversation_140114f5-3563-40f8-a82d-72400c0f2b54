# HLenergy Version Management System

A comprehensive version management system for the HLenergy webapp that handles semantic versioning, changelog generation, and automated git operations.

## 🚀 Quick Start

```bash
# Bump patch version (1.0.0 → 1.0.1)
npm run version:patch

# Bump minor version (1.0.0 → 1.1.0)
npm run version:minor

# Bump major version (1.0.0 → 2.0.0)
npm run version:major

# Set custom version
npm run version:custom 2.5.0

# Check current version
npm run version:current

# Check version consistency
npm run version:check
```

## 📋 Available Scripts

| Script | Description | Example |
|--------|-------------|---------|
| `npm run version:patch` | Increment patch version | `1.0.0` → `1.0.1` |
| `npm run version:minor` | Increment minor version | `1.0.0` → `1.1.0` |
| `npm run version:major` | Increment major version | `1.0.0` → `2.0.0` |
| `npm run version:custom <version>` | Set specific version | `npm run version:custom 2.5.0` |
| `npm run version:current` | Display current version | Shows current version |
| `npm run version:check` | Check version consistency | Validates all version files |

## 🔧 Features

### ✨ Interactive Changelog Generation
- Prompts for changes in each category (Added, Changed, Fixed, Removed)
- Automatically formats and inserts into version config
- Maintains complete version history

### 🎯 Semantic Versioning
- **Patch** (`x.x.X`): Bug fixes and small improvements
- **Minor** (`x.X.x`): New features, backward compatible
- **Major** (`X.x.x`): Breaking changes, major updates

### 🔄 Automated Git Operations
- Creates commit with version bump
- Creates git tag for the new version
- Provides push commands for remote sync

### 📁 File Updates
- Updates `package.json` version
- Updates `src/config/version.ts` with new version and changelog
- Maintains build information and metadata

### ✅ Validation & Safety
- Validates semantic version format
- Checks git repository status
- Confirms actions with user prompts
- Provides rollback information on errors

## 📖 Usage Examples

### Basic Version Bump

```bash
# Patch version bump
npm run version:patch
```

**Interactive Process:**
1. Shows current → new version
2. Prompts for confirmation
3. Requests changelog entries by category
4. Updates files and creates git commit/tag

### Custom Version

```bash
# Set specific version
npm run version:custom 3.0.0-beta.1
```

### Version Check

```bash
# Check version consistency and information
npm run version:check
```

**Output includes:**
- Version consistency between files
- Git information (branch, commit, status)
- Build information
- Environment variables

## 📂 File Structure

```
frontend/
├── scripts/
│   ├── bump-version.js      # Main version bump script
│   ├── check-version.js     # Version validation script
│   └── README.md           # This documentation
├── src/config/
│   └── version.ts          # Version configuration and changelog
└── package.json            # NPM package version
```

## 🔍 Version Configuration

The version system uses `src/config/version.ts` as the central configuration:

```typescript
export const VERSION_INFO: VersionInfo = {
  version: '2.1.0',
  buildDate: __BUILD_DATE__,
  buildNumber: __BUILD_NUMBER__,
  gitCommit: __GIT_COMMIT__,
  environment: import.meta.env.MODE,
  features: [
    'Multilingual Support (EN, ES, PT)',
    'Progressive Web App (PWA)',
    // ... more features
  ],
  changelog: [
    {
      version: '2.1.0',
      date: '2024-01-15',
      changes: {
        added: ['New feature 1', 'New feature 2'],
        changed: ['Modified feature 1'],
        fixed: ['Bug fix 1', 'Bug fix 2'],
        removed: ['Deprecated feature 1']
      }
    }
    // ... previous versions
  ]
}
```

## 🌍 Environment Variables

The system supports build-time environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `BUILD_NUMBER` | Build identifier | Current timestamp |
| `NODE_ENV` | Environment mode | `development` |
| `CI` | Continuous integration flag | `false` |

## 🔄 Git Integration

### Automatic Operations
- `git add package.json src/config/version.ts`
- `git commit -m "chore: bump version to X.X.X"`
- `git tag -a vX.X.X -m "Release version X.X.X"`

### Manual Push Required
After version bump, manually push:
```bash
git push origin main
git push origin vX.X.X
```

## 🛠️ Development Workflow

### 1. Feature Development
```bash
# Work on features
git checkout -b feature/new-feature
# ... develop feature ...
git commit -m "feat: add new feature"
```

### 2. Version Bump
```bash
# Before release, bump version
npm run version:minor
# Follow interactive prompts
```

### 3. Release
```bash
# Push changes and tags
git push origin main
git push origin v2.1.0
```

## 🚨 Troubleshooting

### Version Mismatch
```bash
npm run version:check
# Shows inconsistencies between files
# Run appropriate version bump to fix
```

### Git Issues
```bash
# Check git status
git status
# Ensure clean working directory before version bump
```

### Build Information
```bash
# Check build variables
npm run version:check
# Shows all environment and build information
```

## 📝 Changelog Format

The system uses a structured changelog format:

- **Added**: New features
- **Changed**: Modifications to existing features
- **Fixed**: Bug fixes
- **Removed**: Deprecated or removed features

Each entry includes:
- Version number
- Release date
- Categorized changes
- Automatic integration with version display component

## 🎯 Best Practices

1. **Always run version check** before deployment
2. **Use semantic versioning** appropriately
3. **Write clear changelog entries** for each change
4. **Test thoroughly** before version bumps
5. **Keep working directory clean** before version operations
6. **Push tags immediately** after version bump

## 🔗 Integration

The version system integrates with:
- **VersionInfo Component**: Displays version in UI
- **Build System**: Injects build-time variables
- **PWA Manifest**: Updates app version
- **SEO System**: Includes version in metadata

This comprehensive system ensures consistent versioning across the entire HLenergy application while providing transparency and traceability for all releases.
