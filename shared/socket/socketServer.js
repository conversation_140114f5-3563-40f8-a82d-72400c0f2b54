const { Server } = require('socket.io')
const jwt = require('jsonwebtoken')

class SocketServer {
  constructor(httpServer) {
    this.io = new Server(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.connectedUsers = new Map() // userId -> socket.id
    this.userSockets = new Map()   // socket.id -> user info
    this.rooms = new Map()         // room -> Set of socket.ids
    this.temporaryTokens = new Map() // token -> user data
    this.temporaryUserCounter = 0

    this.setupMiddleware()
    this.setupEventHandlers()
  }

  setupMiddleware() {
    // Enhanced authentication middleware with temporary token support
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')

        if (!token) {
          // Generate temporary token for non-authenticated users
          const tempToken = this.generateTemporaryToken()
          const tempUser = this.createTemporaryUser(tempToken)

          socket.userId = tempUser.id
          socket.userRole = tempUser.role
          socket.userData = tempUser
          socket.isTemporary = true

          console.log(`Temporary user connected: ${tempUser.name} (${socket.id})`)
          return next()
        }

        // Check if it's a temporary token
        if (token.startsWith('temp_')) {
          const tempUser = this.getTemporaryUserFromToken(token)
          if (tempUser) {
            socket.userId = tempUser.id
            socket.userRole = tempUser.role
            socket.userData = tempUser
            socket.isTemporary = true

            console.log(`Temporary user reconnected: ${tempUser.name} (${socket.id})`)
            return next()
          }
        }

        // Verify JWT token for authenticated users
        const decoded = jwt.verify(token, process.env.JWT_SECRET)
        const user = await this.getUserById(decoded.userId)

        if (!user) {
          console.warn(`User not found for token: ${decoded.userId}`)
          // Fall back to temporary user instead of rejecting
          const tempToken = this.generateTemporaryToken()
          const tempUser = this.createTemporaryUser(tempToken)

          socket.userId = tempUser.id
          socket.userRole = tempUser.role
          socket.userData = tempUser
          socket.isTemporary = true

          return next()
        }

        // Authenticated user
        socket.userId = user.id
        socket.userRole = user.role
        socket.userData = {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
          is_active: user.is_active,
          email_verified: user.email_verified
        }
        socket.isTemporary = false

        next()
      } catch (error) {
        console.error('Socket authentication error:', error)

        // Instead of rejecting, provide temporary access
        const tempToken = this.generateTemporaryToken()
        const tempUser = this.createTemporaryUser(tempToken)

        socket.userId = tempUser.id
        socket.userRole = tempUser.role
        socket.userData = tempUser
        socket.isTemporary = true

        console.log(`Authentication failed, providing temporary access: ${tempUser.name} (${socket.id})`)
        next()
      }
    })
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const userType = socket.isTemporary ? 'temporary' : 'authenticated'
      console.log(`${userType} user ${socket.userData.name || socket.userData.email} connected (${socket.id})`)

      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id)
      this.userSockets.set(socket.id, socket.userData)

      // Join user to their personal room
      socket.join(`user:${socket.userId}`)

      // Join role-based rooms
      socket.join(`role:${socket.userRole}`)

      // Temporary users join guest room
      if (socket.isTemporary) {
        socket.join('guests')
      }

      // Admin users join admin room
      if (socket.userRole === 'admin') {
        socket.join('admin')
      }

      // Send connection confirmation with user type
      socket.emit('connected', {
        message: 'Connected to HLenergy real-time server',
        userId: socket.userId,
        userType: userType,
        isTemporary: socket.isTemporary,
        userData: {
          name: socket.userData.name,
          role: socket.userData.role,
          isTemporary: socket.isTemporary
        },
        timestamp: new Date().toISOString()
      })

      // Broadcast user online status to admins
      socket.to('admin').emit('user:online', {
        userId: socket.userId,
        userData: socket.userData,
        timestamp: new Date().toISOString()
      })

      // Handle real-time analytics tracking
      socket.on('analytics:track', (data) => {
        this.handleAnalyticsEvent(socket, data)
      })

      // Handle customer communication events
      socket.on('communication:send', (data) => {
        this.handleCommunication(socket, data)
      })

      // Handle project updates
      socket.on('project:update', (data) => {
        this.handleProjectUpdate(socket, data)
      })

      // Handle notification events
      socket.on('notification:read', (data) => {
        this.handleNotificationRead(socket, data)
      })

      // Handle typing indicators
      socket.on('typing:start', (data) => {
        this.handleTypingStart(socket, data)
      })

      socket.on('typing:stop', (data) => {
        this.handleTypingStop(socket, data)
      })

      // Handle room joining/leaving
      socket.on('room:join', (roomId) => {
        this.joinRoom(socket, roomId)
      })

      socket.on('room:leave', (roomId) => {
        this.leaveRoom(socket, roomId)
      })

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        console.log(`User ${socket.userData.email} disconnected: ${reason}`)
        
        // Remove from tracking
        this.connectedUsers.delete(socket.userId)
        this.userSockets.delete(socket.id)

        // Broadcast user offline status to admins
        socket.to('admin').emit('user:offline', {
          userId: socket.userId,
          userData: socket.userData,
          reason,
          timestamp: new Date().toISOString()
        })

        // Clean up rooms
        this.cleanupUserRooms(socket)
      })
    })
  }

  // Analytics event handling
  handleAnalyticsEvent(socket, data) {
    const event = {
      ...data,
      userId: socket.userId,
      timestamp: new Date().toISOString(),
      sessionId: socket.id
    }

    // Broadcast to admin dashboard for real-time analytics
    this.io.to('admin').emit('analytics:event', event)

    // Store in database (you can implement this)
    this.storeAnalyticsEvent(event)
  }

  // Communication handling
  handleCommunication(socket, data) {
    const communication = {
      ...data,
      senderId: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Send to specific user if specified
    if (data.recipientId) {
      this.sendToUser(data.recipientId, 'communication:received', communication)
    }

    // Broadcast to admins
    this.io.to('admin').emit('communication:new', communication)

    // Store in database
    this.storeCommunication(communication)
  }

  // Project update handling
  handleProjectUpdate(socket, data) {
    const update = {
      ...data,
      updatedBy: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Notify project stakeholders
    if (data.projectId) {
      this.io.to(`project:${data.projectId}`).emit('project:updated', update)
    }

    // Notify admins
    this.io.to('admin').emit('project:update', update)
  }

  // Notification read handling
  handleNotificationRead(socket, data) {
    // Mark notification as read in database
    this.markNotificationAsRead(data.notificationId, socket.userId)

    // Update notification count for user
    this.sendNotificationCount(socket.userId)
  }

  // Typing indicators
  handleTypingStart(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_started', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  handleTypingStop(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_stopped', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  // Room management
  joinRoom(socket, roomId) {
    socket.join(roomId)
    
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set())
    }
    this.rooms.get(roomId).add(socket.id)

    socket.emit('room:joined', { roomId })
    socket.to(roomId).emit('room:user_joined', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  leaveRoom(socket, roomId) {
    socket.leave(roomId)
    
    if (this.rooms.has(roomId)) {
      this.rooms.get(roomId).delete(socket.id)
      if (this.rooms.get(roomId).size === 0) {
        this.rooms.delete(roomId)
      }
    }

    socket.emit('room:left', { roomId })
    socket.to(roomId).emit('room:user_left', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  cleanupUserRooms(socket) {
    for (const [roomId, socketIds] of this.rooms.entries()) {
      if (socketIds.has(socket.id)) {
        socketIds.delete(socket.id)
        if (socketIds.size === 0) {
          this.rooms.delete(roomId)
        }
      }
    }
  }

  // Utility methods
  sendToUser(userId, event, data) {
    const socketId = this.connectedUsers.get(userId)
    if (socketId) {
      this.io.to(socketId).emit(event, data)
      return true
    }
    return false
  }

  sendToRole(role, event, data) {
    this.io.to(`role:${role}`).emit(event, data)
  }

  broadcastToAll(event, data) {
    this.io.emit(event, data)
  }

  getConnectedUsers() {
    return Array.from(this.userSockets.values())
  }

  isUserOnline(userId) {
    return this.connectedUsers.has(userId)
  }

  // Temporary token management methods
  generateTemporaryToken() {
    const crypto = require('crypto')
    return 'temp_' + crypto.randomBytes(16).toString('hex') + '_' + Date.now()
  }

  createTemporaryUser(token) {
    this.temporaryUserCounter++
    const tempUser = {
      id: `temp_${this.temporaryUserCounter}`,
      email: `temp_user_${this.temporaryUserCounter}@temporary.local`,
      role: 'guest',
      name: `Guest User ${this.temporaryUserCounter}`,
      is_active: true,
      email_verified: false,
      isTemporary: true,
      token: token,
      createdAt: new Date().toISOString()
    }

    // Store temporary token with expiration (1 hour)
    this.temporaryTokens.set(token, {
      user: tempUser,
      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
    })

    return tempUser
  }

  getTemporaryUserFromToken(token) {
    const tokenData = this.temporaryTokens.get(token)
    if (!tokenData) return null

    // Check if token has expired
    if (Date.now() > tokenData.expiresAt) {
      this.temporaryTokens.delete(token)
      return null
    }

    return tokenData.user
  }

  cleanupExpiredTemporaryTokens() {
    const now = Date.now()
    for (const [token, tokenData] of this.temporaryTokens.entries()) {
      if (now > tokenData.expiresAt) {
        this.temporaryTokens.delete(token)
      }
    }
  }

  getTemporaryTokenStats() {
    this.cleanupExpiredTemporaryTokens()
    return {
      activeTokens: this.temporaryTokens.size,
      totalGenerated: this.temporaryUserCounter
    }
  }

  // Database operations
  async getUserById(userId) {
    try {
      // Import User model dynamically to avoid circular dependencies
      const { User } = require('../models');

      if (!User) {
        console.error('User model not available, falling back to mock data');
        return this.getMockUser(userId);
      }

      const user = await User.findByPk(userId, {
        attributes: ['id', 'name', 'email', 'role', 'is_active', 'email_verified']
      });

      if (!user) {
        console.warn(`User with ID ${userId} not found`);
        return null;
      }

      if (!user.is_active) {
        console.warn(`User with ID ${userId} is inactive`);
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        is_active: user.is_active,
        email_verified: user.email_verified
      };
    } catch (error) {
      console.error('Error fetching user from database:', error);
      // Fallback to mock data in case of database issues
      return this.getMockUser(userId);
    }
  }

  getMockUser(userId) {
    // Fallback mock user for development/testing
    return {
      id: userId,
      email: '<EMAIL>',
      role: 'user',
      name: 'User Name',
      is_active: true,
      email_verified: true
    };
  }

  async storeAnalyticsEvent(event) {
    try {
      // Import models dynamically
      const { Log } = require('../models');

      if (Log) {
        await Log.create({
          level: 'info',
          message: 'Socket Analytics Event',
          meta: {
            type: 'socket_analytics',
            event: event,
            timestamp: new Date().toISOString()
          },
          source: 'socket_server'
        });
      } else {
        // Fallback to console logging
        console.log('Analytics event (DB unavailable):', event);
      }
    } catch (error) {
      console.error('Error storing analytics event:', error);
      console.log('Analytics event (fallback):', event);
    }
  }

  async storeCommunication(communication) {
    try {
      // Import models dynamically
      const { Communication } = require('../models');

      if (Communication) {
        await Communication.create({
          type: communication.type || 'socket_message',
          from_user_id: communication.fromUserId,
          to_user_id: communication.toUserId,
          content: communication.content,
          metadata: communication.metadata || {},
          status: 'sent',
          sent_at: new Date()
        });
      } else {
        // Fallback to console logging
        console.log('Communication (DB unavailable):', communication);
      }
    } catch (error) {
      console.error('Error storing communication:', error);
      console.log('Communication (fallback):', communication);
    }
  }

  async markNotificationAsRead(notificationId, userId) {
    // Implement notification read marking
    console.log('Notification read:', notificationId, userId)
  }

  async sendNotificationCount(userId) {
    // Get unread notification count and send to user
    const count = 0 // Implement count logic
    this.sendToUser(userId, 'notifications:count', { count })
  }
}

module.exports = SocketServer
