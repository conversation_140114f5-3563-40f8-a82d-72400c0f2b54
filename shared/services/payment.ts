interface PaymentMethod {
  id: string
  type: 'card' | 'bank_transfer' | 'paypal' | 'crypto'
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
  isDefault: boolean
}

interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled'
  clientSecret: string
  description?: string
  metadata?: Record<string, string>
}

interface ServicePackage {
  id: string
  name: string
  description: string
  price: number
  currency: string
  features: string[]
  duration?: string
  type: 'one_time' | 'subscription'
  popular?: boolean
}

interface PaymentResult {
  success: boolean
  paymentIntent?: PaymentIntent
  error?: string
  requiresAction?: boolean
  actionUrl?: string
}

class PaymentService {
  private stripe: any = null
  private isInitialized = false

  // Initialize Stripe
  async initialize(publishableKey: string) {
    if (this.isInitialized) return

    try {
      // Load Stripe.js
      if (!window.Stripe) {
        const script = document.createElement('script')
        script.src = 'https://js.stripe.com/v3/'
        script.async = true
        document.head.appendChild(script)
        
        await new Promise((resolve, reject) => {
          script.onload = resolve
          script.onerror = reject
        })
      }

      this.stripe = window.Stripe(publishableKey)
      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize Stripe:', error)
      throw new Error('Payment system initialization failed')
    }
  }

  // Get available service packages
  async getServicePackages(): Promise<ServicePackage[]> {
    try {
      const response = await fetch('/api/payments/packages')
      if (!response.ok) throw new Error('Failed to fetch service packages')
      return await response.json()
    } catch (error) {
      console.error('Error fetching service packages:', error)
      return this.getDefaultPackages()
    }
  }

  // Create payment intent
  async createPaymentIntent(packageId: string, metadata?: Record<string, string>): Promise<PaymentIntent> {
    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          package_id: packageId,
          metadata
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create payment intent')
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating payment intent:', error)
      throw error
    }
  }

  // Process payment
  async processPayment(paymentIntentId: string, paymentMethodId: string): Promise<PaymentResult> {
    if (!this.isInitialized) {
      throw new Error('Payment service not initialized')
    }

    try {
      const { error, paymentIntent } = await this.stripe.confirmCardPayment(paymentIntentId, {
        payment_method: paymentMethodId
      })

      if (error) {
        return {
          success: false,
          error: error.message
        }
      }

      if (paymentIntent.status === 'requires_action') {
        return {
          success: false,
          requiresAction: true,
          actionUrl: paymentIntent.next_action?.redirect_to_url?.url
        }
      }

      return {
        success: paymentIntent.status === 'succeeded',
        paymentIntent
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      return {
        success: false,
        error: 'Payment processing failed'
      }
    }
  }

  // Create payment element
  createPaymentElement(clientSecret: string, options?: any) {
    if (!this.isInitialized) {
      throw new Error('Payment service not initialized')
    }

    const elements = this.stripe.elements({
      clientSecret,
      appearance: {
        theme: 'stripe',
        variables: {
          colorPrimary: '#02342b',
          colorBackground: '#ffffff',
          colorText: '#1a1a1a',
          colorDanger: '#df1b41',
          fontFamily: 'Inter, system-ui, sans-serif',
          spacingUnit: '4px',
          borderRadius: '8px'
        }
      },
      ...options
    })

    return elements.create('payment', {
      layout: 'tabs'
    })
  }

  // Get saved payment methods
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await fetch('/api/payments/methods', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })

      if (!response.ok) throw new Error('Failed to fetch payment methods')
      return await response.json()
    } catch (error) {
      console.error('Error fetching payment methods:', error)
      return []
    }
  }

  // Save payment method
  async savePaymentMethod(paymentMethodId: string, setAsDefault: boolean = false): Promise<boolean> {
    try {
      const response = await fetch('/api/payments/methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          payment_method_id: paymentMethodId,
          set_as_default: setAsDefault
        })
      })

      return response.ok
    } catch (error) {
      console.error('Error saving payment method:', error)
      return false
    }
  }

  // Delete payment method
  async deletePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/payments/methods/${paymentMethodId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })

      return response.ok
    } catch (error) {
      console.error('Error deleting payment method:', error)
      return false
    }
  }

  // Get payment history
  async getPaymentHistory(limit: number = 10, offset: number = 0) {
    try {
      const response = await fetch(`/api/payments/history?limit=${limit}&offset=${offset}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })

      if (!response.ok) throw new Error('Failed to fetch payment history')
      return await response.json()
    } catch (error) {
      console.error('Error fetching payment history:', error)
      return { payments: [], total: 0 }
    }
  }

  // Create subscription
  async createSubscription(priceId: string, paymentMethodId: string) {
    try {
      const response = await fetch('/api/payments/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          price_id: priceId,
          payment_method_id: paymentMethodId
        })
      })

      if (!response.ok) throw new Error('Failed to create subscription')
      return await response.json()
    } catch (error) {
      console.error('Error creating subscription:', error)
      throw error
    }
  }

  // Cancel subscription
  async cancelSubscription(subscriptionId: string) {
    try {
      const response = await fetch(`/api/payments/subscriptions/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })

      return response.ok
    } catch (error) {
      console.error('Error canceling subscription:', error)
      return false
    }
  }

  // Get invoice
  async getInvoice(invoiceId: string) {
    try {
      const response = await fetch(`/api/payments/invoices/${invoiceId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })

      if (!response.ok) throw new Error('Failed to fetch invoice')
      return await response.json()
    } catch (error) {
      console.error('Error fetching invoice:', error)
      throw error
    }
  }

  // Default service packages (fallback)
  private getDefaultPackages(): ServicePackage[] {
    return [
      {
        id: 'basic_consultation',
        name: 'Basic Energy Consultation',
        description: 'Initial energy assessment and basic recommendations',
        price: 15000, // $150.00 in cents
        currency: 'usd',
        features: [
          'Energy usage analysis',
          'Basic efficiency recommendations',
          'Written report',
          'Email support'
        ],
        duration: '1 hour',
        type: 'one_time'
      },
      {
        id: 'comprehensive_audit',
        name: 'Comprehensive Energy Audit',
        description: 'Detailed energy audit with implementation plan',
        price: 35000, // $350.00 in cents
        currency: 'usd',
        features: [
          'Complete energy audit',
          'Detailed efficiency analysis',
          'Implementation roadmap',
          'ROI calculations',
          'Priority recommendations',
          'Phone consultation'
        ],
        duration: '2-3 hours',
        type: 'one_time',
        popular: true
      },
      {
        id: 'premium_package',
        name: 'Premium Energy Solutions',
        description: 'Complete energy optimization with ongoing support',
        price: 75000, // $750.00 in cents
        currency: 'usd',
        features: [
          'Everything in Comprehensive Audit',
          'Equipment recommendations',
          'Vendor connections',
          'Project management support',
          '6 months follow-up',
          'Priority support'
        ],
        duration: 'Full day + follow-up',
        type: 'one_time'
      },
      {
        id: 'monthly_monitoring',
        name: 'Monthly Energy Monitoring',
        description: 'Ongoing energy monitoring and optimization',
        price: 9900, // $99.00 in cents
        currency: 'usd',
        features: [
          'Monthly energy reports',
          'Performance tracking',
          'Optimization recommendations',
          'Email alerts',
          'Quarterly consultations'
        ],
        type: 'subscription'
      }
    ]
  }
}

// Create singleton instance
export const paymentService = new PaymentService()

// Export types
export type { PaymentMethod, PaymentIntent, ServicePackage, PaymentResult }
