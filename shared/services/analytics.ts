interface AnalyticsEvent {
  name: string
  parameters?: Record<string, any>
  timestamp?: number
  userId?: string
  sessionId?: string
}

interface UserProperties {
  userId?: string
  email?: string
  role?: string
  registrationDate?: string
  lastLoginDate?: string
  totalSessions?: number
  preferredLanguage?: string
  deviceType?: string
  location?: string
}

interface BusinessMetrics {
  leadGeneration: {
    contactFormSubmissions: number
    emailSignups: number
    phoneCallRequests: number
    consultationBookings: number
  }
  userEngagement: {
    pageViews: number
    sessionDuration: number
    bounceRate: number
    returnVisitors: number
  }
  conversion: {
    signupRate: number
    contactRate: number
    serviceInquiries: number
    quotesRequested: number
  }
}

class AnalyticsService {
  private isInitialized = false
  private sessionId: string
  private userId?: string
  private queue: AnalyticsEvent[] = []
  private businessMetrics: BusinessMetrics
  private socket: any = null

  constructor() {
    this.sessionId = this.generateSessionId()
    this.businessMetrics = this.initializeMetrics()
  }

  // Initialize analytics service
  async initialize(config: { userId?: string; debug?: boolean; socket?: any }) {
    if (this.isInitialized) return

    this.userId = config.userId
    this.socket = config.socket

    // Initialize Google Analytics 4
    await this.initializeGA4()

    // Initialize custom analytics
    this.initializeCustomAnalytics()

    // Initialize real-time analytics
    this.initializeRealTimeAnalytics()

    // Process queued events
    this.processQueue()

    this.isInitialized = true

    if (config.debug) {
      console.log('Analytics initialized', { sessionId: this.sessionId, userId: this.userId })
    }
  }

  // Track page views
  trackPageView(page: string, title?: string) {
    const event: AnalyticsEvent = {
      name: 'page_view',
      parameters: {
        page_title: title || document.title,
        page_location: window.location.href,
        page_path: page
      }
    }

    this.track(event)
    this.updateBusinessMetrics('pageViews', 1)

    // Send real-time page view to Socket.io
    this.sendRealTimeEvent('pageview', {
      page,
      title: title || document.title
    })
  }

  // Track user interactions
  trackEvent(eventName: string, parameters?: Record<string, any>) {
    const event: AnalyticsEvent = {
      name: eventName,
      parameters: {
        ...parameters,
        session_id: this.sessionId,
        user_id: this.userId
      }
    }

    this.track(event)
  }

  // Track business-critical events
  trackBusinessEvent(category: 'lead_generation' | 'user_engagement' | 'conversion', action: string, parameters?: Record<string, any>) {
    const event: AnalyticsEvent = {
      name: `business_${category}`,
      parameters: {
        event_category: category,
        event_action: action,
        ...parameters
      }
    }

    this.track(event)
    this.updateBusinessMetricsFromEvent(category, action)
  }

  // Track contact form submissions
  trackContactFormSubmission(formData: any) {
    this.trackBusinessEvent('lead_generation', 'contact_form_submission', {
      form_type: 'contact',
      has_phone: !!formData.phone,
      has_company: !!formData.company,
      message_length: formData.message?.length || 0
    })
  }

  // Track user registration
  trackUserRegistration(method: string, userProperties?: UserProperties) {
    this.trackBusinessEvent('conversion', 'user_registration', {
      registration_method: method
    })

    if (userProperties) {
      this.setUserProperties(userProperties)
    }
  }

  // Track service inquiries
  trackServiceInquiry(serviceType: string, inquiryMethod: string) {
    this.trackBusinessEvent('lead_generation', 'service_inquiry', {
      service_type: serviceType,
      inquiry_method: inquiryMethod
    })
  }

  // Track user engagement
  trackUserEngagement(action: string, parameters?: Record<string, any>) {
    this.trackBusinessEvent('user_engagement', action, parameters)
  }

  // Set user properties
  setUserProperties(properties: UserProperties) {
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_MEASUREMENT_ID', {
        user_id: properties.userId,
        custom_map: {
          custom_parameter_role: properties.role,
          custom_parameter_device: properties.deviceType
        }
      })
    }

    // Store in local storage for offline analytics
    localStorage.setItem('analytics_user_properties', JSON.stringify(properties))
  }

  // Get business metrics
  getBusinessMetrics(): BusinessMetrics {
    return { ...this.businessMetrics }
  }

  // Get session analytics
  getSessionAnalytics() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      sessionStart: localStorage.getItem('session_start'),
      pageViews: this.businessMetrics.userEngagement.pageViews,
      eventsTracked: this.queue.length
    }
  }

  // Private methods
  private async initializeGA4() {
    // Load Google Analytics 4
    if (typeof window !== 'undefined' && !window.gtag) {
      const script = document.createElement('script')
      script.async = true
      script.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'
      document.head.appendChild(script)

      window.dataLayer = window.dataLayer || []
      window.gtag = function() {
        window.dataLayer.push(arguments)
      }
      
      gtag('js', new Date())
      gtag('config', 'GA_MEASUREMENT_ID', {
        send_page_view: false, // We'll handle page views manually
        session_id: this.sessionId,
        user_id: this.userId
      })
    }
  }

  private initializeCustomAnalytics() {
    // Set session start time
    if (!localStorage.getItem('session_start')) {
      localStorage.setItem('session_start', new Date().toISOString())
    }

    // Track session duration
    this.trackSessionDuration()
    
    // Track scroll depth
    this.trackScrollDepth()
    
    // Track click events
    this.trackClickEvents()
  }

  private track(event: AnalyticsEvent) {
    event.timestamp = Date.now()
    event.sessionId = this.sessionId
    event.userId = this.userId

    if (this.isInitialized) {
      this.sendEvent(event)
    } else {
      this.queue.push(event)
    }
  }

  private sendEvent(event: AnalyticsEvent) {
    // Send to Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', event.name, event.parameters)
    }

    // Send to custom analytics endpoint
    this.sendToCustomEndpoint(event)
    
    // Store for offline analytics
    this.storeOfflineEvent(event)
  }

  private async sendToCustomEndpoint(event: AnalyticsEvent) {
    try {
      await fetch('/api/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(event)
      })
    } catch (error) {
      console.warn('Failed to send analytics event:', error)
    }
  }

  private storeOfflineEvent(event: AnalyticsEvent) {
    const offlineEvents = JSON.parse(localStorage.getItem('offline_analytics') || '[]')
    offlineEvents.push(event)
    
    // Keep only last 100 events
    if (offlineEvents.length > 100) {
      offlineEvents.splice(0, offlineEvents.length - 100)
    }
    
    localStorage.setItem('offline_analytics', JSON.stringify(offlineEvents))
  }

  private processQueue() {
    while (this.queue.length > 0) {
      const event = this.queue.shift()
      if (event) {
        this.sendEvent(event)
      }
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeMetrics(): BusinessMetrics {
    return {
      leadGeneration: {
        contactFormSubmissions: 0,
        emailSignups: 0,
        phoneCallRequests: 0,
        consultationBookings: 0
      },
      userEngagement: {
        pageViews: 0,
        sessionDuration: 0,
        bounceRate: 0,
        returnVisitors: 0
      },
      conversion: {
        signupRate: 0,
        contactRate: 0,
        serviceInquiries: 0,
        quotesRequested: 0
      }
    }
  }

  private updateBusinessMetrics(metric: string, value: number) {
    // Update metrics based on tracked events
    // This is a simplified implementation
  }

  private updateBusinessMetricsFromEvent(category: string, action: string) {
    switch (category) {
      case 'lead_generation':
        if (action === 'contact_form_submission') {
          this.businessMetrics.leadGeneration.contactFormSubmissions++
        }
        break
      case 'conversion':
        if (action === 'user_registration') {
          this.businessMetrics.conversion.signupRate++
        }
        break
    }
  }

  private trackSessionDuration() {
    const startTime = Date.now()
    
    window.addEventListener('beforeunload', () => {
      const duration = Date.now() - startTime
      this.trackEvent('session_duration', { duration_ms: duration })
    })
  }

  private trackScrollDepth() {
    let maxScroll = 0
    
    window.addEventListener('scroll', () => {
      const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100)
      
      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent
        
        // Track milestone scroll depths
        if ([25, 50, 75, 90].includes(scrollPercent)) {
          this.trackEvent('scroll_depth', { scroll_percent: scrollPercent })
        }
      }
    })
  }

  private trackClickEvents() {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement

      // Track button clicks
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button = target.closest('button') || target
        const clickData = {
          button_text: button.textContent?.trim(),
          button_class: button.className
        }

        this.trackEvent('button_click', clickData)

        // Send real-time click event
        this.sendRealTimeEvent('click', {
          element: clickData.button_text || 'Button',
          page: window.location.pathname
        })
      }

      // Track link clicks
      if (target.tagName === 'A' || target.closest('a')) {
        const link = target.closest('a') || target
        const clickData = {
          link_url: (link as HTMLAnchorElement).href,
          link_text: link.textContent?.trim()
        }

        this.trackEvent('link_click', clickData)

        // Send real-time click event
        this.sendRealTimeEvent('click', {
          element: clickData.link_text || 'Link',
          page: window.location.pathname
        })
      }
    })
  }

  // Real-time analytics methods
  private initializeRealTimeAnalytics() {
    if (!this.socket) return

    // Track form submissions in real-time
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement
      const formName = form.getAttribute('name') || form.className || 'Unknown Form'

      this.sendRealTimeEvent('form_submit', {
        form: formName,
        success: true // Will be updated based on actual submission result
      })
    })
  }

  private sendRealTimeEvent(type: string, data: any) {
    if (!this.socket?.isConnected?.value) return

    try {
      this.socket.trackPageView?.(data.page, data.title) ||
      this.socket.trackClick?.(data.element, data.page) ||
      this.socket.trackFormSubmit?.(data.form, data.success)
    } catch (error) {
      console.warn('Failed to send real-time analytics event:', error)
    }
  }

  // Public method to set socket instance
  setSocket(socket: any) {
    this.socket = socket
    if (this.isInitialized) {
      this.initializeRealTimeAnalytics()
    }
  }
}

// Create singleton instance
export const analytics = new AnalyticsService()

// Export types
export type { AnalyticsEvent, UserProperties, BusinessMetrics }
