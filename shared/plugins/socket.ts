import { App, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useSocket } from '@/composables/useSocket'
import { useAuthStore } from '@/stores/auth'

// Socket.io plugin for Vue
export default {
  install(app: App) {
    // Make socket available globally
    app.config.globalProperties.$socket = useSocket()
    
    // Provide socket for injection
    app.provide('socket', useSocket())
  }
}

// Auto-connect socket when user is authenticated
export function initializeSocket() {
  const socket = useSocket()
  
  // Auto-connect when token is available
  const authStore = useAuthStore()
  
  watch(() => authStore.token, (newToken) => {
    if (newToken) {
      socket.connect()
    } else {
      socket.disconnect()
    }
  }, { immediate: true })
  
  return socket
}

// Socket.io analytics integration
export function useSocketAnalytics() {
  const socket = useSocket()
  const route = useRoute()
  
  // Track page views automatically
  watch(() => route.path, (newPath) => {
    if (socket.isConnected.value) {
      socket.trackPageView(newPath, document.title)
    }
  }, { immediate: true })
  
  // Track clicks on important elements
  const trackClick = (element: string) => {
    if (socket.isConnected.value) {
      socket.trackClick(element, route.path)
    }
  }
  
  // Track form submissions
  const trackFormSubmit = (form: string, success: boolean) => {
    if (socket.isConnected.value) {
      socket.trackFormSubmit(form, success)
    }
  }
  
  return {
    trackClick,
    trackFormSubmit
  }
}

// Socket.io notifications integration
export function useSocketNotifications() {
  const socket = useSocket()
  
  // Request notification permission
  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }
  
  // Show browser notification
  const showBrowserNotification = (title: string, options?: NotificationOptions) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })
    }
  }
  
  return {
    notifications: socket.notifications,
    unreadNotifications: socket.unreadNotifications,
    markAsRead: socket.markNotificationAsRead,
    requestNotificationPermission,
    showBrowserNotification
  }
}

// Socket.io real-time communication
export function useSocketCommunication() {
  const socket = useSocket()
  
  // Join a conversation room
  const joinConversation = (conversationId: string) => {
    socket.joinRoom(`conversation:${conversationId}`)
  }
  
  // Leave a conversation room
  const leaveConversation = (conversationId: string) => {
    socket.leaveRoom(`conversation:${conversationId}`)
  }
  
  // Send a message
  const sendMessage = (conversationId: string, message: string, attachments?: any[]) => {
    socket.sendMessage(conversationId, message, attachments)
  }
  
  // Typing indicators
  const startTyping = (conversationId: string) => {
    socket.startTyping(`conversation:${conversationId}`)
  }
  
  const stopTyping = (conversationId: string) => {
    socket.stopTyping(`conversation:${conversationId}`)
  }
  
  return {
    joinConversation,
    leaveConversation,
    sendMessage,
    startTyping,
    stopTyping,
    typingUsers: socket.typingUsers
  }
}

// Socket.io connection status component
export function useSocketStatus() {
  const socket = useSocket()
  
  const getStatusColor = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'success'
      case 'connecting': return 'warning'
      case 'error': return 'error'
      default: return 'neutral'
    }
  }
  
  const getStatusIcon = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'wifi'
      case 'connecting': return 'refresh'
      case 'error': return 'wifi-off'
      default: return 'wifi-off'
    }
  }
  
  const getStatusText = () => {
    switch (socket.connectionStatus.value) {
      case 'connected': return 'Connected'
      case 'connecting': return 'Connecting...'
      case 'error': return 'Connection Error'
      default: return 'Disconnected'
    }
  }
  
  return {
    status: socket.connectionStatus,
    isConnected: socket.isConnected,
    isConnecting: socket.isConnecting,
    error: socket.connectionError,
    reconnectAttempts: socket.reconnectAttempts,
    getStatusColor,
    getStatusIcon,
    getStatusText,
    connect: socket.connect,
    disconnect: socket.disconnect
  }
}
