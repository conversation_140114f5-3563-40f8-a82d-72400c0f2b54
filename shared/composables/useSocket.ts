import { ref, onMounted, onUnmounted, computed } from 'vue'
import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '@/stores/auth'
import { storeToRefs } from 'pinia'

// Socket.io connection state
const socket = ref<Socket | null>(null)
const isConnected = ref(false)
const isConnecting = ref(false)
const connectionError = ref<string | null>(null)
const reconnectAttempts = ref(0)
const lastPing = ref<number | null>(null)

// Real-time data
const onlineUsers = ref<any[]>([])
const notifications = ref<any[]>([])
const analyticsEvents = ref<any[]>([])
const typingUsers = ref<Set<string>>(new Set())

export function useSocket() {
  const authStore = useAuthStore()
  const { token, user } = storeToRefs(authStore)

  // Connection management
  const connect = () => {
    if (socket.value?.connected) return

    if (!token.value) {
      console.warn('Cannot connect to Socket.io: No authentication token')
      return
    }

    isConnecting.value = true
    connectionError.value = null

    const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001'

    socket.value = io(serverUrl, {
      auth: {
        token: token.value
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      retries: 3,
      autoConnect: true
    })

    setupEventListeners()
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
    }
    isConnected.value = false
    isConnecting.value = false
    reconnectAttempts.value = 0
  }

  const setupEventListeners = () => {
    if (!socket.value) return

    // Connection events
    socket.value.on('connect', () => {
      console.log('✅ Connected to Socket.io server')
      isConnected.value = true
      isConnecting.value = false
      connectionError.value = null
      reconnectAttempts.value = 0
    })

    socket.value.on('disconnect', (reason) => {
      console.log('❌ Disconnected from Socket.io server:', reason)
      isConnected.value = false
      isConnecting.value = false
    })

    socket.value.on('connect_error', (error) => {
      console.error('🔌 Socket.io connection error:', error)
      isConnecting.value = false
      connectionError.value = error.message
      reconnectAttempts.value++
    })

    socket.value.on('reconnect', (attemptNumber) => {
      console.log(`🔄 Reconnected to Socket.io server (attempt ${attemptNumber})`)
      reconnectAttempts.value = attemptNumber
    })

    socket.value.on('reconnect_attempt', (attemptNumber) => {
      console.log(`🔄 Attempting to reconnect... (${attemptNumber})`)
      isConnecting.value = true
    })

    socket.value.on('reconnect_failed', () => {
      console.error('❌ Failed to reconnect to Socket.io server')
      isConnecting.value = false
      connectionError.value = 'Failed to reconnect'
    })

    // Server confirmation
    socket.value.on('connected', (data) => {
      console.log('🎉 Socket.io connection confirmed:', data)
    })

    // Real-time notifications
    socket.value.on('notification:received', (notification) => {
      notifications.value.unshift(notification)
      showNotification(notification)
    })

    socket.value.on('notification:urgent', (notification) => {
      notifications.value.unshift(notification)
      showUrgentNotification(notification)
    })

    // Analytics events
    socket.value.on('analytics:realtime_pageview', (event) => {
      analyticsEvents.value.unshift(event)
    })

    socket.value.on('analytics:realtime_click', (event) => {
      analyticsEvents.value.unshift(event)
    })

    socket.value.on('analytics:visitor_count', (data) => {
      onlineUsers.value = data.users
    })

    // Communication events
    socket.value.on('communication:message_received', (message) => {
      handleNewMessage(message)
    })

    socket.value.on('communication:auto_response', (response) => {
      handleAutoResponse(response)
    })

    // Typing indicators
    socket.value.on('typing:user_started', (data) => {
      typingUsers.value.add(data.userId)
    })

    socket.value.on('typing:user_stopped', (data) => {
      typingUsers.value.delete(data.userId)
    })

    // Project updates
    socket.value.on('project:status_updated', (update) => {
      handleProjectUpdate(update)
    })

    socket.value.on('project:milestone_completed', (milestone) => {
      handleMilestone(milestone)
    })

    // Energy monitoring
    socket.value.on('energy:realtime_data', (data) => {
      handleEnergyData(data)
    })

    socket.value.on('notification:energy_alert', (alert) => {
      handleEnergyAlert(alert)
    })

    // System events
    socket.value.on('system:maintenance_notice', (notice) => {
      handleMaintenanceNotice(notice)
    })

    socket.value.on('test:message', (data) => {
      console.log('🧪 Test message received:', data)
    })

    // User presence
    socket.value.on('user:online', (data) => {
      console.log('👤 User came online:', data.userData.email)
    })

    socket.value.on('user:offline', (data) => {
      console.log('👤 User went offline:', data.userData.email)
    })

    // Ping/pong for connection health
    socket.value.on('pong', () => {
      lastPing.value = Date.now()
    })
  }

  // Event emitters
  const trackPageView = (page: string, title: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:pageview', {
      page,
      title,
      timestamp: new Date().toISOString()
    })
  }

  const trackClick = (element: string, page: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:click', {
      element,
      page,
      timestamp: new Date().toISOString()
    })
  }

  const trackFormSubmit = (form: string, success: boolean) => {
    if (!socket.value?.connected) return

    socket.value.emit('analytics:form_submit', {
      form,
      success,
      timestamp: new Date().toISOString()
    })
  }

  const sendMessage = (conversationId: string, message: string, attachments?: any[]) => {
    if (!socket.value?.connected) return

    socket.value.emit('communication:chat_message', {
      conversationId,
      message,
      attachments: attachments || [],
      timestamp: new Date().toISOString()
    })
  }

  const startTyping = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('typing:start', { roomId })
  }

  const stopTyping = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('typing:stop', { roomId })
  }

  const joinRoom = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('room:join', roomId)
  }

  const leaveRoom = (roomId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('room:leave', roomId)
  }

  const markNotificationAsRead = (notificationId: string) => {
    if (!socket.value?.connected) return

    socket.value.emit('notification:read', { notificationId })
  }

  // Event handlers
  const showNotification = (notification: any) => {
    // Integrate with your notification system
    console.log('📢 New notification:', notification)
    
    // You can integrate with a toast library here
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico'
      })
    }
  }

  const showUrgentNotification = (notification: any) => {
    console.log('🚨 Urgent notification:', notification)
    
    // Show more prominent notification for urgent messages
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`🚨 ${notification.title}`, {
        body: notification.message,
        icon: '/favicon.ico',
        requireInteraction: true
      })
    }
  }

  const handleNewMessage = (message: any) => {
    console.log('💬 New message:', message)
    // Handle new chat message
  }

  const handleAutoResponse = (response: any) => {
    console.log('🤖 Auto response:', response)
    // Handle auto response
  }

  const handleProjectUpdate = (update: any) => {
    console.log('📋 Project update:', update)
    // Handle project status update
  }

  const handleMilestone = (milestone: any) => {
    console.log('🎯 Milestone completed:', milestone)
    // Handle milestone completion
  }

  const handleEnergyData = (data: any) => {
    console.log('⚡ Energy data:', data)
    // Handle real-time energy consumption data
  }

  const handleEnergyAlert = (alert: any) => {
    console.log('⚠️ Energy alert:', alert)
    // Handle energy efficiency alert
  }

  const handleMaintenanceNotice = (notice: any) => {
    console.log('🔧 Maintenance notice:', notice)
    // Handle system maintenance notification
  }

  // Computed properties
  const connectionStatus = computed(() => {
    if (isConnected.value) return 'connected'
    if (isConnecting.value) return 'connecting'
    if (connectionError.value) return 'error'
    return 'disconnected'
  })

  const unreadNotifications = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })

  // Lifecycle
  onMounted(() => {
    if (token.value) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    // Connection state
    socket: socket.value,
    isConnected,
    isConnecting,
    connectionError,
    connectionStatus,
    reconnectAttempts,
    lastPing,

    // Data
    onlineUsers,
    notifications,
    analyticsEvents,
    typingUsers,
    unreadNotifications,

    // Methods
    connect,
    disconnect,
    trackPageView,
    trackClick,
    trackFormSubmit,
    sendMessage,
    startTyping,
    stopTyping,
    joinRoom,
    leaveRoom,
    markNotificationAsRead
  }
}
