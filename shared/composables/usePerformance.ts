import { ref, onMounted, onUnmounted } from 'vue'

interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number | null // Largest Contentful Paint
  fid: number | null // First Input Delay
  cls: number | null // Cumulative Layout Shift
  
  // Additional metrics
  fcp: number | null // First Contentful Paint
  ttfb: number | null // Time to First Byte
  
  // Custom metrics
  pageLoadTime: number | null
  domContentLoaded: number | null
  resourceLoadTime: number | null
}

interface ResourceTiming {
  name: string
  duration: number
  size: number
  type: string
}

export function usePerformance() {
  const metrics = ref<PerformanceMetrics>({
    lcp: null,
    fid: null,
    cls: null,
    fcp: null,
    ttfb: null,
    pageLoadTime: null,
    domContentLoaded: null,
    resourceLoadTime: null
  })

  const resourceTimings = ref<ResourceTiming[]>([])
  const isSupported = ref(false)

  // Check if Performance API is supported
  const checkSupport = () => {
    isSupported.value = typeof window !== 'undefined' && 
                       'performance' in window && 
                       'PerformanceObserver' in window
  }

  // Measure Core Web Vitals
  const measureCoreWebVitals = () => {
    if (!isSupported.value) return

    // Largest Contentful Paint (LCP)
    try {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        metrics.value.lcp = Math.round(lastEntry.startTime)
      })
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })
    } catch (error) {
      console.warn('LCP measurement not supported:', error)
    }

    // First Input Delay (FID)
    try {
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          metrics.value.fid = Math.round(entry.processingStart - entry.startTime)
        })
      })
      fidObserver.observe({ type: 'first-input', buffered: true })
    } catch (error) {
      console.warn('FID measurement not supported:', error)
    }

    // Cumulative Layout Shift (CLS)
    try {
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            metrics.value.cls = Math.round(clsValue * 1000) / 1000
          }
        })
      })
      clsObserver.observe({ type: 'layout-shift', buffered: true })
    } catch (error) {
      console.warn('CLS measurement not supported:', error)
    }

    // First Contentful Paint (FCP)
    try {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-contentful-paint') {
            metrics.value.fcp = Math.round(entry.startTime)
          }
        })
      })
      fcpObserver.observe({ type: 'paint', buffered: true })
    } catch (error) {
      console.warn('FCP measurement not supported:', error)
    }
  }

  // Measure Navigation Timing
  const measureNavigationTiming = () => {
    if (!isSupported.value) return

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      metrics.value.ttfb = Math.round(navigation.responseStart - navigation.fetchStart)
      metrics.value.domContentLoaded = Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart)
      metrics.value.pageLoadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart)
    }
  }

  // Measure Resource Loading
  const measureResourceTiming = () => {
    if (!isSupported.value) return

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    resourceTimings.value = resources.map(resource => ({
      name: resource.name.split('/').pop() || resource.name,
      duration: Math.round(resource.duration),
      size: (resource as any).transferSize || 0,
      type: getResourceType(resource.name)
    }))

    // Calculate total resource load time
    const totalResourceTime = resources.reduce((total, resource) => total + resource.duration, 0)
    metrics.value.resourceLoadTime = Math.round(totalResourceTime)
  }

  // Get resource type from URL
  const getResourceType = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase()
    
    if (['js', 'mjs'].includes(extension || '')) return 'script'
    if (['css'].includes(extension || '')) return 'stylesheet'
    if (['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'].includes(extension || '')) return 'image'
    if (['woff', 'woff2', 'ttf', 'otf'].includes(extension || '')) return 'font'
    if (url.includes('api/')) return 'api'
    
    return 'other'
  }

  // Get performance score based on Core Web Vitals
  const getPerformanceScore = () => {
    if (!metrics.value.lcp || !metrics.value.fid || metrics.value.cls === null) {
      return null
    }

    let score = 0
    let totalWeight = 0

    // LCP scoring (weight: 25%)
    if (metrics.value.lcp <= 2500) score += 100 * 0.25
    else if (metrics.value.lcp <= 4000) score += 50 * 0.25
    else score += 0 * 0.25
    totalWeight += 0.25

    // FID scoring (weight: 25%)
    if (metrics.value.fid <= 100) score += 100 * 0.25
    else if (metrics.value.fid <= 300) score += 50 * 0.25
    else score += 0 * 0.25
    totalWeight += 0.25

    // CLS scoring (weight: 25%)
    if (metrics.value.cls <= 0.1) score += 100 * 0.25
    else if (metrics.value.cls <= 0.25) score += 50 * 0.25
    else score += 0 * 0.25
    totalWeight += 0.25

    // FCP scoring (weight: 25%)
    if (metrics.value.fcp && metrics.value.fcp <= 1800) score += 100 * 0.25
    else if (metrics.value.fcp && metrics.value.fcp <= 3000) score += 50 * 0.25
    else score += 0 * 0.25
    totalWeight += 0.25

    return Math.round(score / totalWeight)
  }

  // Send metrics to analytics
  const sendMetrics = () => {
    if (!isSupported.value) return

    const performanceData = {
      ...metrics.value,
      score: getPerformanceScore(),
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      url: window.location.href
    }

    // Send to analytics service (implement based on your analytics provider)
    console.log('Performance Metrics:', performanceData)
    
    // Example: Send to Google Analytics 4
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metrics', {
        custom_parameter_lcp: metrics.value.lcp,
        custom_parameter_fid: metrics.value.fid,
        custom_parameter_cls: metrics.value.cls,
        custom_parameter_score: getPerformanceScore()
      })
    }
  }

  // Mark custom performance milestones
  const mark = (name: string) => {
    if (isSupported.value) {
      performance.mark(name)
    }
  }

  const measure = (name: string, startMark: string, endMark?: string) => {
    if (isSupported.value) {
      try {
        if (endMark) {
          performance.measure(name, startMark, endMark)
        } else {
          performance.measure(name, startMark)
        }
        
        const measures = performance.getEntriesByName(name, 'measure')
        return measures[measures.length - 1]?.duration || 0
      } catch (error) {
        console.warn('Performance measure failed:', error)
        return 0
      }
    }
    return 0
  }

  // Initialize performance monitoring
  const init = () => {
    checkSupport()
    
    if (!isSupported.value) {
      console.warn('Performance monitoring not supported in this browser')
      return
    }

    measureCoreWebVitals()
    
    // Measure navigation timing after page load
    if (document.readyState === 'complete') {
      measureNavigationTiming()
      measureResourceTiming()
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => {
          measureNavigationTiming()
          measureResourceTiming()
          sendMetrics()
        }, 1000) // Wait 1 second after load for accurate measurements
      })
    }
  }

  onMounted(() => {
    init()
  })

  return {
    metrics,
    resourceTimings,
    isSupported,
    getPerformanceScore,
    mark,
    measure,
    sendMetrics
  }
}
