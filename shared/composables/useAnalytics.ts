import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { analytics } from '@/services/analytics'
import { useAuthStore } from '@/stores/auth'

export function useAnalytics() {
  const route = useRoute()
  const router = useRouter()
  const authStore = useAuthStore()
  
  const isInitialized = ref(false)
  const sessionMetrics = ref({
    pageViews: 0,
    timeOnPage: 0,
    interactions: 0
  })

  // Initialize analytics
  const initialize = async () => {
    if (isInitialized.value) return

    await analytics.initialize({
      userId: authStore.user?.id?.toString(),
      debug: import.meta.env.DEV
    })

    // Set user properties if authenticated
    if (authStore.user) {
      analytics.setUserProperties({
        userId: authStore.user.id.toString(),
        email: authStore.user.email,
        role: authStore.user.role,
        registrationDate: authStore.user.createdAt,
        preferredLanguage: navigator.language,
        deviceType: getDeviceType()
      })
    }

    isInitialized.value = true
  }

  // Track page views automatically
  const trackPageView = (pageName?: string) => {
    const page = pageName || route.name?.toString() || route.path
    analytics.trackPageView(page, document.title)
    sessionMetrics.value.pageViews++
  }

  // Business event tracking methods
  const trackContactFormSubmission = (formData: any) => {
    analytics.trackContactFormSubmission(formData)
    sessionMetrics.value.interactions++
  }

  const trackUserRegistration = (method: string = 'email') => {
    analytics.trackUserRegistration(method, {
      userId: authStore.user?.id?.toString(),
      email: authStore.user?.email,
      role: authStore.user?.role,
      registrationDate: new Date().toISOString(),
      preferredLanguage: navigator.language,
      deviceType: getDeviceType()
    })
  }

  const trackServiceInquiry = (serviceType: string, method: string = 'contact_form') => {
    analytics.trackServiceInquiry(serviceType, method)
    sessionMetrics.value.interactions++
  }

  const trackUserLogin = (method: string = 'email') => {
    analytics.trackEvent('user_login', {
      login_method: method,
      user_role: authStore.user?.role
    })
  }

  const trackUserLogout = () => {
    analytics.trackEvent('user_logout', {
      session_duration: getSessionDuration()
    })
  }

  // Engagement tracking
  const trackButtonClick = (buttonName: string, context?: string) => {
    analytics.trackUserEngagement('button_click', {
      button_name: buttonName,
      context: context || route.name?.toString(),
      page: route.path
    })
    sessionMetrics.value.interactions++
  }

  const trackFeatureUsage = (featureName: string, action: string = 'used') => {
    analytics.trackUserEngagement('feature_usage', {
      feature_name: featureName,
      action,
      user_role: authStore.user?.role
    })
  }

  const trackSearchQuery = (query: string, resultsCount: number = 0) => {
    analytics.trackUserEngagement('search', {
      search_query: query,
      results_count: resultsCount,
      page: route.path
    })
  }

  const trackDownload = (fileName: string, fileType: string) => {
    analytics.trackEvent('file_download', {
      file_name: fileName,
      file_type: fileType,
      page: route.path
    })
  }

  const trackError = (errorType: string, errorMessage: string, context?: string) => {
    analytics.trackEvent('error_occurred', {
      error_type: errorType,
      error_message: errorMessage,
      context: context || route.name?.toString(),
      user_role: authStore.user?.role
    })
  }

  // Conversion tracking
  const trackConversion = (conversionType: string, value?: number) => {
    analytics.trackBusinessEvent('conversion', conversionType, {
      conversion_value: value,
      page: route.path,
      user_role: authStore.user?.role
    })
  }

  const trackQuoteRequest = (serviceType: string, estimatedValue?: number) => {
    analytics.trackBusinessEvent('lead_generation', 'quote_request', {
      service_type: serviceType,
      estimated_value: estimatedValue
    })
    trackConversion('quote_request', estimatedValue)
  }

  const trackConsultationBooking = (consultationType: string, scheduledDate?: string) => {
    analytics.trackBusinessEvent('lead_generation', 'consultation_booking', {
      consultation_type: consultationType,
      scheduled_date: scheduledDate
    })
    trackConversion('consultation_booking')
  }

  // Performance tracking
  const trackPerformanceMetric = (metricName: string, value: number, unit: string = 'ms') => {
    analytics.trackEvent('performance_metric', {
      metric_name: metricName,
      metric_value: value,
      metric_unit: unit,
      page: route.path
    })
  }

  const trackLoadTime = (loadTime: number) => {
    trackPerformanceMetric('page_load_time', loadTime)
  }

  // Utility functions
  const getDeviceType = (): string => {
    const userAgent = navigator.userAgent.toLowerCase()
    
    if (/tablet|ipad|playbook|silk/.test(userAgent)) {
      return 'tablet'
    }
    
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/.test(userAgent)) {
      return 'mobile'
    }
    
    return 'desktop'
  }

  const getSessionDuration = (): number => {
    const sessionStart = localStorage.getItem('session_start')
    if (sessionStart) {
      return Date.now() - new Date(sessionStart).getTime()
    }
    return 0
  }

  // Get analytics data
  const getBusinessMetrics = () => {
    return analytics.getBusinessMetrics()
  }

  const getSessionAnalytics = () => {
    return {
      ...analytics.getSessionAnalytics(),
      ...sessionMetrics.value,
      sessionDuration: getSessionDuration()
    }
  }

  // Auto-track route changes
  const setupRouteTracking = () => {
    // Track initial page view
    trackPageView()

    // Track route changes
    router.afterEach((to, from) => {
      // Track page view for new route
      trackPageView(to.name?.toString() || to.path)
      
      // Track navigation
      analytics.trackEvent('page_navigation', {
        from_page: from.name?.toString() || from.path,
        to_page: to.name?.toString() || to.path,
        navigation_type: 'route_change'
      })
    })
  }

  // Auto-track time on page
  const setupTimeTracking = () => {
    let startTime = Date.now()
    
    const trackTimeOnPage = () => {
      const timeOnPage = Date.now() - startTime
      sessionMetrics.value.timeOnPage = timeOnPage
      
      analytics.trackEvent('time_on_page', {
        time_ms: timeOnPage,
        page: route.path
      })
    }

    // Track time when leaving page
    onUnmounted(() => {
      trackTimeOnPage()
    })

    // Track time on visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        trackTimeOnPage()
      } else {
        startTime = Date.now()
      }
    })
  }

  // Initialize on mount
  onMounted(async () => {
    await initialize()
    setupRouteTracking()
    setupTimeTracking()
  })

  return {
    // State
    isInitialized,
    sessionMetrics,
    
    // Core tracking
    initialize,
    trackPageView,
    
    // Business events
    trackContactFormSubmission,
    trackUserRegistration,
    trackServiceInquiry,
    trackUserLogin,
    trackUserLogout,
    
    // Engagement
    trackButtonClick,
    trackFeatureUsage,
    trackSearchQuery,
    trackDownload,
    trackError,
    
    // Conversions
    trackConversion,
    trackQuoteRequest,
    trackConsultationBooking,
    
    // Performance
    trackPerformanceMetric,
    trackLoadTime,
    
    // Data access
    getBusinessMetrics,
    getSessionAnalytics
  }
}
