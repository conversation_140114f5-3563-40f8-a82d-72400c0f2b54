<template>
  <div class="bg-base-100 rounded-lg shadow-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold flex items-center">
        <Icon name="chart-line" size="lg" class="mr-3 text-primary" />
        Real-Time Analytics
      </h2>
      
      <!-- Connection Status -->
      <div class="flex items-center space-x-2">
        <div class="flex items-center space-x-2">
          <div 
            class="w-3 h-3 rounded-full"
            :class="{
              'bg-success animate-pulse': socketStatus.isConnected,
              'bg-warning animate-pulse': socketStatus.isConnecting,
              'bg-error': socketStatus.status === 'error',
              'bg-neutral': socketStatus.status === 'disconnected'
            }"
          ></div>
          <span class="text-sm font-medium">{{ socketStatus.getStatusText() }}</span>
        </div>
        
        <button 
          v-if="!socketStatus.isConnected" 
          @click="socketStatus.connect()"
          class="btn btn-sm btn-primary"
        >
          <Icon name="refresh" size="sm" class="mr-1" />
          Reconnect
        </button>
      </div>
    </div>

    <!-- Real-time Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Online Users -->
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-figure text-primary">
          <Icon name="users" size="xl" />
        </div>
        <div class="stat-title">Online Users</div>
        <div class="stat-value text-primary">{{ onlineUsers.length }}</div>
        <div class="stat-desc">{{ getActiveUsersText() }}</div>
      </div>

      <!-- Page Views (Last Hour) -->
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-figure text-secondary">
          <Icon name="eye" size="xl" />
        </div>
        <div class="stat-title">Page Views</div>
        <div class="stat-value text-secondary">{{ recentPageViews }}</div>
        <div class="stat-desc">Last hour</div>
      </div>

      <!-- Form Submissions -->
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-figure text-accent">
          <Icon name="form" size="xl" />
        </div>
        <div class="stat-title">Form Submissions</div>
        <div class="stat-value text-accent">{{ recentFormSubmissions }}</div>
        <div class="stat-desc">Today</div>
      </div>

      <!-- Assessment Requests -->
      <div class="stat bg-base-200 rounded-lg">
        <div class="stat-figure text-warning">
          <Icon name="lightbulb" size="xl" />
        </div>
        <div class="stat-title">Assessment Requests</div>
        <div class="stat-value text-warning">{{ assessmentRequests }}</div>
        <div class="stat-desc">This week</div>
      </div>
    </div>

    <!-- Real-time Activity Feed -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Live Activity -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="activity" size="md" class="mr-2 text-primary" />
          Live Activity
        </h3>
        
        <div class="space-y-3 max-h-80 overflow-y-auto">
          <div 
            v-for="event in recentEvents" 
            :key="event.id"
            class="flex items-center space-x-3 p-3 bg-base-100 rounded-lg"
          >
            <div class="flex-shrink-0">
              <Icon 
                :name="getEventIcon(event.type)" 
                size="sm" 
                :class="getEventColor(event.type)"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium truncate">{{ getEventDescription(event) }}</p>
              <p class="text-xs text-base-content/60">{{ formatTime(event.timestamp) }}</p>
            </div>
          </div>
          
          <div v-if="recentEvents.length === 0" class="text-center py-8 text-base-content/60">
            <Icon name="clock" size="lg" class="mx-auto mb-2" />
            <p>No recent activity</p>
          </div>
        </div>
      </div>

      <!-- Online Users List -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="users" size="md" class="mr-2 text-secondary" />
          Online Users ({{ onlineUsers.length }})
        </h3>
        
        <div class="space-y-2 max-h-80 overflow-y-auto">
          <div 
            v-for="user in onlineUsers" 
            :key="user.id"
            class="flex items-center space-x-3 p-2 bg-base-100 rounded-lg"
          >
            <div class="avatar placeholder">
              <div class="bg-neutral text-neutral-content rounded-full w-8">
                <span class="text-xs">{{ getUserInitials(user) }}</span>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium truncate">{{ user.name || user.email }}</p>
              <p class="text-xs text-base-content/60">{{ user.role }}</p>
            </div>
            <div class="flex-shrink-0">
              <div class="w-2 h-2 bg-success rounded-full"></div>
            </div>
          </div>
          
          <div v-if="onlineUsers.length === 0" class="text-center py-8 text-base-content/60">
            <Icon name="user-x" size="lg" class="mx-auto mb-2" />
            <p>No users online</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Charts (placeholder for future implementation) -->
    <div class="mt-6 bg-base-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <Icon name="chart-bar" size="md" class="mr-2 text-accent" />
        Real-time Charts
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-base-100 rounded-lg p-4 h-48 flex items-center justify-center">
          <div class="text-center text-base-content/60">
            <Icon name="chart-line" size="xl" class="mx-auto mb-2" />
            <p>Page Views Chart</p>
            <p class="text-xs">Coming Soon</p>
          </div>
        </div>
        
        <div class="bg-base-100 rounded-lg p-4 h-48 flex items-center justify-center">
          <div class="text-center text-base-content/60">
            <Icon name="chart-pie" size="xl" class="mx-auto mb-2" />
            <p>User Activity Chart</p>
            <p class="text-xs">Coming Soon</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { useSocket } from '@/composables/useSocket'
import { useSocketStatus } from '@/plugins/socket'
import Icon from '@/components/common/Icon.vue'

// Socket.io integration
const socket = useSocket()
const socketStatus = useSocketStatus()

// Computed properties
const onlineUsers = computed(() => socket.onlineUsers.value)
const analyticsEvents = computed(() => socket.analyticsEvents.value)

const recentEvents = computed(() => {
  return analyticsEvents.value
    .slice(0, 20)
    .map(event => ({
      ...event,
      id: `${event.timestamp}-${event.type}`
    }))
})

const recentPageViews = computed(() => {
  const oneHourAgo = Date.now() - (60 * 60 * 1000)
  return analyticsEvents.value.filter(event => 
    event.type === 'pageview' && 
    new Date(event.timestamp).getTime() > oneHourAgo
  ).length
})

const recentFormSubmissions = computed(() => {
  const today = new Date().toDateString()
  return analyticsEvents.value.filter(event => 
    event.type === 'form_submit' && 
    new Date(event.timestamp).toDateString() === today
  ).length
})

const assessmentRequests = computed(() => {
  const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
  return analyticsEvents.value.filter(event => 
    event.type === 'assessment_request' && 
    new Date(event.timestamp).getTime() > oneWeekAgo
  ).length
})

// Helper functions
const getActiveUsersText = () => {
  const roles = onlineUsers.value.reduce((acc, user) => {
    acc[user.role] = (acc[user.role] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const parts = Object.entries(roles).map(([role, count]) => `${count} ${role}${count > 1 ? 's' : ''}`)
  return parts.join(', ') || 'No active users'
}

const getUserInitials = (user: any) => {
  if (user.name) {
    return user.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()
  }
  return user.email[0].toUpperCase()
}

const getEventIcon = (type: string) => {
  switch (type) {
    case 'pageview': return 'eye'
    case 'click': return 'cursor-click'
    case 'form_submit': return 'form'
    case 'assessment_request': return 'lightbulb'
    default: return 'activity'
  }
}

const getEventColor = (type: string) => {
  switch (type) {
    case 'pageview': return 'text-primary'
    case 'click': return 'text-secondary'
    case 'form_submit': return 'text-accent'
    case 'assessment_request': return 'text-warning'
    default: return 'text-neutral'
  }
}

const getEventDescription = (event: any) => {
  switch (event.type) {
    case 'pageview':
      return `Page view: ${event.page}`
    case 'click':
      return `Clicked: ${event.element} on ${event.page}`
    case 'form_submit':
      return `Form submitted: ${event.form} (${event.success ? 'success' : 'failed'})`
    case 'assessment_request':
      return `New ${event.serviceType} assessment request`
    default:
      return `${event.type} event`
  }
}

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return 'Just now'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
  return date.toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  if (!socketStatus.isConnected.value) {
    socketStatus.connect()
  }
})
</script>
