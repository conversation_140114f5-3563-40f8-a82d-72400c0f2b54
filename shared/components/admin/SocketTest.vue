<template>
  <div class="bg-base-100 rounded-lg shadow-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold flex items-center">
        <Icon name="zap" size="lg" class="mr-3 text-primary" />
        Socket.io Real-time Test
      </h2>
      
      <!-- Connection Status -->
      <SocketStatus :auto-expand="false" />
    </div>

    <!-- Test Controls -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Analytics Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="chart-line" size="md" class="mr-2 text-primary" />
          Analytics Events
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendPageView"
            class="btn btn-primary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="eye" size="sm" class="mr-2" />
            Send Page View
          </button>
          
          <button 
            @click="sendClickEvent"
            class="btn btn-secondary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="cursor-click" size="sm" class="mr-2" />
            Send Click Event
          </button>
          
          <button 
            @click="sendFormSubmit"
            class="btn btn-accent btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="form" size="sm" class="mr-2" />
            Send Form Submit
          </button>
        </div>
      </div>

      <!-- Communication Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="message-circle" size="md" class="mr-2 text-secondary" />
          Communication
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendTestMessage"
            class="btn btn-primary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="send" size="sm" class="mr-2" />
            Send Test Message
          </button>
          
          <button 
            @click="joinTestRoom"
            class="btn btn-secondary btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="users" size="sm" class="mr-2" />
            Join Test Room
          </button>
          
          <button 
            @click="startTyping"
            class="btn btn-accent btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="edit" size="sm" class="mr-2" />
            Start Typing
          </button>
        </div>
      </div>

      <!-- Notifications Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="bell" size="md" class="mr-2 text-warning" />
          Notifications
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="sendNotification"
            class="btn btn-warning btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="bell" size="sm" class="mr-2" />
            Send Notification
          </button>
          
          <button 
            @click="sendUrgentAlert"
            class="btn btn-error btn-sm w-full"
            :disabled="!socket.isConnected"
          >
            <Icon name="alert-triangle" size="sm" class="mr-2" />
            Send Urgent Alert
          </button>
          
          <div class="text-sm text-base-content/70">
            Unread: {{ socket.unreadNotifications }}
          </div>
        </div>
      </div>

      <!-- API Test -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <Icon name="server" size="md" class="mr-2 text-info" />
          API Tests
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="testSocketAPI"
            class="btn btn-info btn-sm w-full"
          >
            <Icon name="activity" size="sm" class="mr-2" />
            Test Socket API
          </button>
          
          <button 
            @click="getOnlineUsers"
            class="btn btn-success btn-sm w-full"
          >
            <Icon name="users" size="sm" class="mr-2" />
            Get Online Users
          </button>
          
          <button 
            @click="broadcastMessage"
            class="btn btn-primary btn-sm w-full"
          >
            <Icon name="broadcast" size="sm" class="mr-2" />
            Broadcast Message
          </button>
        </div>
      </div>
    </div>

    <!-- Event Log -->
    <div class="mt-6 bg-base-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <Icon name="list" size="md" class="mr-2 text-neutral" />
        Event Log
        <button 
          @click="clearLog"
          class="btn btn-ghost btn-xs ml-auto"
        >
          Clear
        </button>
      </h3>
      
      <div class="bg-base-100 rounded p-3 max-h-60 overflow-y-auto">
        <div 
          v-for="(event, index) in eventLog" 
          :key="index"
          class="text-sm font-mono mb-2 p-2 rounded"
          :class="{
            'bg-success/20 text-success': event.type === 'success',
            'bg-error/20 text-error': event.type === 'error',
            'bg-info/20 text-info': event.type === 'info',
            'bg-warning/20 text-warning': event.type === 'warning'
          }"
        >
          <div class="flex justify-between items-start">
            <span class="font-semibold">{{ event.title }}</span>
            <span class="text-xs opacity-70">{{ formatTime(event.timestamp) }}</span>
          </div>
          <div class="mt-1 opacity-80">{{ event.message }}</div>
          <div v-if="event.data" class="mt-1 text-xs opacity-60">
            {{ JSON.stringify(event.data, null, 2) }}
          </div>
        </div>
        
        <div v-if="eventLog.length === 0" class="text-center py-4 text-base-content/60">
          No events yet. Try the buttons above!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSocket } from '@/composables/useSocket'
import SocketStatus from '@/components/common/SocketStatus.vue'
import Icon from '@/components/common/Icon.vue'

// Socket integration
const socket = useSocket()

// Event log
const eventLog = ref<any[]>([])

// Helper functions
const addToLog = (type: string, title: string, message: string, data?: any) => {
  eventLog.value.unshift({
    type,
    title,
    message,
    data,
    timestamp: new Date().toISOString()
  })
  
  // Keep only last 50 events
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

const clearLog = () => {
  eventLog.value = []
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString()
}

// Analytics test functions
const sendPageView = () => {
  socket.trackPageView('/test-page', 'Socket.io Test Page')
  addToLog('success', 'Page View Sent', 'Tracked page view for /test-page')
}

const sendClickEvent = () => {
  socket.trackClick('test-button', '/test-page')
  addToLog('success', 'Click Event Sent', 'Tracked click on test-button')
}

const sendFormSubmit = () => {
  socket.trackFormSubmit('test-form', true)
  addToLog('success', 'Form Submit Sent', 'Tracked successful form submission')
}

// Communication test functions
const sendTestMessage = () => {
  socket.sendMessage('test-conversation', 'Hello from Socket.io test!')
  addToLog('info', 'Message Sent', 'Sent test message to conversation')
}

const joinTestRoom = () => {
  socket.joinRoom('test-room')
  addToLog('info', 'Room Joined', 'Joined test-room')
}

const startTyping = () => {
  socket.startTyping('test-room')
  addToLog('info', 'Typing Started', 'Started typing in test-room')
  
  // Stop typing after 3 seconds
  setTimeout(() => {
    socket.stopTyping('test-room')
    addToLog('info', 'Typing Stopped', 'Stopped typing in test-room')
  }, 3000)
}

// Notification test functions
const sendNotification = () => {
  // This would normally be sent from the server
  addToLog('warning', 'Notification Test', 'This would send a notification via server API')
}

const sendUrgentAlert = () => {
  addToLog('error', 'Urgent Alert Test', 'This would send an urgent alert via server API')
}

// API test functions
const testSocketAPI = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: 'Test message from frontend'
      })
    })
    
    const data = await response.json()
    addToLog('success', 'API Test Success', 'Socket API test completed', data)
  } catch (error) {
    addToLog('error', 'API Test Failed', error.message)
  }
}

const getOnlineUsers = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/users/online')
    const data = await response.json()
    addToLog('info', 'Online Users', `Found ${data.count} online users`, data.users)
  } catch (error) {
    addToLog('error', 'Failed to get users', error.message)
  }
}

const broadcastMessage = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/v1/socket/broadcast', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event: 'test:broadcast',
        message: 'Hello from frontend broadcast test!'
      })
    })
    
    const data = await response.json()
    addToLog('success', 'Broadcast Sent', `Message sent to ${data.recipients} users`, data)
  } catch (error) {
    addToLog('error', 'Broadcast Failed', error.message)
  }
}

// Auto-connect on mount
onMounted(() => {
  if (!socket.isConnected.value) {
    socket.connect()
    addToLog('info', 'Connecting', 'Attempting to connect to Socket.io server...')
  }
})
</script>
