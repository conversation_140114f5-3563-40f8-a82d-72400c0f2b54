<template>
  <div class="service-packages">
    <div class="container mx-auto px-4 py-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-base-content mb-4">
          {{ $t('services.title', 'Choose Your Energy Solution') }}
        </h2>
        <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
          {{ $t('services.subtitle', 'Professional energy consulting services tailored to your needs') }}
        </p>
      </div>

      <div v-if="loading" class="flex justify-center py-12">
        <div class="loading loading-spinner loading-lg"></div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div
          v-for="package in packages"
          :key="package.id"
          class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300"
          :class="{ 'ring-2 ring-primary': package.popular }"
        >
          <div class="card-body">
            <!-- Popular badge -->
            <div v-if="package.popular" class="badge badge-primary badge-lg mb-4">
              {{ $t('services.popular', 'Most Popular') }}
            </div>

            <!-- Package name -->
            <h3 class="card-title text-xl mb-2">{{ package.name }}</h3>
            
            <!-- Description -->
            <p class="text-base-content/70 mb-4">{{ package.description }}</p>

            <!-- Price -->
            <div class="mb-6">
              <div class="text-3xl font-bold text-primary">
                ${{ formatPrice(package.price) }}
                <span v-if="package.type === 'subscription'" class="text-sm text-base-content/70">
                  /{{ $t('services.month', 'month') }}
                </span>
              </div>
              <div v-if="package.duration" class="text-sm text-base-content/70">
                {{ package.duration }}
              </div>
            </div>

            <!-- Features -->
            <div class="mb-6">
              <ul class="space-y-2">
                <li
                  v-for="feature in package.features"
                  :key="feature"
                  class="flex items-center text-sm"
                >
                  <Icon name="check" size="sm" class="text-success mr-2 flex-shrink-0" />
                  <span>{{ feature }}</span>
                </li>
              </ul>
            </div>

            <!-- Action button -->
            <div class="card-actions justify-end mt-auto">
              <button
                class="btn btn-primary w-full"
                :class="{ 'btn-outline': !package.popular }"
                @click="selectPackage(package)"
                :disabled="processing"
              >
                <Icon v-if="processing && selectedPackageId === package.id" name="loading" size="sm" class="animate-spin mr-2" />
                {{ package.type === 'subscription' ? $t('services.subscribe', 'Subscribe') : $t('services.purchase', 'Purchase') }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Custom package option -->
      <div class="text-center mt-12">
        <div class="card bg-base-200 shadow-lg max-w-md mx-auto">
          <div class="card-body text-center">
            <h3 class="card-title justify-center mb-2">
              {{ $t('services.custom.title', 'Need Something Custom?') }}
            </h3>
            <p class="text-base-content/70 mb-4">
              {{ $t('services.custom.description', 'Contact us for a personalized energy solution') }}
            </p>
            <button
              class="btn btn-outline btn-primary"
              @click="requestCustomQuote"
            >
              {{ $t('services.custom.button', 'Request Custom Quote') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Payment Modal -->
    <PaymentModal
      v-if="showPaymentModal"
      :package="selectedPackage"
      @close="closePaymentModal"
      @success="onPaymentSuccess"
      @error="onPaymentError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { paymentService, type ServicePackage } from '@/services/payment'
import { useAnalytics } from '@/composables/useAnalytics'
import { useNotifications } from '@/composables/useNotifications'
import Icon from '@/components/common/Icon.vue'
import PaymentModal from './PaymentModal.vue'

const router = useRouter()
const { trackServiceInquiry, trackQuoteRequest } = useAnalytics()
const { showSuccess, showError } = useNotifications()

const packages = ref<ServicePackage[]>([])
const loading = ref(true)
const processing = ref(false)
const selectedPackage = ref<ServicePackage | null>(null)
const selectedPackageId = ref<string | null>(null)
const showPaymentModal = ref(false)

// Load service packages
const loadPackages = async () => {
  try {
    loading.value = true
    packages.value = await paymentService.getServicePackages()
  } catch (error) {
    console.error('Failed to load service packages:', error)
    showError('Failed to load service packages')
  } finally {
    loading.value = false
  }
}

// Format price for display
const formatPrice = (priceInCents: number): string => {
  return (priceInCents / 100).toFixed(2)
}

// Select package for purchase
const selectPackage = async (pkg: ServicePackage) => {
  selectedPackage.value = pkg
  selectedPackageId.value = pkg.id
  processing.value = true

  try {
    // Track analytics
    trackServiceInquiry(pkg.name, 'package_selection')

    // Initialize payment service if needed
    await paymentService.initialize(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)

    // Show payment modal
    showPaymentModal.value = true
  } catch (error) {
    console.error('Failed to initialize payment:', error)
    showError('Payment system unavailable. Please try again later.')
  } finally {
    processing.value = false
    selectedPackageId.value = null
  }
}

// Request custom quote
const requestCustomQuote = () => {
  trackQuoteRequest('custom_energy_solution')
  router.push('/contact?service=custom_quote')
}

// Close payment modal
const closePaymentModal = () => {
  showPaymentModal.value = false
  selectedPackage.value = null
}

// Handle successful payment
const onPaymentSuccess = (paymentResult: any) => {
  showSuccess('Payment successful! You will receive a confirmation email shortly.')
  closePaymentModal()
  
  // Track conversion
  trackServiceInquiry(selectedPackage.value?.name || 'unknown', 'payment_completed')
  
  // Redirect to success page or dashboard
  router.push('/dashboard?payment=success')
}

// Handle payment error
const onPaymentError = (error: string) => {
  showError(`Payment failed: ${error}`)
  closePaymentModal()
}

onMounted(() => {
  loadPackages()
})
</script>

<style scoped>
.service-packages {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
}

.card.ring-2 {
  position: relative;
}

.card.ring-2::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #02342b, #eaaa34);
  border-radius: inherit;
  z-index: -1;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .service-packages {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }
}
</style>
