<template>
  <div class="optimized-image-container" :class="containerClass">
    <img
      v-if="!isLazy || isVisible"
      :src="optimizedSrc"
      :alt="alt"
      :class="imageClass"
      :loading="isLazy ? 'lazy' : 'eager'"
      :decoding="isLazy ? 'async' : 'sync'"
      @load="onLoad"
      @error="onError"
    />
    <div
      v-else-if="isLazy && !isVisible"
      class="placeholder"
      :class="placeholderClass"
      :style="placeholderStyle"
    >
      <div class="placeholder-content">
        <Icon name="image" size="lg" class="text-base-content/30" />
      </div>
    </div>
    <div
      v-if="isLoading"
      class="loading-overlay"
      :class="loadingClass"
    >
      <div class="loading-spinner">
        <Icon name="loading" size="md" class="animate-spin" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Icon from './Icon.vue'

interface Props {
  src: string
  alt: string
  width?: number
  height?: number
  quality?: number
  format?: 'webp' | 'avif' | 'jpg' | 'png'
  lazy?: boolean
  placeholder?: string
  containerClass?: string
  imageClass?: string
  placeholderClass?: string
  loadingClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  quality: 85,
  format: 'webp',
  lazy: true,
  containerClass: '',
  imageClass: '',
  placeholderClass: '',
  loadingClass: ''
})

const isVisible = ref(false)
const isLoading = ref(true)
const isError = ref(false)
const observer = ref<IntersectionObserver | null>(null)
const imageRef = ref<HTMLElement | null>(null)

const isLazy = computed(() => props.lazy)

const optimizedSrc = computed(() => {
  // In a real implementation, this would integrate with an image optimization service
  // For now, we'll use the original src with query parameters for optimization hints
  const url = new URL(props.src, window.location.origin)
  
  if (props.width) url.searchParams.set('w', props.width.toString())
  if (props.height) url.searchParams.set('h', props.height.toString())
  if (props.quality) url.searchParams.set('q', props.quality.toString())
  if (props.format) url.searchParams.set('f', props.format)
  
  return url.toString()
})

const placeholderStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.width) style.width = `${props.width}px`
  if (props.height) style.height = `${props.height}px`
  
  // Create aspect ratio if both dimensions provided
  if (props.width && props.height) {
    style.aspectRatio = `${props.width} / ${props.height}`
  }
  
  return style
})

const onLoad = () => {
  isLoading.value = false
  isError.value = false
}

const onError = () => {
  isLoading.value = false
  isError.value = true
}

const setupIntersectionObserver = () => {
  if (!props.lazy || typeof window === 'undefined') {
    isVisible.value = true
    return
  }

  observer.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isVisible.value = true
          observer.value?.disconnect()
        }
      })
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.1
    }
  )

  if (imageRef.value) {
    observer.value.observe(imageRef.value)
  }
}

onMounted(() => {
  setupIntersectionObserver()
})

onUnmounted(() => {
  observer.value?.disconnect()
})
</script>

<style scoped>
.optimized-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  min-height: 200px;
  min-width: 200px;
}

.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #1a1a1a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
  
  .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .loading-spinner {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>
